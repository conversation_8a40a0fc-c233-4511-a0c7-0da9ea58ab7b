# Enhanced AI Podcast Studio - API Specifications

## 🔧 Backend API Requirements

This document outlines all the new API endpoints required to support the enhanced UI features and advanced TTS capabilities.

## 📋 Core API Endpoints

### 1. TTS Provider Management

#### GET `/api/tts/providers`
**Description**: Get available TTS providers with their capabilities and status

**Response**:
```json
{
  "success": true,
  "providers": [
    {
      "id": "enhanced_elevenlabs",
      "name": "Enhanced ElevenLabs",
      "description": "Advanced TTS with emotion tags and audio events",
      "available": true,
      "status": "online",
      "model": "v3",
      "fallback_model": "v2",
      "features": [
        {
          "name": "Emotion Tags",
          "enhanced": true,
          "icon": "fas fa-theater-masks",
          "description": "Support for [excited], [calm], [whispering], etc."
        },
        {
          "name": "Audio Events",
          "enhanced": true,
          "icon": "fas fa-music",
          "description": "Support for {applause}, {music}, {footsteps}, etc."
        },
        {
          "name": "Text-to-Dialogue",
          "enhanced": true,
          "icon": "fas fa-comments",
          "description": "Advanced dialogue synthesis"
        },
        {
          "name": "Voice Cloning",
          "enhanced": false,
          "icon": "fas fa-clone",
          "description": "Custom voice creation"
        }
      ],
      "supported_languages": ["en", "zh", "es", "fr"],
      "max_characters": 5000,
      "pricing_tier": "premium"
    },
    {
      "id": "minimax",
      "name": "MiniMax TTS",
      "description": "Standard TTS with pause optimization",
      "available": true,
      "status": "online",
      "model": "standard",
      "features": [
        {
          "name": "Pause Optimization",
          "enhanced": false,
          "icon": "fas fa-pause",
          "description": "Optimized pause markers ≤0.3s"
        },
        {
          "name": "Voice Selection",
          "enhanced": false,
          "icon": "fas fa-microphone",
          "description": "Multiple voice options"
        }
      ],
      "supported_languages": ["zh", "en"],
      "max_characters": 3000,
      "pricing_tier": "standard"
    }
  ]
}
```

#### GET `/api/tts/voices?provider={provider_id}`
**Description**: Get available voices for a specific provider

**Parameters**:
- `provider`: TTS provider ID

**Response**:
```json
{
  "success": true,
  "provider": "enhanced_elevenlabs",
  "voices": [
    {
      "id": "voice_001",
      "name": "Sarah",
      "description": "Professional female voice, clear and articulate",
      "gender": "female",
      "age_range": "adult",
      "accent": "american",
      "style": "professional",
      "sample_url": "/static/samples/sarah_sample.mp3",
      "supported_features": ["emotion_tags", "audio_events"],
      "language": "en",
      "premium": false
    },
    {
      "id": "voice_002",
      "name": "Marcus",
      "description": "Warm male voice with natural intonation",
      "gender": "male",
      "age_range": "adult",
      "accent": "british",
      "style": "conversational",
      "sample_url": "/static/samples/marcus_sample.mp3",
      "supported_features": ["emotion_tags", "audio_events", "dialogue_mode"],
      "language": "en",
      "premium": true
    }
  ]
}
```

#### POST `/api/tts/preview`
**Description**: Generate voice preview

**Request Body**:
```json
{
  "voice_id": "voice_001",
  "provider": "enhanced_elevenlabs",
  "text": "Hello, this is a preview of my voice. How do I sound?",
  "enhanced_features": {
    "emotion_tags": true,
    "audio_events": false
  }
}
```

**Response**:
```json
{
  "success": true,
  "audio_url": "/static/previews/preview_abc123.mp3",
  "duration": 3.2,
  "file_size": "156KB",
  "model_used": "v3"
}
```

### 2. Enhanced Script Generation

#### POST `/api/generate_script`
**Description**: Generate podcast script with advanced options

**Request Body**:
```json
{
  "topic": "Artificial Intelligence in Healthcare",
  "language": "en",
  "duration": 600,
  "style": "conversational",
  "speakers": 2,
  "provider": "enhanced_elevenlabs",
  "advanced_options": {
    "target_audience": "professional",
    "complexity": "advanced",
    "mood": "serious",
    "pacing": "normal",
    "background_music": {
      "enabled": true,
      "style": "ambient"
    },
    "custom_instructions": "Include recent research examples and maintain technical accuracy"
  }
}
```

**Response**:
```json
{
  "success": true,
  "script": "Host: [excited] Welcome to our show about AI in healthcare! <#0.3#>\nGuest: Thank you for having me! {applause} <#0.5#>\nHost: [calm] Let's start with the basics...",
  "metadata": {
    "word_count": 847,
    "estimated_duration": "5:23",
    "complexity_score": 7.5,
    "readability_score": 8.2,
    "emotion_tags_count": 12,
    "audio_events_count": 5,
    "pause_markers_count": 23
  },
  "suggestions": [
    "Consider adding more technical examples",
    "The pacing could be slightly faster for professional audience"
  ]
}
```

### 3. Enhanced Audio Generation

#### POST `/api/generate_audio`
**Description**: Generate audio with enhanced TTS features

**Request Body**:
```json
{
  "script": "Enhanced script with emotion tags and audio events",
  "voices": {
    "speaker1": "voice_001",
    "speaker2": "voice_002"
  },
  "provider": "enhanced_elevenlabs",
  "enhanced_features": {
    "emotion_tags": true,
    "audio_events": true,
    "dialogue_mode": true,
    "background_music": {
      "enabled": true,
      "style": "ambient",
      "volume": 0.2
    }
  },
  "quality_settings": {
    "sample_rate": 44100,
    "bit_depth": 16,
    "format": "mp3",
    "quality": "high"
  }
}
```

**Response**:
```json
{
  "success": true,
  "audio_url": "/static/generated/podcast_abc123.mp3",
  "summary": {
    "duration": "5:23",
    "words": 847,
    "speakers": 2,
    "provider": "Enhanced ElevenLabs",
    "model": "v3",
    "enhanced_features": "Emotion Tags, Audio Events, Dialogue Mode",
    "processing_time": "2m 15s",
    "file_size": "12.3 MB",
    "quality_score": 9.2
  },
  "quality_metrics": {
    "audio_quality": "High",
    "ai_naturalness": 95,
    "processing_speed": "Fast",
    "enhancement_score": 8,
    "emotion_accuracy": 92,
    "audio_event_quality": 88
  },
  "timeline_markers": [
    {
      "time": 15.2,
      "type": "emotion",
      "tag": "excited",
      "speaker": "host"
    },
    {
      "time": 32.8,
      "type": "audio_event",
      "tag": "applause",
      "duration": 2.1
    }
  ]
}
```

#### POST `/api/regenerate_audio`
**Description**: Regenerate audio with same settings

**Request Body**: Same as `/api/generate_audio`

**Response**: Same as `/api/generate_audio` with additional regeneration metadata

### 4. Configuration Management

#### POST `/api/save_config`
**Description**: Save user configuration

**Request Body**:
```json
{
  "name": "My Podcast Config",
  "topic": "Technology Trends",
  "language": "en",
  "duration": 600,
  "style": "conversational",
  "speakers": 2,
  "target_audience": "professional",
  "complexity": "advanced",
  "mood": "neutral",
  "pacing": "normal",
  "enable_music": true,
  "music_style": "ambient",
  "custom_instructions": "Focus on practical applications",
  "provider": "enhanced_elevenlabs"
}
```

**Response**:
```json
{
  "success": true,
  "config_id": "config_abc123",
  "message": "Configuration saved successfully"
}
```

#### GET `/api/configs`
**Description**: Get saved configurations

**Response**:
```json
{
  "success": true,
  "configs": [
    {
      "id": "config_abc123",
      "name": "My Podcast Config",
      "created_at": "2024-01-15T10:30:00Z",
      "last_used": "2024-01-16T14:20:00Z",
      "settings": { /* configuration object */ }
    }
  ]
}
```

### 5. Audio Processing

#### POST `/api/convert_audio`
**Description**: Convert audio to different formats

**Request Body**:
```json
{
  "audio_url": "/static/generated/podcast_abc123.mp3",
  "format": "wav",
  "quality": "high"
}
```

**Response**:
```json
{
  "success": true,
  "download_url": "/static/converted/podcast_abc123.wav",
  "file_size": "45.7 MB",
  "processing_time": "15s"
}
```

### 6. Collaboration Features

#### POST `/api/invite_collaborator`
**Description**: Invite collaborator to project

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "permission": "edit",
  "message": "Let's collaborate on this podcast project",
  "project_id": "project_abc123"
}
```

**Response**:
```json
{
  "success": true,
  "invitation_id": "invite_xyz789",
  "message": "Invitation sent successfully"
}
```

#### GET `/api/collaborators?project_id={project_id}`
**Description**: Get project collaborators

**Response**:
```json
{
  "success": true,
  "collaborators": [
    {
      "id": "user_001",
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "Owner",
      "status": "online",
      "avatar": "/static/avatars/john.jpg",
      "last_active": "2024-01-16T15:30:00Z",
      "permissions": ["read", "write", "admin"]
    },
    {
      "id": "user_002",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "role": "Editor",
      "status": "away",
      "avatar": "/static/avatars/jane.jpg",
      "last_active": "2024-01-16T14:45:00Z",
      "permissions": ["read", "write"]
    }
  ]
}
```

### 7. Analytics and Metrics

#### GET `/api/usage_analytics`
**Description**: Get usage analytics

**Response**:
```json
{
  "success": true,
  "analytics": {
    "total_podcasts": 156,
    "total_duration": "12h 34m",
    "favorite_provider": "enhanced_elevenlabs",
    "most_used_features": ["emotion_tags", "audio_events"],
    "average_quality_score": 8.7,
    "monthly_usage": {
      "podcasts_created": 23,
      "minutes_generated": 187,
      "enhancement_usage": 89
    }
  }
}
```

## 🔒 Authentication & Security

All API endpoints should include:
- JWT token authentication
- Rate limiting (100 requests/minute per user)
- Input validation and sanitization
- CORS headers for web client
- Request logging for debugging

## 📊 Error Handling

Standard error response format:
```json
{
  "success": false,
  "error": "Error message",
  "error_code": "PROVIDER_UNAVAILABLE",
  "details": {
    "provider": "enhanced_elevenlabs",
    "fallback_available": true
  }
}
```

## 🚀 Performance Requirements

- TTS provider status check: < 500ms
- Voice preview generation: < 3 seconds
- Script generation: < 30 seconds
- Audio generation: < 2 minutes for 10-minute podcast
- File conversion: < 30 seconds for typical podcast length

This API specification provides the foundation for implementing all enhanced features while maintaining backward compatibility with existing functionality.
