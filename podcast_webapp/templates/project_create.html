<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create New Project - AI Podcast Studio</title>
    <link rel="stylesheet" href="{{ url_for('static', path='css/commercial.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="project-create-page">
    <!-- Main Navigation Header -->
    <header class="main-header">
        <div class="header-container">
            <!-- Logo and Brand -->
            <div class="header-brand">
                <div class="brand-logo">
                    <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                        <path d="M12 19v4"/>
                        <path d="M8 23h8"/>
                    </svg>
                    <span class="brand-text">AI Podcast Studio</span>
                    <span class="brand-badge">Pro</span>
                </div>
            </div>

            <!-- Primary Navigation -->
            <nav class="primary-nav">
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    <span>Dashboard</span>
                </a>
                <a href="/projects" class="nav-link active">
                    <i class="fas fa-folder"></i>
                    <span>Projects</span>
                </a>
                <a href="/library" class="nav-link">
                    <i class="fas fa-book"></i>
                    <span>Library</span>
                </a>
                <a href="/analytics" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </nav>

            <!-- Header Actions -->
            <div class="header-actions">
                <!-- Search -->
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search projects...">
                    <kbd class="search-shortcut">⌘K</kbd>
                </div>

                <!-- Notifications -->
                <button class="action-button notifications-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>

                <!-- User Menu -->
                <div class="user-menu">
                    <button class="user-avatar-btn">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" 
                             alt="User Avatar" class="user-avatar">
                        <span class="user-name">John Doe</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-container">
            <!-- Breadcrumb Navigation -->
            <nav class="breadcrumb">
                <a href="/dashboard" class="breadcrumb-item">Dashboard</a>
                <i class="fas fa-chevron-right breadcrumb-separator"></i>
                <a href="/projects" class="breadcrumb-item">Projects</a>
                <i class="fas fa-chevron-right breadcrumb-separator"></i>
                <span class="breadcrumb-item current">Create New Project</span>
            </nav>

            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title-section">
                    <h1 class="page-title">Create New Project</h1>
                    <p class="page-subtitle">Set up your podcast project with AI-powered content generation and advanced TTS features.</p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-secondary" onclick="history.back()">
                        <i class="fas fa-arrow-left"></i>
                        <span>Back</span>
                    </button>
                </div>
            </div>

            <!-- Project Creation Wizard -->
            <div class="creation-wizard">
                <!-- Progress Steps -->
                <div class="wizard-progress">
                    <div class="progress-step active" data-step="1">
                        <div class="step-number">1</div>
                        <div class="step-info">
                            <div class="step-title">Project Setup</div>
                            <div class="step-description">Basic information and template</div>
                        </div>
                    </div>
                    <div class="progress-connector"></div>
                    <div class="progress-step" data-step="2">
                        <div class="step-number">2</div>
                        <div class="step-info">
                            <div class="step-title">Content Configuration</div>
                            <div class="step-description">Topic, style, and advanced settings</div>
                        </div>
                    </div>
                    <div class="progress-connector"></div>
                    <div class="progress-step" data-step="3">
                        <div class="step-number">3</div>
                        <div class="step-info">
                            <div class="step-title">TTS Provider</div>
                            <div class="step-description">Voice technology selection</div>
                        </div>
                    </div>
                </div>

                <!-- Step 1: Project Setup -->
                <div class="wizard-step active" id="step1">
                    <div class="step-content">
                        <div class="content-grid">
                            <!-- Template Selection -->
                            <div class="content-section">
                                <h3 class="section-title">Choose a Template</h3>
                                <p class="section-description">Start with a pre-configured template or create from scratch</p>
                                
                                <div class="template-grid">
                                    <div class="template-card" data-template="blank">
                                        <div class="template-icon">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div class="template-content">
                                            <h4 class="template-title">Blank Project</h4>
                                            <p class="template-description">Start from scratch with full customization</p>
                                            <div class="template-features">
                                                <span class="feature-tag">Custom</span>
                                                <span class="feature-tag">Flexible</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="template-card selected" data-template="interview">
                                        <div class="template-icon">
                                            <i class="fas fa-comments"></i>
                                        </div>
                                        <div class="template-content">
                                            <h4 class="template-title">Interview Style</h4>
                                            <p class="template-description">Professional interview format with Q&A structure</p>
                                            <div class="template-features">
                                                <span class="feature-tag">2 Speakers</span>
                                                <span class="feature-tag">10-15 min</span>
                                                <span class="feature-tag">Professional</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="template-card" data-template="educational">
                                        <div class="template-icon">
                                            <i class="fas fa-graduation-cap"></i>
                                        </div>
                                        <div class="template-content">
                                            <h4 class="template-title">Educational Content</h4>
                                            <p class="template-description">Informative content with clear explanations</p>
                                            <div class="template-features">
                                                <span class="feature-tag">1 Speaker</span>
                                                <span class="feature-tag">15-20 min</span>
                                                <span class="feature-tag">Academic</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="template-card" data-template="news">
                                        <div class="template-icon">
                                            <i class="fas fa-newspaper"></i>
                                        </div>
                                        <div class="template-content">
                                            <h4 class="template-title">News & Analysis</h4>
                                            <p class="template-description">Current events with expert commentary</p>
                                            <div class="template-features">
                                                <span class="feature-tag">2-3 Speakers</span>
                                                <span class="feature-tag">5-10 min</span>
                                                <span class="feature-tag">Formal</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="template-card" data-template="storytelling">
                                        <div class="template-icon">
                                            <i class="fas fa-book-open"></i>
                                        </div>
                                        <div class="template-content">
                                            <h4 class="template-title">Storytelling</h4>
                                            <p class="template-description">Narrative-driven content with dramatic elements</p>
                                            <div class="template-features">
                                                <span class="feature-tag">Multiple Speakers</span>
                                                <span class="feature-tag">20+ min</span>
                                                <span class="feature-tag">Creative</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="template-card" data-template="business">
                                        <div class="template-icon">
                                            <i class="fas fa-briefcase"></i>
                                        </div>
                                        <div class="template-content">
                                            <h4 class="template-title">Business Discussion</h4>
                                            <p class="template-description">Corporate content and industry insights</p>
                                            <div class="template-features">
                                                <span class="feature-tag">2-4 Speakers</span>
                                                <span class="feature-tag">15-25 min</span>
                                                <span class="feature-tag">Corporate</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Project Information -->
                            <div class="content-section">
                                <h3 class="section-title">Project Information</h3>
                                <p class="section-description">Basic details about your podcast project</p>
                                
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="projectName" class="form-label">Project Name</label>
                                        <input type="text" id="projectName" class="form-input" 
                                               placeholder="Enter project name..." required>
                                        <div class="form-hint">This will be used as the default title for your podcast</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="projectDescription" class="form-label">Description (Optional)</label>
                                        <textarea id="projectDescription" class="form-textarea" rows="3"
                                                  placeholder="Brief description of your podcast project..."></textarea>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="projectLanguage" class="form-label">Language</label>
                                            <select id="projectLanguage" class="form-select">
                                                <option value="en">English</option>
                                                <option value="zh">中文 (Chinese)</option>
                                                <option value="es">Español (Spanish)</option>
                                                <option value="fr">Français (French)</option>
                                                <option value="de">Deutsch (German)</option>
                                                <option value="ja">日本語 (Japanese)</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="projectCategory" class="form-label">Category</label>
                                            <select id="projectCategory" class="form-select">
                                                <option value="technology">Technology</option>
                                                <option value="business">Business</option>
                                                <option value="education">Education</option>
                                                <option value="entertainment">Entertainment</option>
                                                <option value="news">News & Politics</option>
                                                <option value="health">Health & Wellness</option>
                                                <option value="science">Science</option>
                                                <option value="arts">Arts & Culture</option>
                                                <option value="sports">Sports</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="form-label">Project Settings</label>
                                        <div class="checkbox-group">
                                            <label class="checkbox-item">
                                                <input type="checkbox" id="enableCollaboration" checked>
                                                <span class="checkbox-mark"></span>
                                                <span class="checkbox-label">Enable team collaboration</span>
                                            </label>
                                            <label class="checkbox-item">
                                                <input type="checkbox" id="autoSave" checked>
                                                <span class="checkbox-mark"></span>
                                                <span class="checkbox-label">Auto-save changes</span>
                                            </label>
                                            <label class="checkbox-item">
                                                <input type="checkbox" id="versionHistory">
                                                <span class="checkbox-mark"></span>
                                                <span class="checkbox-label">Track version history</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wizard Navigation -->
                <div class="wizard-navigation">
                    <div class="nav-left">
                        <button class="btn btn-secondary" id="prevBtn" style="display: none;">
                            <i class="fas fa-arrow-left"></i>
                            <span>Previous</span>
                        </button>
                    </div>
                    <div class="nav-right">
                        <button class="btn btn-secondary" onclick="location.href='/projects'">
                            <span>Cancel</span>
                        </button>
                        <button class="btn btn-primary" id="nextBtn">
                            <span>Next: Content Configuration</span>
                            <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="{{ url_for('static', path='js/commercial.js') }}"></script>
    <script src="{{ url_for('static', path='js/project-create.js') }}"></script>
</body>
</html>
