<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Podcast Generator</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🎙️ AI Podcast Generator</h1>
            <p>Transform any topic into an engaging podcast conversation</p>
            {% if demo_mode %}
            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 4px; margin-top: 10px;">
                <small>🔧 Demo Mode: API keys not configured. Using sample data for demonstration.</small>
            </div>
            {% endif %}
        </header>

        <main>
            <!-- Input Section -->
            <section id="input-section" class="card">
                <h2>Enter Your Topic</h2>
                <form id="podcast-form">
                    <div class="form-group">
                        <input type="text" id="topic-input" name="topic" 
                               placeholder="e.g., The Future of Renewable Energy" 
                               required maxlength="500">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="script-style">Script Style</label>
                            <select id="script-style" name="script_style">
                                <option value="conversational">Conversational</option>
                                <option value="educational">Educational</option>
                                <option value="interview">Interview</option>
                                <option value="debate">Debate</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="num-speakers">Number of Speakers</label>
                            <select id="num-speakers" name="num_speakers">
                                <option value="2">2 Speakers</option>
                                <option value="3">3 Speakers</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="duration">Target Duration</label>
                            <select id="duration" name="duration_target">
                                <option value="180">3 minutes</option>
                                <option value="300" selected>5 minutes</option>
                                <option value="600">10 minutes</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="language">Language</label>
                            <select id="language" name="language">
                                <option value="en">English</option>
                                <option value="zh">中文 (Chinese)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="tts-provider">TTS Engine</label>
                            <select id="tts-provider" name="tts_provider">
                                <option value="minimax" selected>MiniMax TTS (Default)</option>
                                <option value="elevenlabs">ElevenLabs TTS (V2)</option>
                                <option value="elevenlabs_v3">ElevenLabs V3 (Latest) ⭐</option>
                                <option value="auto">Smart Selection</option>
                            </select>
                            <small class="form-help" id="tts-help">Loading TTS information...</small>
                        </div>
                    </div>
                    
                    <button type="submit" id="generate-btn" class="btn btn-primary">
                        Generate Podcast
                    </button>
                </form>
            </section>

            <!-- Progress Section -->
            <section id="progress-section" class="card hidden">
                <h2>Generation Progress</h2>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <p class="progress-text">Starting generation...</p>
                </div>
                <div class="progress-steps">
                    <div class="step" data-step="searching">🔍 Searching web</div>
                    <div class="step" data-step="generating_report">📄 Generating report</div>
                    <div class="step" data-step="generating_script">✍️ Writing script</div>
                    <div class="step" data-step="script_ready">👀 Ready for review</div>
                    <div class="step" data-step="synthesizing_audio">🎙️ Synthesizing audio</div>
                </div>
                <div id="tts-status" class="tts-status hidden">
                    <p><strong>TTS Engine:</strong> <span id="current-tts-provider">-</span></p>
                    <p><strong>Status:</strong> <span id="tts-generation-status">-</span></p>
                </div>
            </section>

            <!-- Research Report Section -->
            <section id="report-section" class="card hidden">
                <h2>Research Report</h2>
                <div id="report-content" class="content-box">
                    <!-- Report will be inserted here -->
                </div>
            </section>

            <!-- Podcast Script Section -->
            <section id="script-section" class="card hidden">
                <h2>Podcast Script</h2>
                <div class="script-header">
                    <h3 id="script-title"></h3>
                    <p id="script-description"></p>
                </div>
                <div id="script-dialogue" class="dialogue-container">
                    <!-- Dialogue lines will be inserted here -->
                </div>
                <button id="edit-script-btn" class="btn btn-secondary">Edit Script</button>
            </section>

            <!-- Voice Selection & Review Section -->
            <section id="review-section" class="card hidden">
                <h2>🎭 Review & Voice Selection</h2>
                <p>Please review the podcast script and select voices for each speaker:</p>
                
                <div id="voice-selection-container">
                    <!-- Voice selection for each speaker will be populated here -->
                </div>
                
                <div class="review-actions">
                    <button id="approve-script-btn" class="btn btn-primary">
                        ✅ Approve & Generate Audio
                    </button>
                    <button id="edit-before-approve-btn" class="btn btn-secondary">
                        ✏️ Edit Script First
                    </button>
                </div>
            </section>

            <!-- Script Editor Modal -->
            <div id="script-editor-modal" class="modal hidden">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h2>Edit Podcast Script</h2>
                    <textarea id="script-editor" rows="20"></textarea>
                    <div class="modal-actions">
                        <button id="save-script-btn" class="btn btn-primary">Save & Re-generate Audio</button>
                        <button id="cancel-edit-btn" class="btn btn-secondary">Cancel</button>
                    </div>
                </div>
            </div>



            <!-- Audio Player Section -->
            <section id="audio-section" class="card hidden">
                <h2>🎧 Your Podcast</h2>
                <div class="audio-player">
                    <audio id="podcast-audio" controls>
                        Your browser does not support the audio element.
                    </audio>
                </div>
                <div class="audio-actions">
                    <a id="download-audio-btn" class="btn btn-primary" download>
                        Download Podcast
                    </a>
                </div>
            </section>

            <!-- Error Section -->
            <section id="error-section" class="card error hidden">
                <h2>❌ Error</h2>
                <p id="error-message"></p>
                <button id="retry-btn" class="btn btn-primary">Try Again</button>
            </section>
        </main>

        <footer>
            <p>Powered by MiniMax TTS & OpenRouter AI</p>
        </footer>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html>