<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Podcast AI - Generate Beautiful Podcasts by Talking to AI</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
    <!-- Readdy-style Header -->
    <header class="readdy-header">
        <div class="header-container">
            <div class="brand-section">
                <div class="brand-logo">
                    <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z" fill="currentColor"/>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path d="M12 19v4M8 23h8" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <span class="brand-text">Podcast AI</span>
            </div>
            
            <nav class="nav-links">
                <a href="#home" class="nav-link">Home</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#pricing" class="nav-link">Pricing</a>
                <a href="#blog" class="nav-link">Blog</a>
                <a href="#login" class="nav-link">Login</a>
            </nav>
            
            <button class="cta-button">Get Started</button>
        </div>
    </header>

    <!-- Main Container -->
    <div class="app-container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Transform Ideas into Engaging Podcasts</h1>
                <p class="hero-subtitle">Powered by advanced AI, create professional multi-voice podcasts in minutes</p>
                <div class="hero-features">
                    <span class="feature-tag">🤖 AI-Powered</span>
                    <span class="feature-tag">🌍 Multi-Language</span>
                    <span class="feature-tag">🎭 Multiple Voices</span>
                    <span class="feature-tag">⚡ Fast Generation</span>
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Hero Section -->
            <section id="home" class="hero-section">
                <div class="hero-container">
                    <div class="hero-content">
                        <h1 class="hero-title">Build Your Dream Podcast by Talking to Our AI</h1>
                        <p class="hero-subtitle">No production or hosting skills needed — generate professional podcast conversations with multiple voices in minutes.</p>
                        
                        <div class="hero-cta">
                            <button class="btn-primary" id="try-free-btn">Try for Free</button>
                            <button class="btn-outline" id="watch-demo-btn">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="5 3 19 12 5 21 5 3"/>
                                </svg>
                                Watch Demo
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- How It Works Section -->
            <section class="how-it-works">
                <div class="section-container">
                    <h2 class="section-title">How It Works</h2>
                    <div class="steps-grid">
                        <div class="step-card">
                            <div class="step-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                            </div>
                            <h3 class="step-title">Describe your idea</h3>
                            <p class="step-description">Tell us what you need — educational content, interviews, debates, or casual conversations...</p>
                        </div>
                        
                        <div class="step-card">
                            <div class="step-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
                                </svg>
                            </div>
                            <h3 class="step-title">Refine via chat</h3>
                            <p class="step-description">Make tweaks — voices, style, duration — as easily as messaging a friend.</p>
                        </div>
                        
                        <div class="step-card">
                            <div class="step-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="16 18 22 12 16 6"/>
                                    <polyline points="8 6 2 12 8 18"/>
                                </svg>
                            </div>
                            <h3 class="step-title">Export audio</h3>
                            <p class="step-description">Download high-quality MP3 files or stream directly to your podcast platform.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Design Preview Section -->
            <section class="design-preview">
                <div class="section-container">
                    <h2 class="section-title">See It In Action</h2>
                    <div class="preview-tabs">
                        <button class="tab-btn active" data-tab="desktop">Desktop</button>
                        <button class="tab-btn" data-tab="mobile">Mobile</button>
                    </div>
                    
                    <div class="preview-container">
                        <div class="preview-mockup">
                            <div class="mockup-screen">
                                <div class="generator-form-preview">
                                    <form id="podcast-form" class="generator-form">
                                        <div class="form-group">
                                            <label for="topic-input" class="form-label">What should we discuss?</label>
                                            <input type="text" 
                                                   id="topic-input" 
                                                   name="topic" 
                                                   class="input-field"
                                                   placeholder="Enter any topic you're curious about..."
                                                   required 
                                                   maxlength="500">
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="script-style" class="form-label">Style</label>
                                                <select id="script-style" name="script_style" class="select-field">
                                                    <option value="conversational">Conversational</option>
                                                    <option value="educational">Educational</option>
                                                    <option value="interview">Interview</option>
                                                    <option value="debate">Debate</option>
                                                </select>
                                            </div>

                                            <div class="form-group">
                                                <label for="num-speakers" class="form-label">Speakers</label>
                                                <select id="num-speakers" name="num_speakers" class="select-field">
                                                    <option value="2">2 Speakers</option>
                                                    <option value="3">3 Speakers</option>
                                                </select>
                                            </div>

                                            <div class="form-group">
                                                <label for="duration" class="form-label">Duration</label>
                                                <select id="duration" name="duration_target" class="select-field">
                                                    <option value="180">3 minutes</option>
                                                    <option value="300" selected>5 minutes</option>
                                                    <option value="600">10 minutes</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="language" class="form-label">Language</label>
                                                <select id="language" name="language" class="select-field">
                                                    <option value="en">English</option>
                                                    <option value="zh">Chinese</option>
                                                </select>
                                            </div>

                                            <div class="form-group">
                                                <label for="tts-provider" class="form-label">Voice Engine</label>
                                                <select id="tts-provider" name="tts_provider" class="select-field">
                                                    <option value="auto" selected>Smart Selection</option>
                                                    <option value="elevenlabs_v3">ElevenLabs V3</option>
                                                    <option value="elevenlabs">ElevenLabs V2</option>
                                                    <option value="minimax">MiniMax TTS</option>
                                                </select>
                                            </div>
                                        </div>

                                        <button type="submit" id="generate-btn" class="generate-btn">
                                            <span class="btn-text">Generate Podcast</span>
                                            <span class="btn-loading hidden">
                                                <div class="spinner"></div>
                                                Generating...
                                            </span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="preview-caption">Interaction in real-time — refresh on the fly.</div>
                    </div>
                </div>
            </section>

            <!-- Features Grid -->
            <section id="features" class="features-section">
                <div class="section-container">
                    <h2 class="section-title">Features</h2>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                                </svg>
                            </div>
                            <h3 class="feature-title">Natural-language prompts</h3>
                            <p class="feature-description">AI understands context and intent from simple descriptions.</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 2a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"/>
                                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                                </svg>
                            </div>
                            <h3 class="feature-title">Professional-quality voices</h3>
                            <p class="feature-description">Multiple AI voice engines for natural conversations.</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                                    <polyline points="7.5 4.21 12 6.81 16.5 4.21"/>
                                    <polyline points="7.5 19.79 7.5 14.6 3 12"/>
                                    <polyline points="21 12 16.5 14.6 16.5 19.79"/>
                                    <polyline points="12 22.81 12 17"/>
                                </svg>
                            </div>
                            <h3 class="feature-title">Export to audio & platforms</h3>
                            <p class="feature-description">MP3 files or direct integration with podcast platforms.</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14 2 14 8 20 8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10 9 9 9 8 9"/>
                                </svg>
                            </div>
                            <h3 class="feature-title">One episode at a time</h3>
                            <p class="feature-description">Focused, high-quality output with attention to detail.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Status Section -->
            <section class="status-section">
                <div class="section-container">
                    <div class="status-container">
                        <div class="status-item">
                            <div class="status-label">Status</div>
                            <div class="status-value" id="status-value">Ready</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">Progress</div>
                            <div class="status-value" id="progress-value">0%</div>
                        </div>
                        <div class="status-item">
                            <div class="status-label">Time</div>
                            <div class="status-value" id="time-value">--</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Dashboard Metrics Panel -->
            <section id="metrics-panel" class="metrics-dashboard hidden">
                <div class="metrics-header">
                    <h3 class="metrics-title">
                        <span class="metrics-icon">📊</span>
                        Session Analytics
                    </h3>
                    <div class="metrics-controls">
                        <button id="metrics-refresh" class="metrics-btn">🔄 Refresh</button>
                        <button id="metrics-export" class="metrics-btn">📊 Export</button>
                    </div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <div class="metric-icon">⚡</div>
                            <h4 class="metric-title">Generation Speed</h4>
                        </div>
                        <div class="metric-value-container">
                            <div id="generation-speed" class="metric-value">--</div>
                            <div class="metric-unit">seconds</div>
                        </div>
                        <div class="metric-trend">
                            <span class="trend-indicator">📈</span>
                            <span class="trend-text">Optimizing...</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <div class="metric-icon">🎙️</div>
                            <h4 class="metric-title">Audio Quality</h4>
                        </div>
                        <div class="metric-value-container">
                            <div id="audio-quality" class="metric-value">High</div>
                            <div class="metric-unit">44.1kHz</div>
                        </div>
                        <div class="metric-trend">
                            <span class="trend-indicator">✅</span>
                            <span class="trend-text">Excellent</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <div class="metric-icon">🌐</div>
                            <h4 class="metric-title">Research Sources</h4>
                        </div>
                        <div class="metric-value-container">
                            <div id="research-sources" class="metric-value">0</div>
                            <div class="metric-unit">sources</div>
                        </div>
                        <div class="metric-trend">
                            <span class="trend-indicator">🔍</span>
                            <span class="trend-text">Searching...</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <div class="metric-icon">📝</div>
                            <h4 class="metric-title">Script Length</h4>
                        </div>
                        <div class="metric-value-container">
                            <div id="script-length" class="metric-value">0</div>
                            <div class="metric-unit">words</div>
                        </div>
                        <div class="metric-trend">
                            <span class="trend-indicator">✍️</span>
                            <span class="trend-text">Generating...</span>
                        </div>
                    </div>
                </div>
                
                <div class="status-timeline-detailed">
                    <div class="timeline-detailed-header">
                        <h4>Detailed Timeline</h4>
                        <div class="timeline-legend">
                            <span class="legend-item pending">⏳ Pending</span>
                            <span class="legend-item active">🔄 Active</span>
                            <span class="legend-item completed">✅ Completed</span>
                            <span class="legend-item error">❌ Error</span>
                        </div>
                    </div>
                    <div class="timeline-events">
                        <div class="timeline-event" data-status="completed">
                            <div class="event-time">00:00</div>
                            <div class="event-icon">🚀</div>
                            <div class="event-content">
                                <div class="event-title">Session Started</div>
                                <div class="event-description">Podcast generation workflow initiated</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2: Generation Progress -->
            <section id="progress-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">⚡</span>
                        Generating Your Podcast
                    </h2>
                    <p class="card-subtitle">Please wait while we create your professional podcast script</p>
                </div>

                <div class="progress-container">
                    <div class="progress-track">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-percentage">0%</div>
                </div>

                <div class="progress-stages">
                    <div class="stage" data-stage="searching">
                        <div class="stage-icon">🔍</div>
                        <div class="stage-content">
                            <div class="stage-title">Web Research</div>
                            <div class="stage-description">Gathering latest information</div>
                        </div>
                        <div class="stage-status"></div>
                    </div>

                    <div class="stage" data-stage="generating_report">
                        <div class="stage-icon">📊</div>
                        <div class="stage-content">
                            <div class="stage-title">Analysis</div>
                            <div class="stage-description">Creating research report</div>
                        </div>
                        <div class="stage-status"></div>
                    </div>

                    <div class="stage" data-stage="generating_script">
                        <div class="stage-icon">✍️</div>
                        <div class="stage-content">
                            <div class="stage-title">Script Writing</div>
                            <div class="stage-description">Crafting engaging dialogue</div>
                        </div>
                        <div class="stage-status"></div>
                    </div>

                    <div class="stage" data-stage="script_ready">
                        <div class="stage-icon">✅</div>
                        <div class="stage-content">
                            <div class="stage-title">Ready</div>
                            <div class="stage-description">Script completed</div>
                        </div>
                        <div class="stage-status"></div>
                    </div>
                </div>

                <div id="tts-status" class="tts-info-panel hidden">
                    <div class="tts-info-header">
                        <h4>🎙️ Voice Synthesis Status</h4>
                    </div>
                    <div class="tts-info-content">
                        <div class="info-row">
                            <span class="info-label">Engine:</span>
                            <span id="current-tts-provider" class="info-value">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Status:</span>
                            <span id="tts-generation-status" class="info-value">-</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2.5: Research Report -->
            <section id="report-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">📋</span>
                        Research Report
                    </h2>
                    <p class="card-subtitle">AI-generated research summary for your podcast topic</p>
                </div>
                <div id="report-content" class="report-content">
                    <!-- Report will be inserted here -->
                </div>
            </section>

            <!-- Step 3: Script Review -->
            <section id="script-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">📄</span>
                        Podcast Script
                    </h2>
                    <div class="script-meta">
                        <h3 id="script-title" class="script-title"></h3>
                        <p id="script-description" class="script-description"></p>
                    </div>
                </div>

                <div class="script-container">
                    <div id="script-dialogue" class="dialogue-view">
                        <!-- Dialogue lines will be inserted here -->
                    </div>
                </div>

                <div class="script-actions">
                    <button id="edit-script-btn" class="btn btn-outline">
                        <span class="btn-icon">✏️</span>
                        Edit Script
                    </button>
                </div>
            </section>

            <!-- Step 4: Voice Selection -->
            <section id="review-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">🎭</span>
                        Voice Selection
                    </h2>
                    <p class="card-subtitle">Choose the perfect voice for each speaker in your podcast</p>
                </div>

                <div id="voice-selection-container" class="voice-selection-grid">
                    <!-- Voice selection cards will be populated here -->
                </div>

                <div class="approval-panel">
                    <div class="approval-actions">
                        <button id="approve-script-btn" class="btn btn-success btn-large">
                            <span class="btn-icon">🎬</span>
                            <span class="btn-text">Generate Audio</span>
                        </button>
                        <button id="edit-before-approve-btn" class="btn btn-outline">
                            <span class="btn-icon">✏️</span>
                            Edit Script First
                        </button>
                    </div>
                </div>
            </section>

            <!-- Step 5: Final Audio -->
            <section id="audio-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">🎧</span>
                        Your Podcast is Ready!
                    </h2>
                    <p class="card-subtitle">Listen to your AI-generated podcast and download when ready</p>
                </div>

                <div class="audio-player-container">
                    <div class="audio-player-wrapper">
                        <audio id="podcast-audio" controls class="modern-audio-player">
                            Your browser does not support the audio element.
                        </audio>
                    </div>
                    <div class="audio-meta">
                        <div class="audio-info">
                            <span class="audio-duration">Duration: <span id="audio-duration">-</span></span>
                            <span class="audio-format">Format: MP3</span>
                        </div>
                    </div>
                </div>

                <div class="download-section">
                    <a id="download-audio-btn" class="btn btn-primary btn-large" download>
                        <span class="btn-icon">⬇️</span>
                        Download Podcast
                    </a>
                    <button id="create-new-btn" class="btn btn-outline">
                        <span class="btn-icon">🔄</span>
                        Create Another
                    </button>
                </div>
            </section>

            <!-- Error Section -->
            <section id="error-section" class="content-card error-card hidden">
                <div class="error-content">
                    <div class="error-icon">❌</div>
                    <h2 class="error-title">Something went wrong</h2>
                    <p id="error-message" class="error-message"></p>
                    <div class="error-actions">
                        <button id="retry-btn" class="btn btn-primary">
                            <span class="btn-icon">🔄</span>
                            Try Again
                        </button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Script Editor Modal -->
    <div id="script-editor-modal" class="modal-overlay hidden" role="dialog" aria-labelledby="modal-title" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h3 id="modal-title" class="modal-title">Edit Podcast Script</h3>
                <button class="modal-close" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="editor-container">
                    <textarea id="script-editor" class="script-editor" rows="25" placeholder="Edit your podcast script here..." aria-label="Podcast script editor"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button id="save-script-btn" class="btn btn-primary">
                    <span class="btn-icon">💾</span>
                    Save & Update
                </button>
                <button id="cancel-edit-btn" class="btn btn-outline">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="app-footer">
        <div class="footer-content">
            <p class="footer-text">Powered by MiniMax TTS & OpenRouter AI</p>
            <div class="footer-links">
                <span class="footer-version">v2.0</span>
            </div>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>
</body>
</html>