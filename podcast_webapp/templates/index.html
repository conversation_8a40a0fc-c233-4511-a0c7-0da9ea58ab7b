<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PodcastMaster AI</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
    <!-- Top Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-container">
            <div class="nav-left">
                <div class="brand-logo">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
                        <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                        <path d="M12 19v4"/>
                        <path d="M8 23h8"/>
                    </svg>
                    <span class="brand-text">PodcastMaster</span>
                </div>
            </div>
            
            <div class="nav-center">
                <div class="nav-tabs">
                    <button class="nav-tab active" data-tab="create">Create</button>
                    <button class="nav-tab" data-tab="library">Library</button>
                    <button class="nav-tab" data-tab="analytics">Analytics</button>
                </div>
            </div>
            
            <div class="nav-right">
                <div class="nav-actions">
                    <button class="nav-action-btn" title="Settings">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                        </svg>
                    </button>
                    <div class="user-avatar">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="User">
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="app-container">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">Transform Ideas into Engaging Podcasts</h1>
                <p class="hero-subtitle">Powered by advanced AI, create professional multi-voice podcasts in minutes</p>
                <div class="hero-features">
                    <span class="feature-tag">🤖 AI-Powered</span>
                    <span class="feature-tag">🌍 Multi-Language</span>
                    <span class="feature-tag">🎭 Multiple Voices</span>
                    <span class="feature-tag">⚡ Fast Generation</span>
                </div>
            </div>
        </section>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title-section">
                    <h1 class="page-title">Create Podcast</h1>
                    <p class="page-subtitle">Generate professional AI-powered podcasts with multiple voices</p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-outline-small">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                            <circle cx="12" cy="12" r="3"/>
                        </svg>
                        Preview
                    </button>
                    <button class="btn btn-primary-small">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
                        </svg>
                        Save Draft
                    </button>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Main Form Card -->
                <div class="main-card">
                    <div class="card-header-minimal">
                        <h3 class="card-title-minimal">Podcast Configuration</h3>
                        <span class="step-indicator">Step 1 of 3</span>
                    </div>
                    
                    <!-- Form Content -->
                    <div class="card-content-minimal">
                        <form id="podcast-form" class="podcast-form">
                        <!-- Topic Input -->
                        <div class="form-section">
                            <label for="topic-input" class="form-label">
                                Topic <span class="required">*</span>
                            </label>
                            <input type="text" 
                                   id="topic-input" 
                                   name="topic" 
                                   class="form-input"
                                   placeholder="Enter your podcast topic..."
                                   required 
                                   maxlength="500">
                            <p class="form-help">Describe the subject you'd like to explore in your podcast</p>
                        </div>

                        <!-- Configuration Grid -->
                        <div class="config-grid">
                            <div class="config-item">
                                <label for="script-style" class="form-label">Style</label>
                                <select id="script-style" name="script_style" class="form-select">
                                    <option value="conversational">Conversational</option>
                                    <option value="educational">Educational</option>
                                    <option value="interview">Interview</option>
                                    <option value="debate">Debate</option>
                                </select>
                            </div>

                            <div class="config-item">
                                <label for="num-speakers" class="form-label">Speakers</label>
                                <select id="num-speakers" name="num_speakers" class="form-select">
                                    <option value="2">2 Speakers</option>
                                    <option value="3">3 Speakers</option>
                                </select>
                            </div>

                            <div class="config-item">
                                <label for="duration" class="form-label">Duration</label>
                                <select id="duration" name="duration_target" class="form-select">
                                    <option value="180">3 minutes</option>
                                    <option value="300" selected>5 minutes</option>
                                    <option value="600">10 minutes</option>
                                </select>
                            </div>

                            <div class="config-item">
                                <label for="language" class="form-label">Language</label>
                                <select id="language" name="language" class="form-select">
                                    <option value="en">English</option>
                                    <option value="zh">Chinese</option>
                                </select>
                            </div>

                            <div class="config-item">
                                <label for="tts-provider" class="form-label">Voice Engine</label>
                                <select id="tts-provider" name="tts_provider" class="form-select">
                                    <option value="auto" selected>Smart Selection</option>
                                    <option value="elevenlabs_v3">ElevenLabs V3</option>
                                    <option value="elevenlabs">ElevenLabs V2</option>
                                    <option value="minimax">MiniMax TTS</option>
                                </select>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="form-actions-minimal">
                            <button type="button" class="btn btn-outline">Cancel</button>
                            <button type="submit" id="generate-btn" class="btn btn-primary">
                                <span class="btn-text">Generate Podcast</span>
                                <span class="btn-loading hidden">
                                    <span class="spinner"></span>
                                    Generating...
                                </span>
                            </button>
                        </div>
                        </form>
                    </div>
                </div>

                <!-- Sidebar Card -->
                <div class="sidebar-card">
                    <div class="card-header-minimal">
                        <h3 class="card-title-minimal">Generation Progress</h3>
                    </div>
                    <div class="card-content-minimal">
                        <div class="progress-steps">
                            <div class="progress-step active">
                                <div class="step-number">1</div>
                                <div class="step-info">
                                    <div class="step-title">Configure</div>
                                    <div class="step-desc">Set parameters</div>
                                </div>
                            </div>
                            <div class="progress-step">
                                <div class="step-number">2</div>
                                <div class="step-info">
                                    <div class="step-title">Generate</div>
                                    <div class="step-desc">Create script</div>
                                </div>
                            </div>
                            <div class="progress-step">
                                <div class="step-number">3</div>
                                <div class="step-info">
                                    <div class="step-title">Finalize</div>
                                    <div class="step-desc">Audio synthesis</div>
                                </div>
                            </div>
                        </div>

                        <div class="metrics-summary">
                            <div class="metric-item">
                                <div class="metric-label">Total Sessions</div>
                                <div class="metric-value">0</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">Avg. Duration</div>
                                <div class="metric-value">--</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-label">Success Rate</div>
                                <div class="metric-value">--</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Metrics Panel -->
            <section id="metrics-panel" class="metrics-dashboard hidden">
                <div class="metrics-header">
                    <h3 class="metrics-title">
                        <span class="metrics-icon">📊</span>
                        Session Analytics
                    </h3>
                    <div class="metrics-controls">
                        <button id="metrics-refresh" class="metrics-btn">🔄 Refresh</button>
                        <button id="metrics-export" class="metrics-btn">📊 Export</button>
                    </div>
                </div>
                
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <div class="metric-icon">⚡</div>
                            <h4 class="metric-title">Generation Speed</h4>
                        </div>
                        <div class="metric-value-container">
                            <div id="generation-speed" class="metric-value">--</div>
                            <div class="metric-unit">seconds</div>
                        </div>
                        <div class="metric-trend">
                            <span class="trend-indicator">📈</span>
                            <span class="trend-text">Optimizing...</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <div class="metric-icon">🎙️</div>
                            <h4 class="metric-title">Audio Quality</h4>
                        </div>
                        <div class="metric-value-container">
                            <div id="audio-quality" class="metric-value">High</div>
                            <div class="metric-unit">44.1kHz</div>
                        </div>
                        <div class="metric-trend">
                            <span class="trend-indicator">✅</span>
                            <span class="trend-text">Excellent</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <div class="metric-icon">🌐</div>
                            <h4 class="metric-title">Research Sources</h4>
                        </div>
                        <div class="metric-value-container">
                            <div id="research-sources" class="metric-value">0</div>
                            <div class="metric-unit">sources</div>
                        </div>
                        <div class="metric-trend">
                            <span class="trend-indicator">🔍</span>
                            <span class="trend-text">Searching...</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-card-header">
                            <div class="metric-icon">📝</div>
                            <h4 class="metric-title">Script Length</h4>
                        </div>
                        <div class="metric-value-container">
                            <div id="script-length" class="metric-value">0</div>
                            <div class="metric-unit">words</div>
                        </div>
                        <div class="metric-trend">
                            <span class="trend-indicator">✍️</span>
                            <span class="trend-text">Generating...</span>
                        </div>
                    </div>
                </div>
                
                <div class="status-timeline-detailed">
                    <div class="timeline-detailed-header">
                        <h4>Detailed Timeline</h4>
                        <div class="timeline-legend">
                            <span class="legend-item pending">⏳ Pending</span>
                            <span class="legend-item active">🔄 Active</span>
                            <span class="legend-item completed">✅ Completed</span>
                            <span class="legend-item error">❌ Error</span>
                        </div>
                    </div>
                    <div class="timeline-events">
                        <div class="timeline-event" data-status="completed">
                            <div class="event-time">00:00</div>
                            <div class="event-icon">🚀</div>
                            <div class="event-content">
                                <div class="event-title">Session Started</div>
                                <div class="event-description">Podcast generation workflow initiated</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2: Generation Progress -->
            <section id="progress-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">⚡</span>
                        Generating Your Podcast
                    </h2>
                    <p class="card-subtitle">Please wait while we create your professional podcast script</p>
                </div>

                <div class="progress-container">
                    <div class="progress-track">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-percentage">0%</div>
                </div>

                <div class="progress-stages">
                    <div class="stage" data-stage="searching">
                        <div class="stage-icon">🔍</div>
                        <div class="stage-content">
                            <div class="stage-title">Web Research</div>
                            <div class="stage-description">Gathering latest information</div>
                        </div>
                        <div class="stage-status"></div>
                    </div>

                    <div class="stage" data-stage="generating_report">
                        <div class="stage-icon">📊</div>
                        <div class="stage-content">
                            <div class="stage-title">Analysis</div>
                            <div class="stage-description">Creating research report</div>
                        </div>
                        <div class="stage-status"></div>
                    </div>

                    <div class="stage" data-stage="generating_script">
                        <div class="stage-icon">✍️</div>
                        <div class="stage-content">
                            <div class="stage-title">Script Writing</div>
                            <div class="stage-description">Crafting engaging dialogue</div>
                        </div>
                        <div class="stage-status"></div>
                    </div>

                    <div class="stage" data-stage="script_ready">
                        <div class="stage-icon">✅</div>
                        <div class="stage-content">
                            <div class="stage-title">Ready</div>
                            <div class="stage-description">Script completed</div>
                        </div>
                        <div class="stage-status"></div>
                    </div>
                </div>

                <div id="tts-status" class="tts-info-panel hidden">
                    <div class="tts-info-header">
                        <h4>🎙️ Voice Synthesis Status</h4>
                    </div>
                    <div class="tts-info-content">
                        <div class="info-row">
                            <span class="info-label">Engine:</span>
                            <span id="current-tts-provider" class="info-value">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Status:</span>
                            <span id="tts-generation-status" class="info-value">-</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2.5: Research Report -->
            <section id="report-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">📋</span>
                        Research Report
                    </h2>
                    <p class="card-subtitle">AI-generated research summary for your podcast topic</p>
                </div>
                <div id="report-content" class="report-content">
                    <!-- Report will be inserted here -->
                </div>
            </section>

            <!-- Step 3: Script Review -->
            <section id="script-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">📄</span>
                        Podcast Script
                    </h2>
                    <div class="script-meta">
                        <h3 id="script-title" class="script-title"></h3>
                        <p id="script-description" class="script-description"></p>
                    </div>
                </div>

                <div class="script-container">
                    <div id="script-dialogue" class="dialogue-view">
                        <!-- Dialogue lines will be inserted here -->
                    </div>
                </div>

                <div class="script-actions">
                    <button id="edit-script-btn" class="btn btn-outline">
                        <span class="btn-icon">✏️</span>
                        Edit Script
                    </button>
                </div>
            </section>

            <!-- Step 4: Voice Selection -->
            <section id="review-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">🎭</span>
                        Voice Selection
                    </h2>
                    <p class="card-subtitle">Choose the perfect voice for each speaker in your podcast</p>
                </div>

                <div id="voice-selection-container" class="voice-selection-grid">
                    <!-- Voice selection cards will be populated here -->
                </div>

                <div class="approval-panel">
                    <div class="approval-actions">
                        <button id="approve-script-btn" class="btn btn-success btn-large">
                            <span class="btn-icon">🎬</span>
                            <span class="btn-text">Generate Audio</span>
                        </button>
                        <button id="edit-before-approve-btn" class="btn btn-outline">
                            <span class="btn-icon">✏️</span>
                            Edit Script First
                        </button>
                    </div>
                </div>
            </section>

            <!-- Step 5: Final Audio -->
            <section id="audio-section" class="content-card hidden">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="card-icon">🎧</span>
                        Your Podcast is Ready!
                    </h2>
                    <p class="card-subtitle">Listen to your AI-generated podcast and download when ready</p>
                </div>

                <div class="audio-player-container">
                    <div class="audio-player-wrapper">
                        <audio id="podcast-audio" controls class="modern-audio-player">
                            Your browser does not support the audio element.
                        </audio>
                    </div>
                    <div class="audio-meta">
                        <div class="audio-info">
                            <span class="audio-duration">Duration: <span id="audio-duration">-</span></span>
                            <span class="audio-format">Format: MP3</span>
                        </div>
                    </div>
                </div>

                <div class="download-section">
                    <a id="download-audio-btn" class="btn btn-primary btn-large" download>
                        <span class="btn-icon">⬇️</span>
                        Download Podcast
                    </a>
                    <button id="create-new-btn" class="btn btn-outline">
                        <span class="btn-icon">🔄</span>
                        Create Another
                    </button>
                </div>
            </section>

            <!-- Error Section -->
            <section id="error-section" class="content-card error-card hidden">
                <div class="error-content">
                    <div class="error-icon">❌</div>
                    <h2 class="error-title">Something went wrong</h2>
                    <p id="error-message" class="error-message"></p>
                    <div class="error-actions">
                        <button id="retry-btn" class="btn btn-primary">
                            <span class="btn-icon">🔄</span>
                            Try Again
                        </button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Script Editor Modal -->
    <div id="script-editor-modal" class="modal-overlay hidden" role="dialog" aria-labelledby="modal-title" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h3 id="modal-title" class="modal-title">Edit Podcast Script</h3>
                <button class="modal-close" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="editor-container">
                    <textarea id="script-editor" class="script-editor" rows="25" placeholder="Edit your podcast script here..." aria-label="Podcast script editor"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button id="save-script-btn" class="btn btn-primary">
                    <span class="btn-icon">💾</span>
                    Save & Update
                </button>
                <button id="cancel-edit-btn" class="btn btn-outline">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="app-footer">
        <div class="footer-content">
            <p class="footer-text">Powered by MiniMax TTS & OpenRouter AI</p>
            <div class="footer-links">
                <span class="footer-version">v2.0</span>
            </div>
        </div>
    </footer>

    <script src="/static/js/app.js"></script>
</body>
</html>