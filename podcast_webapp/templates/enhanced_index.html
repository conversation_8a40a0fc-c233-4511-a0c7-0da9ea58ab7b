<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Podcast Studio - Enhanced Edition</title>
    <link rel="stylesheet" href="{{ url_for('static', path='css/enhanced_style.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header with Enhanced Branding -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-microphone-alt"></i>
                    <span>AI Podcast Studio</span>
                    <span class="version-badge">Enhanced</span>
                </div>
                <div class="header-status">
                    <div class="tts-status-indicator" id="ttsStatusIndicator">
                        <i class="fas fa-circle status-dot"></i>
                        <span>TTS Services</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Workflow Progress Bar -->
            <div class="workflow-progress" id="workflowProgress">
                <div class="progress-step active" data-step="1">
                    <div class="step-icon"><i class="fas fa-cog"></i></div>
                    <div class="step-info">
                        <div class="step-title">Configuration</div>
                        <div class="step-subtitle">Topic & Settings</div>
                    </div>
                </div>
                <div class="progress-connector"></div>
                <div class="progress-step" data-step="2">
                    <div class="step-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="step-info">
                        <div class="step-title">Script Generation</div>
                        <div class="step-subtitle">AI-Powered Content</div>
                    </div>
                </div>
                <div class="progress-connector"></div>
                <div class="progress-step" data-step="3">
                    <div class="step-icon"><i class="fas fa-volume-up"></i></div>
                    <div class="step-info">
                        <div class="step-title">Audio Synthesis</div>
                        <div class="step-subtitle">Enhanced TTS</div>
                    </div>
                </div>
            </div>

            <!-- Step 1: Enhanced Configuration Panel -->
            <div class="step-panel active" id="step1Panel">
                <div class="panel-header">
                    <h2><i class="fas fa-cog"></i> Podcast Configuration</h2>
                    <p>Configure your AI-powered podcast with advanced TTS capabilities</p>
                </div>

                <div class="config-grid">
                    <!-- Basic Configuration -->
                    <div class="config-section">
                        <h3><i class="fas fa-edit"></i> Content Settings</h3>
                        <div class="form-group">
                            <label for="topic">Podcast Topic</label>
                            <input type="text" id="topic" placeholder="Enter your podcast topic..." required>
                            <div class="input-hint">AI will research and generate content about this topic</div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="language">Language</label>
                                <select id="language">
                                    <option value="en">English</option>
                                    <option value="zh">中文</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="duration">Duration</label>
                                <select id="duration">
                                    <option value="300">5 minutes</option>
                                    <option value="600">10 minutes</option>
                                    <option value="900">15 minutes</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="style">Style</label>
                                <select id="style">
                                    <option value="conversational">Conversational</option>
                                    <option value="educational">Educational</option>
                                    <option value="interview">Interview</option>
                                    <option value="debate">Debate</option>
                                    <option value="storytelling">Storytelling</option>
                                    <option value="news">News Report</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="speakers">Speakers</label>
                                <select id="speakers">
                                    <option value="1">1 Speaker (Monologue)</option>
                                    <option value="2" selected>2 Speakers</option>
                                    <option value="3">3 Speakers</option>
                                    <option value="4">4 Speakers</option>
                                </select>
                            </div>
                        </div>

                        <!-- Advanced Settings Toggle -->
                        <div class="advanced-settings-toggle">
                            <button type="button" class="btn btn-secondary" id="advancedToggle">
                                <i class="fas fa-cog"></i> Advanced Settings
                                <i class="fas fa-chevron-down toggle-icon"></i>
                            </button>
                        </div>

                        <!-- Advanced Settings Panel -->
                        <div class="advanced-settings" id="advancedSettings">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="targetAudience">Target Audience</label>
                                    <select id="targetAudience">
                                        <option value="general">General Public</option>
                                        <option value="professional">Professionals</option>
                                        <option value="academic">Academic</option>
                                        <option value="children">Children</option>
                                        <option value="seniors">Seniors</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="complexity">Content Complexity</label>
                                    <select id="complexity">
                                        <option value="simple">Simple</option>
                                        <option value="moderate" selected>Moderate</option>
                                        <option value="advanced">Advanced</option>
                                        <option value="expert">Expert</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="mood">Overall Mood</label>
                                    <select id="mood">
                                        <option value="neutral" selected>Neutral</option>
                                        <option value="upbeat">Upbeat</option>
                                        <option value="serious">Serious</option>
                                        <option value="humorous">Humorous</option>
                                        <option value="dramatic">Dramatic</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="pacing">Speaking Pace</label>
                                    <select id="pacing">
                                        <option value="slow">Slow</option>
                                        <option value="normal" selected>Normal</option>
                                        <option value="fast">Fast</option>
                                        <option value="variable">Variable</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="backgroundMusic">Background Music</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-item">
                                        <input type="checkbox" id="enableMusic" name="backgroundMusic">
                                        <span class="checkmark"></span>
                                        <span class="checkbox-label">Enable Background Music</span>
                                    </label>
                                    <select id="musicStyle" class="music-style-select" disabled>
                                        <option value="ambient">Ambient</option>
                                        <option value="corporate">Corporate</option>
                                        <option value="uplifting">Uplifting</option>
                                        <option value="dramatic">Dramatic</option>
                                        <option value="minimal">Minimal</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="customInstructions">Custom Instructions</label>
                                <textarea id="customInstructions"
                                         placeholder="Add any specific instructions for the AI (e.g., 'Include technical examples', 'Use simple language', 'Add humor')..."
                                         rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced TTS Provider Selection -->
                    <div class="config-section">
                        <h3><i class="fas fa-microphone"></i> TTS Provider Selection</h3>
                        <div class="tts-providers" id="ttsProviders">
                            <!-- Providers will be populated by JavaScript -->
                        </div>
                        
                        <!-- Enhanced Features Preview -->
                        <div class="features-preview" id="featuresPreview">
                            <h4><i class="fas fa-star"></i> Available Features</h4>
                            <div class="feature-tags" id="featureTags">
                                <!-- Feature tags will be populated based on selected provider -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Template Library -->
                <div class="template-library" id="templateLibrary">
                    <h3><i class="fas fa-layer-group"></i> Quick Start Templates</h3>
                    <div class="template-grid">
                        <div class="template-card" data-template="tech-interview">
                            <div class="template-icon"><i class="fas fa-laptop-code"></i></div>
                            <div class="template-info">
                                <div class="template-title">Tech Interview</div>
                                <div class="template-description">Expert discusses latest technology trends</div>
                                <div class="template-tags">
                                    <span class="tag">2 Speakers</span>
                                    <span class="tag">10 min</span>
                                    <span class="tag">Professional</span>
                                </div>
                            </div>
                        </div>
                        <div class="template-card" data-template="educational">
                            <div class="template-icon"><i class="fas fa-graduation-cap"></i></div>
                            <div class="template-info">
                                <div class="template-title">Educational</div>
                                <div class="template-description">Learn complex topics made simple</div>
                                <div class="template-tags">
                                    <span class="tag">1 Speaker</span>
                                    <span class="tag">15 min</span>
                                    <span class="tag">Academic</span>
                                </div>
                            </div>
                        </div>
                        <div class="template-card" data-template="news-report">
                            <div class="template-icon"><i class="fas fa-newspaper"></i></div>
                            <div class="template-info">
                                <div class="template-title">News Report</div>
                                <div class="template-description">Current events and analysis</div>
                                <div class="template-tags">
                                    <span class="tag">2 Speakers</span>
                                    <span class="tag">5 min</span>
                                    <span class="tag">Formal</span>
                                </div>
                            </div>
                        </div>
                        <div class="template-card" data-template="storytelling">
                            <div class="template-icon"><i class="fas fa-book-open"></i></div>
                            <div class="template-info">
                                <div class="template-title">Storytelling</div>
                                <div class="template-description">Engaging narrative podcast</div>
                                <div class="template-tags">
                                    <span class="tag">3 Speakers</span>
                                    <span class="tag">20 min</span>
                                    <span class="tag">Dramatic</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Collaboration Panel -->
                <div class="collaboration-panel" id="collaborationPanel">
                    <h3><i class="fas fa-users"></i> Collaboration</h3>
                    <div class="collaboration-content">
                        <div class="collaboration-status">
                            <div class="status-indicator">
                                <i class="fas fa-circle online"></i>
                                <span>Real-time collaboration enabled</span>
                            </div>
                            <button class="btn btn-secondary" id="inviteBtn">
                                <i class="fas fa-user-plus"></i> Invite Collaborators
                            </button>
                        </div>

                        <div class="active-collaborators" id="activeCollaborators">
                            <div class="collaborator-list">
                                <div class="collaborator-item">
                                    <div class="collaborator-avatar">
                                        <img src="/static/images/avatar-placeholder.png" alt="You" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                        <div class="avatar-fallback" style="display:none;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="collaborator-info">
                                        <div class="collaborator-name">You</div>
                                        <div class="collaborator-role">Owner</div>
                                    </div>
                                    <div class="collaborator-status online"></div>
                                </div>
                            </div>
                        </div>

                        <div class="collaboration-features">
                            <div class="feature-item">
                                <i class="fas fa-comments"></i>
                                <span>Real-time comments</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-history"></i>
                                <span>Version history</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-share-alt"></i>
                                <span>Share & review</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-actions">
                    <button class="btn btn-secondary" id="loadTemplateBtn">
                        <i class="fas fa-layer-group"></i>
                        Use Template
                    </button>
                    <button class="btn btn-secondary" id="saveConfigBtn">
                        <i class="fas fa-save"></i>
                        Save Config
                    </button>
                    <button class="btn btn-primary" id="generateScriptBtn">
                        <i class="fas fa-magic"></i>
                        Generate Script with AI
                    </button>
                </div>
            </div>

            <!-- Step 2: Enhanced Script Review Panel -->
            <div class="step-panel" id="step2Panel">
                <div class="panel-header">
                    <h2><i class="fas fa-file-alt"></i> Script Review & Enhancement</h2>
                    <p>Review AI-generated script and add emotional expressions</p>
                </div>

                <div class="script-container">
                    <div class="script-toolbar">
                        <div class="toolbar-left">
                            <button class="btn btn-secondary" id="regenerateBtn">
                                <i class="fas fa-redo"></i> Regenerate
                            </button>
                            <button class="btn btn-secondary" id="enhanceBtn">
                                <i class="fas fa-magic"></i> Auto-Enhance
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <div class="enhancement-toggle">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="emotionToggle" checked>
                                    <span class="slider"></span>
                                </label>
                                <span>Emotion Tags</span>
                            </div>
                            <div class="enhancement-toggle">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="audioEventsToggle" checked>
                                    <span class="slider"></span>
                                </label>
                                <span>Audio Events</span>
                            </div>
                        </div>
                    </div>

                    <div class="script-editor" id="scriptEditor">
                        <!-- Script content will be populated here -->
                    </div>

                    <!-- Enhancement Palette -->
                    <div class="enhancement-palette" id="enhancementPalette">
                        <div class="palette-section">
                            <h4><i class="fas fa-theater-masks"></i> Emotion Tags</h4>
                            <div class="tag-grid">
                                <span class="emotion-tag" data-tag="excited">excited</span>
                                <span class="emotion-tag" data-tag="calm">calm</span>
                                <span class="emotion-tag" data-tag="happy">happy</span>
                                <span class="emotion-tag" data-tag="sad">sad</span>
                                <span class="emotion-tag" data-tag="whispering">whispering</span>
                                <span class="emotion-tag" data-tag="laughing">laughing</span>
                            </div>
                        </div>
                        <div class="palette-section">
                            <h4><i class="fas fa-music"></i> Audio Events</h4>
                            <div class="tag-grid">
                                <span class="audio-tag" data-tag="applause">applause</span>
                                <span class="audio-tag" data-tag="background music">background music</span>
                                <span class="audio-tag" data-tag="footsteps">footsteps</span>
                                <span class="audio-tag" data-tag="phone ringing">phone ringing</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-actions">
                    <button class="btn btn-secondary" id="backToConfigBtn">
                        <i class="fas fa-arrow-left"></i> Back to Config
                    </button>
                    <button class="btn btn-primary" id="proceedToVoicesBtn">
                        <i class="fas fa-arrow-right"></i> Select Voices
                    </button>
                </div>
            </div>

            <!-- Step 3: Enhanced Voice Selection & Audio Synthesis -->
            <div class="step-panel" id="step3Panel">
                <div class="panel-header">
                    <h2><i class="fas fa-volume-up"></i> Voice Selection & Audio Synthesis</h2>
                    <p>Choose voices and generate high-quality audio with enhanced features</p>
                </div>

                <div class="voice-selection-container">
                    <div class="speakers-grid" id="speakersGrid">
                        <!-- Speaker voice selections will be populated here -->
                    </div>

                    <!-- TTS Status Dashboard -->
                    <div class="tts-dashboard">
                        <h3><i class="fas fa-tachometer-alt"></i> TTS Status Dashboard</h3>
                        <div class="status-grid">
                            <div class="status-card" id="providerStatus">
                                <div class="status-icon"><i class="fas fa-server"></i></div>
                                <div class="status-info">
                                    <div class="status-title">Active Provider</div>
                                    <div class="status-value" id="activeProvider">-</div>
                                </div>
                            </div>
                            <div class="status-card" id="modelStatus">
                                <div class="status-icon"><i class="fas fa-brain"></i></div>
                                <div class="status-info">
                                    <div class="status-title">Model Version</div>
                                    <div class="status-value" id="activeModel">-</div>
                                </div>
                            </div>
                            <div class="status-card" id="featuresStatus">
                                <div class="status-icon"><i class="fas fa-star"></i></div>
                                <div class="status-info">
                                    <div class="status-title">Enhanced Features</div>
                                    <div class="status-value" id="enhancedFeatures">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-actions">
                    <button class="btn btn-secondary" id="backToScriptBtn">
                        <i class="fas fa-arrow-left"></i> Back to Script
                    </button>
                    <button class="btn btn-primary" id="generateAudioBtn">
                        <i class="fas fa-play"></i> Generate Audio
                    </button>
                </div>
            </div>

            <!-- Results Panel -->
            <div class="step-panel" id="resultsPanel">
                <div class="panel-header">
                    <h2><i class="fas fa-check-circle"></i> Podcast Generated Successfully</h2>
                    <p>Your AI-powered podcast is ready with enhanced audio features</p>
                </div>

                <div class="results-container">
                    <!-- Enhanced Audio Player with Waveform -->
                    <div class="audio-player-container">
                        <div class="audio-player-header">
                            <div class="track-info">
                                <h3 id="trackTitle">AI Generated Podcast</h3>
                                <p id="trackSubtitle">Enhanced with emotion tags and audio events</p>
                            </div>
                            <div class="audio-controls-extra">
                                <button class="btn-icon" id="loopBtn" title="Loop">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="btn-icon" id="speedBtn" title="Playback Speed">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span class="speed-indicator">1x</span>
                                </button>
                            </div>
                        </div>

                        <div class="waveform-container" id="waveformContainer">
                            <canvas id="waveformCanvas" class="waveform-canvas"></canvas>
                            <div class="progress-overlay" id="progressOverlay"></div>
                        </div>

                        <audio controls id="finalAudio" class="audio-player">
                            Your browser does not support the audio element.
                        </audio>

                        <div class="audio-timeline">
                            <span class="time-current" id="currentTime">0:00</span>
                            <div class="timeline-markers" id="timelineMarkers">
                                <!-- Emotion and audio event markers will be added here -->
                            </div>
                            <span class="time-total" id="totalTime">0:00</span>
                        </div>
                    </div>

                    <!-- Enhanced Generation Summary -->
                    <div class="generation-summary" id="generationSummary">
                        <!-- Summary will be populated by JavaScript -->
                    </div>

                    <!-- Quality Metrics Dashboard -->
                    <div class="quality-metrics" id="qualityMetrics">
                        <h3><i class="fas fa-chart-line"></i> Quality Metrics</h3>
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-icon"><i class="fas fa-volume-up"></i></div>
                                <div class="metric-info">
                                    <div class="metric-value" id="audioQuality">High</div>
                                    <div class="metric-label">Audio Quality</div>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon"><i class="fas fa-brain"></i></div>
                                <div class="metric-info">
                                    <div class="metric-value" id="aiScore">95%</div>
                                    <div class="metric-label">AI Naturalness</div>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon"><i class="fas fa-clock"></i></div>
                                <div class="metric-info">
                                    <div class="metric-value" id="processingSpeed">Fast</div>
                                    <div class="metric-label">Processing Speed</div>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon"><i class="fas fa-star"></i></div>
                                <div class="metric-info">
                                    <div class="metric-value" id="enhancementScore">8/10</div>
                                    <div class="metric-label">Enhancement Score</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Export Options -->
                    <div class="export-options" id="exportOptions">
                        <h3><i class="fas fa-download"></i> Export Options</h3>
                        <div class="export-grid">
                            <div class="export-card" data-format="mp3">
                                <div class="export-icon"><i class="fas fa-music"></i></div>
                                <div class="export-info">
                                    <div class="export-title">MP3 Audio</div>
                                    <div class="export-description">High quality compressed audio</div>
                                    <div class="export-size">~12.3 MB</div>
                                </div>
                                <button class="btn btn-secondary export-btn">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="export-card" data-format="wav">
                                <div class="export-icon"><i class="fas fa-file-audio"></i></div>
                                <div class="export-info">
                                    <div class="export-title">WAV Audio</div>
                                    <div class="export-description">Uncompressed studio quality</div>
                                    <div class="export-size">~45.7 MB</div>
                                </div>
                                <button class="btn btn-secondary export-btn">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="export-card" data-format="script">
                                <div class="export-icon"><i class="fas fa-file-alt"></i></div>
                                <div class="export-info">
                                    <div class="export-title">Enhanced Script</div>
                                    <div class="export-description">Script with emotion tags</div>
                                    <div class="export-size">~2.1 KB</div>
                                </div>
                                <button class="btn btn-secondary export-btn">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                            <div class="export-card" data-format="metadata">
                                <div class="export-icon"><i class="fas fa-info-circle"></i></div>
                                <div class="export-info">
                                    <div class="export-title">Metadata JSON</div>
                                    <div class="export-description">Generation details and settings</div>
                                    <div class="export-size">~1.5 KB</div>
                                </div>
                                <button class="btn btn-secondary export-btn">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Social Sharing -->
                    <div class="social-sharing" id="socialSharing">
                        <h3><i class="fas fa-share-alt"></i> Share Your Podcast</h3>
                        <div class="sharing-options">
                            <button class="share-btn twitter" data-platform="twitter">
                                <i class="fab fa-twitter"></i>
                                <span>Twitter</span>
                            </button>
                            <button class="share-btn linkedin" data-platform="linkedin">
                                <i class="fab fa-linkedin"></i>
                                <span>LinkedIn</span>
                            </button>
                            <button class="share-btn facebook" data-platform="facebook">
                                <i class="fab fa-facebook"></i>
                                <span>Facebook</span>
                            </button>
                            <button class="share-btn copy-link" data-platform="copy">
                                <i class="fas fa-link"></i>
                                <span>Copy Link</span>
                            </button>
                        </div>
                        <div class="share-preview" id="sharePreview">
                            <div class="preview-card">
                                <div class="preview-image">
                                    <i class="fas fa-microphone-alt"></i>
                                </div>
                                <div class="preview-content">
                                    <div class="preview-title">AI Generated Podcast</div>
                                    <div class="preview-description">Created with Enhanced ElevenLabs TTS</div>
                                    <div class="preview-url">podcast-studio.ai/listen/abc123</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="results-actions">
                        <button class="btn btn-secondary" id="editScriptBtn">
                            <i class="fas fa-edit"></i> Edit Script
                        </button>
                        <button class="btn btn-secondary" id="regenerateAudioBtn">
                            <i class="fas fa-redo"></i> Regenerate Audio
                        </button>
                        <button class="btn btn-primary" id="createNewBtn">
                            <i class="fas fa-plus"></i> Create New Podcast
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text" id="loadingText">Processing...</div>
                    <div class="loading-details" id="loadingDetails"></div>
                </div>
            </div>
        </div>
    </main>

    <script src="{{ url_for('static', path='js/enhanced_app.js') }}"></script>
</body>
</html>
