<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Podcast Studio - Enhanced Edition</title>
    <link rel="stylesheet" href="{{ url_for('static', path='css/enhanced_style.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header with Enhanced Branding -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-microphone-alt"></i>
                    <span>AI Podcast Studio</span>
                    <span class="version-badge">Enhanced</span>
                </div>
                <div class="header-status">
                    <div class="tts-status-indicator" id="ttsStatusIndicator">
                        <i class="fas fa-circle status-dot"></i>
                        <span>TTS Services</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Workflow Progress Bar -->
            <div class="workflow-progress" id="workflowProgress">
                <div class="progress-step active" data-step="1">
                    <div class="step-icon"><i class="fas fa-cog"></i></div>
                    <div class="step-info">
                        <div class="step-title">Configuration</div>
                        <div class="step-subtitle">Topic & Settings</div>
                    </div>
                </div>
                <div class="progress-connector"></div>
                <div class="progress-step" data-step="2">
                    <div class="step-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="step-info">
                        <div class="step-title">Script Generation</div>
                        <div class="step-subtitle">AI-Powered Content</div>
                    </div>
                </div>
                <div class="progress-connector"></div>
                <div class="progress-step" data-step="3">
                    <div class="step-icon"><i class="fas fa-volume-up"></i></div>
                    <div class="step-info">
                        <div class="step-title">Audio Synthesis</div>
                        <div class="step-subtitle">Enhanced TTS</div>
                    </div>
                </div>
            </div>

            <!-- Step 1: Enhanced Configuration Panel -->
            <div class="step-panel active" id="step1Panel">
                <div class="panel-header">
                    <h2><i class="fas fa-cog"></i> Podcast Configuration</h2>
                    <p>Configure your AI-powered podcast with advanced TTS capabilities</p>
                </div>

                <div class="config-grid">
                    <!-- Basic Configuration -->
                    <div class="config-section">
                        <h3><i class="fas fa-edit"></i> Content Settings</h3>
                        <div class="form-group">
                            <label for="topic">Podcast Topic</label>
                            <input type="text" id="topic" placeholder="Enter your podcast topic..." required>
                            <div class="input-hint">AI will research and generate content about this topic</div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="language">Language</label>
                                <select id="language">
                                    <option value="en">English</option>
                                    <option value="zh">中文</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="duration">Duration</label>
                                <select id="duration">
                                    <option value="300">5 minutes</option>
                                    <option value="600">10 minutes</option>
                                    <option value="900">15 minutes</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="style">Style</label>
                                <select id="style">
                                    <option value="conversational">Conversational</option>
                                    <option value="educational">Educational</option>
                                    <option value="interview">Interview</option>
                                    <option value="debate">Debate</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="speakers">Speakers</label>
                                <select id="speakers">
                                    <option value="2">2 Speakers</option>
                                    <option value="3">3 Speakers</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced TTS Provider Selection -->
                    <div class="config-section">
                        <h3><i class="fas fa-microphone"></i> TTS Provider Selection</h3>
                        <div class="tts-providers" id="ttsProviders">
                            <!-- Providers will be populated by JavaScript -->
                        </div>
                        
                        <!-- Enhanced Features Preview -->
                        <div class="features-preview" id="featuresPreview">
                            <h4><i class="fas fa-star"></i> Available Features</h4>
                            <div class="feature-tags" id="featureTags">
                                <!-- Feature tags will be populated based on selected provider -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-actions">
                    <button class="btn btn-primary" id="generateScriptBtn">
                        <i class="fas fa-magic"></i>
                        Generate Script with AI
                    </button>
                </div>
            </div>

            <!-- Step 2: Enhanced Script Review Panel -->
            <div class="step-panel" id="step2Panel">
                <div class="panel-header">
                    <h2><i class="fas fa-file-alt"></i> Script Review & Enhancement</h2>
                    <p>Review AI-generated script and add emotional expressions</p>
                </div>

                <div class="script-container">
                    <div class="script-toolbar">
                        <div class="toolbar-left">
                            <button class="btn btn-secondary" id="regenerateBtn">
                                <i class="fas fa-redo"></i> Regenerate
                            </button>
                            <button class="btn btn-secondary" id="enhanceBtn">
                                <i class="fas fa-magic"></i> Auto-Enhance
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <div class="enhancement-toggle">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="emotionToggle" checked>
                                    <span class="slider"></span>
                                </label>
                                <span>Emotion Tags</span>
                            </div>
                            <div class="enhancement-toggle">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="audioEventsToggle" checked>
                                    <span class="slider"></span>
                                </label>
                                <span>Audio Events</span>
                            </div>
                        </div>
                    </div>

                    <div class="script-editor" id="scriptEditor">
                        <!-- Script content will be populated here -->
                    </div>

                    <!-- Enhancement Palette -->
                    <div class="enhancement-palette" id="enhancementPalette">
                        <div class="palette-section">
                            <h4><i class="fas fa-theater-masks"></i> Emotion Tags</h4>
                            <div class="tag-grid">
                                <span class="emotion-tag" data-tag="excited">excited</span>
                                <span class="emotion-tag" data-tag="calm">calm</span>
                                <span class="emotion-tag" data-tag="happy">happy</span>
                                <span class="emotion-tag" data-tag="sad">sad</span>
                                <span class="emotion-tag" data-tag="whispering">whispering</span>
                                <span class="emotion-tag" data-tag="laughing">laughing</span>
                            </div>
                        </div>
                        <div class="palette-section">
                            <h4><i class="fas fa-music"></i> Audio Events</h4>
                            <div class="tag-grid">
                                <span class="audio-tag" data-tag="applause">applause</span>
                                <span class="audio-tag" data-tag="background music">background music</span>
                                <span class="audio-tag" data-tag="footsteps">footsteps</span>
                                <span class="audio-tag" data-tag="phone ringing">phone ringing</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-actions">
                    <button class="btn btn-secondary" id="backToConfigBtn">
                        <i class="fas fa-arrow-left"></i> Back to Config
                    </button>
                    <button class="btn btn-primary" id="proceedToVoicesBtn">
                        <i class="fas fa-arrow-right"></i> Select Voices
                    </button>
                </div>
            </div>

            <!-- Step 3: Enhanced Voice Selection & Audio Synthesis -->
            <div class="step-panel" id="step3Panel">
                <div class="panel-header">
                    <h2><i class="fas fa-volume-up"></i> Voice Selection & Audio Synthesis</h2>
                    <p>Choose voices and generate high-quality audio with enhanced features</p>
                </div>

                <div class="voice-selection-container">
                    <div class="speakers-grid" id="speakersGrid">
                        <!-- Speaker voice selections will be populated here -->
                    </div>

                    <!-- TTS Status Dashboard -->
                    <div class="tts-dashboard">
                        <h3><i class="fas fa-tachometer-alt"></i> TTS Status Dashboard</h3>
                        <div class="status-grid">
                            <div class="status-card" id="providerStatus">
                                <div class="status-icon"><i class="fas fa-server"></i></div>
                                <div class="status-info">
                                    <div class="status-title">Active Provider</div>
                                    <div class="status-value" id="activeProvider">-</div>
                                </div>
                            </div>
                            <div class="status-card" id="modelStatus">
                                <div class="status-icon"><i class="fas fa-brain"></i></div>
                                <div class="status-info">
                                    <div class="status-title">Model Version</div>
                                    <div class="status-value" id="activeModel">-</div>
                                </div>
                            </div>
                            <div class="status-card" id="featuresStatus">
                                <div class="status-icon"><i class="fas fa-star"></i></div>
                                <div class="status-info">
                                    <div class="status-title">Enhanced Features</div>
                                    <div class="status-value" id="enhancedFeatures">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="panel-actions">
                    <button class="btn btn-secondary" id="backToScriptBtn">
                        <i class="fas fa-arrow-left"></i> Back to Script
                    </button>
                    <button class="btn btn-primary" id="generateAudioBtn">
                        <i class="fas fa-play"></i> Generate Audio
                    </button>
                </div>
            </div>

            <!-- Results Panel -->
            <div class="step-panel" id="resultsPanel">
                <div class="panel-header">
                    <h2><i class="fas fa-check-circle"></i> Podcast Generated Successfully</h2>
                    <p>Your AI-powered podcast is ready with enhanced audio features</p>
                </div>

                <div class="results-container">
                    <div class="audio-player-container">
                        <audio controls id="finalAudio" class="audio-player">
                            Your browser does not support the audio element.
                        </audio>
                    </div>

                    <div class="generation-summary" id="generationSummary">
                        <!-- Summary will be populated by JavaScript -->
                    </div>

                    <div class="results-actions">
                        <button class="btn btn-secondary" id="downloadBtn">
                            <i class="fas fa-download"></i> Download Audio
                        </button>
                        <button class="btn btn-secondary" id="shareBtn">
                            <i class="fas fa-share"></i> Share
                        </button>
                        <button class="btn btn-primary" id="createNewBtn">
                            <i class="fas fa-plus"></i> Create New Podcast
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text" id="loadingText">Processing...</div>
                    <div class="loading-details" id="loadingDetails"></div>
                </div>
            </div>
        </div>
    </main>

    <script src="{{ url_for('static', path='js/enhanced_app.js') }}"></script>
</body>
</html>
