#!/usr/bin/env python3
"""
Test ElevenLabs Provider Selection Fix
Verify that selecting ElevenLabs actually uses ElevenLabs, not MiniMax
"""
import asyncio
import httpx
import json


async def test_elevenlabs_selection():
    """Test that selecting ElevenLabs actually uses ElevenLabs"""
    print("🎯 Testing ElevenLabs Provider Selection Fix")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            # Step 1: Generate a simple script
            print("1. Generating test script...")
            script_data = {
                "topic": "Test ElevenLabs Selection",
                "script_style": "conversational",
                "num_speakers": 2,
                "duration_target": 60,
                "language": "en",
                "tts_provider": "elevenlabs"  # Explicitly select ElevenLabs
            }
            
            script_response = await client.post(
                "http://127.0.0.1:8005/api/generate-script",
                json=script_data
            )
            
            if script_response.status_code != 200:
                print(f"❌ Script generation failed: {script_response.status_code}")
                return False
            
            script_result = script_response.json()
            session_id = script_result.get("session_id")
            
            if not session_id:
                print(f"❌ No session ID returned")
                return False
            
            print(f"✅ Script generated with session ID: {session_id}")
            
            # Step 2: Wait for script completion
            print("2. Waiting for script completion...")
            import time
            time.sleep(3)
            
            # Step 3: Get session data
            session_response = await client.get(f"http://127.0.0.1:8005/api/session/{session_id}")
            if session_response.status_code != 200:
                print(f"❌ Failed to get session: {session_response.status_code}")
                return False
            
            session_data = session_response.json()
            script = session_data.get("script")
            
            if not script:
                print(f"❌ No script found in session")
                return False
            
            print(f"✅ Script ready with {len(script.get('dialogue', []))} dialogue lines")
            
            # Step 4: Test audio generation with ElevenLabs
            print("3. Testing audio generation with ElevenLabs...")
            
            form_data = {
                "session_id": session_id,
                "approved_script": json.dumps(script),
                "voice_selections": json.dumps({
                    "host": "21m00Tcm4TlvDq8ikWAM",  # Rachel (ElevenLabs)
                    "expert": "ErXwobaYiN019PkySvjV"  # Antoni (ElevenLabs)
                }),
                "tts_provider": "elevenlabs"  # Explicitly select ElevenLabs
            }
            
            audio_response = await client.post(
                "http://127.0.0.1:8005/api/generate-audio",
                data=form_data
            )
            
            if audio_response.status_code != 200:
                print(f"❌ Audio generation failed: {audio_response.status_code}")
                try:
                    error_data = audio_response.json()
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Error text: {audio_response.text}")
                return False
            
            audio_result = audio_response.json()
            print(f"✅ Audio generation response received")
            print(f"   Success: {audio_result.get('success', False)}")
            print(f"   Provider used: {audio_result.get('provider_used', 'unknown')}")
            print(f"   Fallback used: {audio_result.get('fallback_used', False)}")
            
            # Check if ElevenLabs was actually used
            provider_used = audio_result.get('provider_used', '').lower()
            if 'elevenlabs' in provider_used:
                print(f"🎉 SUCCESS: ElevenLabs was used as expected!")
                return True
            elif 'minimax' in provider_used:
                print(f"❌ FAILURE: MiniMax was used instead of ElevenLabs")
                print(f"   This indicates the provider selection fix didn't work")
                return False
            else:
                print(f"⚠️ UNKNOWN: Unexpected provider used: {provider_used}")
                return False
                
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_provider_priority():
    """Test the new provider priority logic"""
    print(f"\n🔧 Testing Provider Priority Logic")
    print("=" * 60)
    
    try:
        # Import the services to test configuration
        import sys
        from pathlib import Path
        sys.path.append(str(Path(__file__).parent / "src"))
        
        from src.services.podcast_audio_service import PodcastAudioService
        
        # Test default configuration
        service = PodcastAudioService()
        config = service._build_tts_config()
        
        print(f"Default primary provider: {config.get('primary_provider')}")
        print(f"Default fallback providers: {config.get('fallback_providers')}")
        
        # Check if ElevenLabs is now the default
        if config.get('primary_provider') == 'elevenlabs':
            print(f"✅ ElevenLabs is now the default primary provider")
        else:
            print(f"❌ Primary provider is still: {config.get('primary_provider')}")
            return False
        
        # Check fallback logic
        fallbacks = config.get('fallback_providers', [])
        if 'minimax' not in fallbacks:
            print(f"✅ MiniMax is not in fallback list for ElevenLabs")
        else:
            print(f"❌ MiniMax is still in fallback list: {fallbacks}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Provider priority test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🔍 ElevenLabs Provider Selection Fix Verification")
    print("Testing that selecting ElevenLabs actually uses ElevenLabs")
    print("=" * 70)
    
    tests = [
        ("Provider Priority Logic", test_provider_priority),
        ("ElevenLabs Selection", test_elevenlabs_selection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Final summary
    print(f"\n" + "=" * 70)
    print("🏁 ELEVENLABS FIX TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ElevenLabs provider selection fix is working!")
        print("✅ ElevenLabs is now the default provider")
        print("✅ Selecting ElevenLabs actually uses ElevenLabs")
        print("✅ No unwanted fallback to MiniMax")
    else:
        print("\n⚠️ ElevenLabs provider selection still has issues")
        print("Check the failed tests above for details")
    
    print(f"\n💡 Manual Testing:")
    print("1. Open http://127.0.0.1:8005 in your browser")
    print("2. Generate a podcast script")
    print("3. Select 'ElevenLabs' as TTS provider")
    print("4. Choose ElevenLabs voices for speakers")
    print("5. Click 'Approve & Generate Audio'")
    print("6. Verify that ElevenLabs is used (not MiniMax)")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
