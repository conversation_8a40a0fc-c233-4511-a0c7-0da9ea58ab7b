import os
import httpx
import json
import logging
import re
import asyncio
from typing import List, Dict, Optional, Any
from ..models.podcast_models import (
    ResearchReport, PodcastScript, DialogueLine,
    PodcastRole, PodcastGenerationRequest
)

logger = logging.getLogger(__name__)


class PodcastScriptService:
    def __init__(self):
        # Initialize OpenRouter service (primary and only service)
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.base_url = os.getenv("OPENROUTER_BASE_URL", "https://openrouter.ai/api/v1")

        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        } if self.api_key else {}

        # Check if OpenRouter API is available
        if not self.api_key:
            logger.warning("OPENROUTER_API_KEY not found - will use demo mode")
        else:
            logger.info("OpenRouter service initialized successfully")
            logger.info("Using OpenRouter to access Gemini 2.5 Pro model")
        
        # Define voice mappings for different roles with speech-02-hd voice IDs
        # Generate elegant and realistic speaker names randomly
        import random
        
        english_hosts = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
        english_experts = ["<PERSON>. <PERSON> <PERSON>", "Prof. <PERSON>", "Dr. <PERSON>", "<PERSON>. <PERSON>", "Dr. <PERSON><PERSON>"]
        english_interview<PERSON> = ["<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>"]
        english_guests = ["<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON> <PERSON>", "<PERSON>", "<PERSON> <PERSON>ois"]
        
        chinese_hosts = ["林雅婷", "陈浩然", "王诗涵", "李明轩", "赵雨桐"]
        chinese_experts = ["张文华教授", "刘建国博士", "王美玲教授", "陈志强博士", "李雪梅教授"]
        chinese_interviewers = ["杨晓峰", "徐若琳", "周天宇", "孙美娟", "马俊杰"]
        chinese_guests = ["钱思远", "邓晓雅", "韩立明", "石雨萱", "邱志华"]
        
        self.voice_mappings = {
            "en": {
                PodcastRole.HOST: {
                    "name": random.choice(english_hosts),
                    "voice_id": "Chinese (Mandarin)_Wise_Women",
                    "description": "Warm and engaging podcast host",
                    "emotion": "happy"
                },
                PodcastRole.EXPERT: {
                    "name": random.choice(english_experts),
                    "voice_id": "Chinese (Mandarin)_Reliable_Executive",
                    "description": "Knowledgeable expert voice",
                    "emotion": "neutral"
                },
                PodcastRole.INTERVIEWER: {
                    "name": random.choice(english_interviewers),
                    "voice_id": "Chinese (Mandarin)_News_Anchor",
                    "description": "Curious and thoughtful interviewer",
                    "emotion": "auto"
                },
                PodcastRole.GUEST: {
                    "name": random.choice(english_guests),
                    "voice_id": "Chinese (Mandarin)_Reliable_Executive",
                    "description": "Enthusiastic guest speaker",
                    "emotion": "happy"
                }
            },
            "zh": {
                PodcastRole.HOST: {
                    "name": random.choice(chinese_hosts),
                    "voice_id": "Chinese (Mandarin)_Wise_Women",
                    "description": "温暖亲切的播客主持人",
                    "emotion": "happy"
                },
                PodcastRole.EXPERT: {
                    "name": random.choice(chinese_experts),
                    "voice_id": "Chinese (Mandarin)_Reliable_Executive",
                    "description": "知识渊博的专家声音",
                    "emotion": "neutral"
                },
                PodcastRole.INTERVIEWER: {
                    "name": random.choice(chinese_interviewers),
                    "voice_id": "Chinese (Mandarin)_News_Anchor",
                    "description": "好奇深思的采访者",
                    "emotion": "auto"
                },
                PodcastRole.GUEST: {
                    "name": random.choice(chinese_guests),
                    "voice_id": "Chinese (Mandarin)_Reliable_Executive",
                    "description": "热情的嘉宾发言人",
                    "emotion": "happy"
                }
            }
        }
    
    async def generate_podcast_script(
        self, 
        report: ResearchReport, 
        request: PodcastGenerationRequest
    ) -> PodcastScript:
        """Generate a multi-role podcast script from the research report"""
        
        
        # Prepare the context for script generation
        key_points = "\n".join([f"- {finding}" for finding in report.key_findings])
        
        # Define roles based on number of speakers and style
        if request.num_speakers == 2:
            if request.script_style == "interview":
                roles = [PodcastRole.INTERVIEWER, PodcastRole.EXPERT]
            else:
                roles = [PodcastRole.HOST, PodcastRole.EXPERT]
        else:  # 3 speakers
            if request.script_style == "debate":
                roles = [PodcastRole.HOST, PodcastRole.EXPERT, PodcastRole.GUEST]
            else:
                roles = [PodcastRole.HOST, PodcastRole.EXPERT, PodcastRole.INTERVIEWER]
        
        # Get language-specific voice mappings
        lang_voices = self.voice_mappings.get(request.language, self.voice_mappings["en"])
        
        role_descriptions = "\n".join([
            f"- {lang_voices[role]['name']} ({role.value}): {lang_voices[role]['description']}"
            for role in roles
        ])
        
        # Language-specific instructions
        language_instructions = {
            "en": {
                "instruction": "Create the entire script in English",
                "sample_patterns": '"um", "well", "you know", "I mean", "actually", "wait", "really?", "exactly!", "but here\'s the thing", "hold on"',
                "title_prefix": "Podcast:",
                "duration_note": "Average speaking pace is about 150-180 words per minute"
            },
            "zh": {
                "instruction": "用中文创建整个脚本",
                "sample_patterns": '"嗯", "那么", "你知道", "我觉得", "等等", "真的吗", "没错!", "但是", "我想说的是", "这个"',
                "title_prefix": "播客:",
                "duration_note": "中文语音平均每分钟约100-120字"
            }
        }
        
        lang_info = language_instructions.get(request.language, language_instructions["en"])
        
        # Calculate target word count based on language and duration
        if request.language == "zh":
            target_words = int(request.duration_target * 110 / 60)  # ~110 characters per minute for Chinese
            words_per_line = "15-25 characters"
            max_chars_per_line = "100 characters MAX (strictly enforced)"
        else:
            target_words = int(request.duration_target * 165 / 60)  # ~165 words per minute for English
            words_per_line = "8-15 words"
            max_chars_per_line = "100 characters MAX (strictly enforced)"
        
        # Generate professional podcast script using specialized prompt templates
        script_prompt = self._generate_professional_prompt(
            report=report,
            request=request,
            lang_info=lang_info,
            role_descriptions=role_descriptions,
            key_points=key_points,
            target_words=target_words
        )

        # Use OpenRouter to access Gemini 2.5 Pro (not direct connection)
        logger.info(f"Generating podcast script via OpenRouter using Gemini 2.5 Pro model")
        logger.info(f"Topic: {report.topic}, Language: {request.language}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json={
                    "model": "google/gemini-2.5-pro",  # Accessed via OpenRouter, not direct
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are an expert podcast script writer specializing in creating long-form, engaging, natural conversations with rich content and professional depth. You excel at transforming research into compelling dialogue."
                        },
                        {
                            "role": "user",
                            "content": script_prompt
                        }
                    ],
                    "temperature": 0.85,  # Higher creativity for natural dialogue
                    "max_tokens": 8000,   # Significantly increased for long-form content
                    "top_p": 0.95,        # Allow more diverse token selection
                    "frequency_penalty": 0.2,  # Reduce repetitive phrases
                    "presence_penalty": 0.15   # Encourage topic diversity
                },
                timeout=60.0
            )
            
            if response.status_code != 200:
                raise Exception(f"OpenRouter API error: {response.status_code} - {response.text}")
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            try:
                # Parse JSON from response
                import re
                import json
                json_match = re.search(r'\{[\s\S]*\}', content)
                if json_match:
                    script_data = json.loads(json_match.group())
                else:
                    script_data = json.loads(content)
                
                # Create dialogue lines with voice assignments
                dialogue_lines = []
                for line_data in script_data.get('dialogue', []):
                    role = PodcastRole(line_data['role'])
                    voice_info = lang_voices.get(role, lang_voices[PodcastRole.HOST])

                    # Process text - remove role prefixes like [A], [B], [Host], [主持人], [嘉宾], etc.
                    original_text = line_data['text']
                    # Remove role prefixes and clean text (supports both English and Chinese)
                    import re
                    cleaned_text = re.sub(r'^\[[A-Za-z0-9\u4e00-\u9fff]+\]\s*', '', original_text)
                    # Extract emotion from text if embedded like <emotion=happy>
                    emotion_match = re.search(r'<emotion=([a-zA-Z]+)>', cleaned_text)
                    if emotion_match:
                        extracted_emotion = emotion_match.group(1)
                        cleaned_text = re.sub(r'<emotion=[a-zA-Z]+>', '', cleaned_text).strip()
                    else:
                        extracted_emotion = line_data.get('emotion', 'neutral')

                    # Add natural pause marks for better TTS synthesis
                    cleaned_text = self._add_natural_pauses(cleaned_text, extracted_emotion, request.language)

                    # Use emotion from extraction or LLM analysis
                    valid_emotions = ["happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral",
                                    "thoughtful", "concerned", "confident", "challenging", "passionate",
                                    "amused", "curious"]
                    emotion = extracted_emotion if extracted_emotion in valid_emotions else "neutral"

                    # For ElevenLabs, we can use longer text (up to 2500 chars)
                    # For MiniMax, we need to keep it under 100 chars
                    # But we should avoid splitting into multiple dialogue lines to prevent display issues

                    # Truncate text if too long instead of splitting into multiple lines
                    max_length = 2500 if request.tts_provider == TTSProvider.ELEVENLABS else 100
                    final_text = cleaned_text[:max_length] if len(cleaned_text) > max_length else cleaned_text

                    # Create single dialogue line (no splitting)
                    dialogue_lines.append(DialogueLine(
                        role=role,
                        speaker_name=line_data.get('speaker_name', voice_info['name']),
                        text=final_text,
                        voice_id=voice_info['voice_id'],
                        emotion=emotion
                    ))
                
                # Estimate duration (roughly 150 words per minute)
                total_words = sum(len(line.text.split()) for line in dialogue_lines)
                duration_estimate = int((total_words / 150) * 60)
                
                return PodcastScript(
                    title=script_data.get('title', f"Podcast: {report.topic}"),
                    description=script_data.get('description', f"A discussion about {report.topic}"),
                    dialogue=dialogue_lines,
                    duration_estimate=duration_estimate
                )
                
            except Exception as e:
                print(f"Error parsing script: {e}")
                # Create a simple fallback script
                return self._create_fallback_script(report, roles, request.language)

    def _generate_professional_prompt(self, report, request, lang_info, role_descriptions, key_points, target_words):
        """Generate professional podcast script using specialized prompt templates"""
        
        # Get speaker names from role descriptions
        lang_voices = self.voice_mappings.get(request.language, self.voice_mappings["en"])
        
        # Determine script style and generate appropriate prompt
        if request.script_style == "interview" and request.num_speakers == 2:
            return self._create_interview_prompt(report, request, lang_info, lang_voices, key_points, target_words)
        elif request.script_style == "debate" and request.num_speakers == 2:
            return self._create_debate_prompt(report, request, lang_info, lang_voices, key_points, target_words)
        elif request.num_speakers == 3:
            return self._create_roundtable_prompt(report, request, lang_info, lang_voices, key_points, target_words)
        else:
            # Default to interview style
            return self._create_interview_prompt(report, request, lang_info, lang_voices, key_points, target_words)
    
    def _create_interview_prompt(self, report, request, lang_info, lang_voices, key_points, target_words):
        """Create two-person interview script prompt"""
        
        # Determine roles
        if request.script_style == "interview":
            host_role = PodcastRole.INTERVIEWER
            guest_role = PodcastRole.EXPERT
        else:
            host_role = PodcastRole.HOST
            guest_role = PodcastRole.EXPERT
            
        host_name = lang_voices[host_role]["name"]
        guest_name = lang_voices[guest_role]["name"]
        guest_description = lang_voices[guest_role]["description"]
        
        # Calculate segments for target duration
        total_exchanges = max(8, int(target_words / 60))  # ~60 words per exchange

        # Format key findings for the prompt
        key_findings_text = "\n".join([f"- {finding}" for finding in report.key_findings])
        
        if request.language == "zh":
            prompt = f"""
🎙️ 高质量播客脚本生成任务

你是一位顶级播客脚本编剧，专门创作深度、专业、引人入胜的长篇对话内容。你的作品以内容丰富、对话自然、专业深度著称。

[核心任务]
基于详细的研究报告，创作一个高质量的播客脚本，要求内容充实、对话自然、专业深入。

[基本参数]
- 话题：{report.topic}
- 语言：中文
- 演讲者（2人）：
  · 主持人：{host_name}，语调：温暖、引导性、善于提问
  · 专家嘉宾：{guest_name}，专业领域：{guest_description}，语调：见解深刻、专业、善于举例

[详细研究报告内容]
主题摘要：{report.summary}

核心发现：
{key_findings_text}

[内容质量要求 - 关键指标]
1. **对话轮数**：必须生成12-15轮对话（不是6-8轮）
2. **段落长度**：每个对话段落50-100字，包含具体细节和深入分析
3. **总字数**：800-1000字中文文本（对应5分钟音频）
4. **专业深度**：充分利用研究报告中的具体信息、数据、案例
5. **事实准确**：基于研究报告内容，包含具体的例子、数字、案例
6. **避免空洞**：每句话都要有实质内容，避免"这很重要"等泛泛而谈

[对话自然度要求]
1. **真实对话节奏**：包含自然的停顿、思考、惊讶、认同等反应
2. **口语化表达**：使用"嗯"、"确实"、"你提到的这点很有意思"、"我想补充一下"等自然表达
3. **互动性强**：主持人要有跟进提问，专家要有详细回应和举例
4. **角色差异**：主持人（好奇、引导）vs 专家（分析、举例、数据支撑）
5. **话题转换**：自然的过渡，如"说到这个，我想到另一个问题"、"这让我想起..."

[详细结构要求（12-15轮对话）]
1. 开场介绍（1轮）：主持人介绍话题和嘉宾，60-80字
2. 话题背景（2轮）：简要背景 + 专家补充，每轮50-70字
3. 核心问题探讨第一部分（3-4轮）：深入讨论主要发现，每轮60-90字
4. 具体案例分析（2-3轮）：举例说明，包含具体数据，每轮70-100字
5. 核心问题探讨第二部分（3-4轮）：不同角度分析，每轮60-90字
6. 实际应用/影响（2轮）：实际意义和应用，每轮50-80字
7. 总结和展望（1-2轮）：总结要点和未来展望，每轮60-80字

[格式与风格]
- 对话内容格式：直接的对话文本，不要包含任何角色标签如[主持人]、[嘉宾]等
- 每行最多90字符（中文）；演讲者交替
- 可以使用停顿标记`<#0.1#>`、`<#0.2#>`、`<#0.3#>`来增强语音自然度
- 保持语言一致性和对话流畅度
- **重要**：文本内容将直接用于语音合成，停顿标记会被TTS系统识别

[关键讨论要点（必须涵盖）]
{key_points}

[严格要求]
- 对话内容必须紧密围绕"{report.topic}"这个具体主题
- 避免使用"这个领域"、"这个话题"等模糊表述，直接使用具体的主题名称
- 每句对话都应该包含与主题相关的具体信息、观点、数据或见解
- 如果主题涉及比较，要明确讨论具体差异点和对比数据
- 必须使用研究报告中的具体例子、数据或案例来支撑观点
- 确保达到800-1000字的目标长度
- 确保12-15轮对话的目标数量

[输出格式]
仅返回脚本正文的JSON格式——没有系统或用户文本。确保JSON格式正确，可被程序解析。

{{
    "title": "{report.topic}深度解析",
    "description": "{host_name}与{guest_name}深入探讨{report.topic}",
    "dialogue": [
        {{
            "role": "host",
            "speaker_name": "{host_name}",
            "text": "大家好！今天我们要深入探讨{report.topic}。这个话题最近引起了很多关注。",
            "emotion": "excited"
        }},
        {{
            "role": "expert",
            "speaker_name": "{guest_name}",
            "text": "谢谢邀请！{report.topic}确实是一个值得深入分析的主题，我很乐意分享一些专业观点。",
            "emotion": "happy"
        }}
    ]
}}
"""
        else:
            prompt = f"""
🎙️ High-Quality Podcast Script Generation Task

You are a top-tier podcast script writer specializing in creating in-depth, professional, engaging long-form dialogue content. Your work is known for rich content, natural conversation flow, and professional depth.

[CORE MISSION]
Based on detailed research reports, create a high-quality podcast script that is content-rich, naturally conversational, and professionally in-depth.

[BASIC PARAMETERS]
- Topic: {report.topic}
- Language: English
- Speakers (2):
  · Host: {host_name}, tone: warm, engaging, excellent at asking questions
  · Expert Guest: {guest_name}, expertise: {guest_description}, tone: insightful, professional, good at examples

[DETAILED RESEARCH REPORT CONTENT]
Topic Summary: {report.summary}

Key Findings:
{key_findings_text}

[CONTENT QUALITY REQUIREMENTS - KEY METRICS]
1. **Dialogue Rounds**: Must generate 12-15 rounds of dialogue (not 6-8 rounds)
2. **Paragraph Length**: Each dialogue paragraph 50-100 words, including specific details and in-depth analysis
3. **Total Word Count**: 600-800 English words (corresponding to 5 minutes of audio)
4. **Professional Depth**: Fully utilize specific information, data, and cases from the research report
5. **Factual Accuracy**: Based on research report content, include specific examples, numbers, and cases
6. **Avoid Emptiness**: Every sentence should have substantial content, avoid generic statements like "this is important"

[DIALOGUE NATURALNESS REQUIREMENTS]
1. **Authentic Dialogue Rhythm**: Include natural pauses, thinking, surprise, agreement, and other reactions
2. **Conversational Expressions**: Use "um", "actually", "you mentioned something interesting", "I'd like to add" and other natural expressions
3. **Strong Interactivity**: Host should have follow-up questions, expert should have detailed responses and examples
4. **Role Differences**: Host (curious, guiding) vs Expert (analytical, examples, data support)
5. **Topic Transitions**: Natural transitions like "speaking of that, I'm thinking of another question", "that reminds me..."

[DETAILED STRUCTURE REQUIREMENTS (12-15 rounds of dialogue)]
1. Opening Introduction (1 round): Host introduces topic and guest, 60-80 words
2. Topic Background (2 rounds): Brief background + expert supplement, 50-70 words each
3. Core Issue Discussion Part 1 (3-4 rounds): In-depth discussion of main findings, 60-90 words each
4. Specific Case Analysis (2-3 rounds): Examples with specific data, 70-100 words each
5. Core Issue Discussion Part 2 (3-4 rounds): Different angle analysis, 60-90 words each
6. Practical Application/Impact (2 rounds): Practical significance and applications, 50-80 words each
7. Summary and Outlook (1-2 rounds): Summary of key points and future outlook, 60-80 words each

[FORMAT & STYLE]
- Dialogue content format: Direct dialogue text without any role labels like [Host], [Guest], etc.
- Max 90 words per line (English); alternate speakers
- Pause marks can be used like `<#0.1#>`, `<#0.2#>`, `<#0.3#>` to enhance speech naturalness
- Keep language consistent and dialogue flowing
- **Important**: Text content will be used directly for speech synthesis, pause marks will be recognized by TTS

[KEY DISCUSSION POINTS (MUST COVER)]
{key_points}

[STRICT REQUIREMENTS]
- Dialogue content must closely focus on the specific topic "{report.topic}"
- Avoid vague expressions like "this field", "this topic", use specific topic names directly
- Every dialogue sentence should contain specific information, viewpoints, data, or insights related to the topic
- If the topic involves comparison, clearly discuss specific differences and comparative data
- Must use specific examples, data, or cases from the research report to support viewpoints
- Ensure reaching the target length of 600-800 words
- Ensure reaching the target of 12-15 rounds of dialogue

[OUTPUT FORMAT]
Return **only** the script body in JSON format—no SYSTEM or USER text. Ensure JSON format is correct and can be parsed by programs.

{{
    "title": "Deep Dive: {report.topic}",
    "description": "An engaging conversation between {host_name} and {guest_name}",
    "dialogue": [
        {{
            "role": "host",
            "speaker_name": "{host_name}",
            "text": "Welcome everyone to today's show! We're diving into something really fascinating.",
            "emotion": "excited"
        }},
        {{
            "role": "expert",
            "speaker_name": "{guest_name}",
            "text": "Thanks for having me! I'm thrilled to share some insights with your listeners.",
            "emotion": "happy"
        }}
    ]
}}
"""
        
        return prompt

    def _create_debate_prompt(self, report, request, lang_info, lang_voices, key_points, target_words):
        """Create two-person debate script prompt"""
        
        host_role = PodcastRole.HOST
        pro_role = PodcastRole.EXPERT  
        con_role = PodcastRole.GUEST
        
        host_name = lang_voices[host_role]["name"]
        pro_name = lang_voices[pro_role]["name"]
        con_name = lang_voices[con_role]["name"]
        
        # Calculate rounds for target duration
        total_rounds = max(3, int(target_words / 80))  # ~80 words per round
        
        if request.language == "zh":
            prompt = f"""
🔹 系统指令（元指令——不得出现在最终脚本中）
你是一位经验丰富的播客脚本编剧。

🔹 用户请求（实际请求）
请根据以下参数起草播客对话脚本：

[基本参数]
- 话题：{report.topic}
- 语言：中文
- 演讲者（3人）：
  · 主持人：{host_name}，语调：中立、引导性
  · 正方：{pro_name}，专业领域：支持观点，语调：自信、有理有据
  · 反方：{con_name}，专业领域：反对观点，语调：挑战性、批判性思维

[结构要求]
1. 吸引人的开场（~30字）：主持人介绍辩论话题和双方立场
2. 正方开场论述（1轮）：明确立场和核心论点
3. 反方回应（1轮）：提出反驳和不同视角
4. 深度辩论段落1（2轮交锋，主持人适时引导）
5. 深度辩论段落2（2轮交锋，可引用数据或案例）
6. 关键争议点总结（主持人总结双方观点差异）
7. 结束语（邀请听众思考和参与讨论）

[格式与风格]
- 对话内容格式：直接的对话文本，不要包含任何角色标签
- 每行最多80字符（中文）；保持观点交锋的节奏
- 使用情感标签反映辩论强度，但会在处理时被移除
- 可以使用停顿标记增强辩论的戏剧性，如在关键观点前使用`<#0.3#>`
- 辩论必须有理有据，避免人身攻击
- **重要**：文本内容将直接用于语音合成，停顿标记会被TTS系统识别

[关键讨论要点]
{key_points}

[输出]
仅返回脚本正文的JSON格式。

{{
    "title": "观点交锋：{report.topic}",
    "description": "{pro_name}与{con_name}的精彩辩论",
    "dialogue": [
        {{
            "role": "host",
            "speaker_name": "{host_name}",
            "text": "欢迎收听今天的辩论节目，我们将探讨一个备受关注的话题。",
            "emotion": "neutral"
        }}
    ]
}}
"""
        else:
            prompt = f"""
🔹 SYSTEM (meta-instruction—must not appear in the final script)
You are an experienced podcast scriptwriter.

🔹 USER (actual request)
Please draft a podcast dialogue script according to the following parameters:

[BASIC PARAMETERS]
- Topic: {report.topic}
- Language: EN
- Speakers (3):
  · Host: {host_name}, tone: neutral, guiding
  · Pro side: {pro_name}, expertise: supporting viewpoint, tone: confident, evidence-based
  · Con side: {con_name}, expertise: opposing viewpoint, tone: challenging, critical thinking

[STRUCTURE REQUIREMENTS]
1. Hook & Intro (~30 words): host introduces debate topic and both positions
2. Pro Opening Statement (1 turn): clear position and core arguments
3. Con Response (1 turn): rebuttal and alternative perspective
4. Deep Debate Segment 1 (2 rounds of exchange, host moderates)
5. Deep Debate Segment 2 (2 rounds, data or examples welcome)
6. Key Disagreement Summary (host summarizes the core differences)
7. Wrap-up (invite listeners to think and engage)

[FORMAT & STYLE]
- Dialogue content format: Direct dialogue text without any role labels
- Max 80 words per line; maintain debate rhythm
- Use emotion tags to reflect debate intensity, but they will be removed during processing
- Debate must be reasoned, avoid personal attacks
- **Important**: Text content will be used directly for speech synthesis, do not include any labels that would be read aloud

[KEY POINTS TO DISCUSS]
{key_points}

[OUTPUT]
Return **only** the script body in JSON format.

{{
    "title": "Debate: {report.topic}",
    "description": "A spirited debate between {pro_name} and {con_name}",
    "dialogue": [
        {{
            "role": "host",
            "speaker_name": "{host_name}",
            "text": "Welcome to today's debate show where we explore a highly relevant topic.",
            "emotion": "neutral"
        }}
    ]
}}
"""
            
        return prompt

    def _create_roundtable_prompt(self, report, request, lang_info, lang_voices, key_points, target_words):
        """Create three-person round-table script prompt"""
        
        host_role = PodcastRole.HOST
        guest1_role = PodcastRole.EXPERT
        guest2_role = PodcastRole.GUEST
        
        host_name = lang_voices[host_role]["name"]
        guest1_name = lang_voices[guest1_role]["name"]
        guest2_name = lang_voices[guest2_role]["name"]
        guest1_traits = lang_voices[guest1_role]["description"]
        guest2_traits = lang_voices[guest2_role]["description"]
        
        # Calculate topics and rounds
        total_rounds = max(6, int(target_words / 90))  # ~90 words per round
        
        if request.language == "zh":
            prompt = f"""
🔹 系统指令（元指令——不得出现在最终脚本中）
你是一位经验丰富的播客脚本编剧。

🔹 用户请求（实际请求）
请根据以下参数起草播客对话脚本：

[基本参数]
- 话题：{report.topic}
- 语言：中文
- 演讲者（3人）：
  · 主持人：{host_name}，语调：活跃、善于引导讨论
  · 嘉宾1：{guest1_name}，专业领域：{guest1_traits}，语调：专业、分析性
  · 嘉宾2：{guest2_name}，专业领域：{guest2_traits}，语调：实践性、案例丰富

[结构要求]
1. 吸引人的开场（~30字）：主持人介绍话题和两位嘉宾
2. 话题导入（主持人提出第一个关键问题）
3. 深度讨论段落1：
   - 嘉宾1分享专业见解（1轮）
   - 嘉宾2补充或提供不同视角（1轮）
   - 三方自由讨论（2-3轮）
4. 深度讨论段落2：
   - 主持人引入新角度
   - 两位嘉宾交替发言并互动
5. 实践应用讨论（可选）
6. 总结与展望（各方总结要点）
7. 致谢与结束语

[格式与风格]
- 对话内容格式：直接的对话文本，不要包含任何角色标签
- 每行最多90字符（中文）；保持对话节奏
- 可在适当时候加入(笑)等非语言提示
- 鼓励嘉宾间的自然互动和观点碰撞
- **重要**：文本内容将直接用于语音合成，不要包含任何会被读出的标签

[关键讨论要点]
{key_points}

[输出]
仅返回脚本正文的JSON格式。

{{
    "title": "圆桌会谈：{report.topic}",
    "description": "{host_name}主持的精彩三人对话",
    "dialogue": [
        {{
            "role": "host",
            "speaker_name": "{host_name}",
            "text": "欢迎大家！今天我们有两位杰出嘉宾共同探讨这个话题。",
            "emotion": "happy"
        }}
    ]
}}
"""
        else:
            prompt = f"""
🔹 SYSTEM (meta-instruction—must not appear in the final script)
You are an experienced podcast scriptwriter.

🔹 USER (actual request)
Please draft a podcast dialogue script according to the following parameters:

[BASIC PARAMETERS]
- Topic: {report.topic}
- Language: EN
- Speakers (3):
  · Host: {host_name}, tone: lively, good at facilitating discussion
  · Guest 1: {guest1_name}, expertise: {guest1_traits}, tone: professional, analytical
  · Guest 2: {guest2_name}, expertise: {guest2_traits}, tone: practical, case-driven

[STRUCTURE REQUIREMENTS]
1. Hook & Intro (~30 words): host introduces topic and both guests
2. Topic Lead-in (host poses first key question)
3. Deep Dive Segment 1:
   - Guest 1 shares professional insights (1 turn)
   - Guest 2 adds or provides different perspective (1 turn)
   - Three-way free discussion (2-3 turns)
4. Deep Dive Segment 2:
   - Host introduces new angle
   - Guests alternate and interact
5. Practical Applications (optional)
6. Summary & Outlook (each party summarizes key points)
7. Thanks & Sign-off

[FORMAT & STYLE]
- Dialogue content format: Direct dialogue text without any role labels
- Max 90 words per line; maintain conversation rhythm
- Can include (laughs) or other non-verbal cues where appropriate
- Encourage natural interaction and viewpoint exchanges between guests
- **Important**: Text content will be used directly for speech synthesis, do not include any labels that would be read aloud

[KEY POINTS TO DISCUSS]
{key_points}

[OUTPUT]
Return **only** the script body in JSON format.

{{
    "title": "Round Table: {report.topic}",
    "description": "A lively three-way conversation hosted by {host_name}",
    "dialogue": [
        {{
            "role": "host",
            "speaker_name": "{host_name}",
            "text": "Welcome everyone! We have two distinguished guests today to explore this topic.",
            "emotion": "happy"
        }}
    ]
}}
"""
            
        return prompt



    def _add_natural_pauses(self, text: str, emotion: str, language: str = "en") -> str:
        """Add natural pause marks to improve TTS synthesis quality"""
        import re

        # Define pause durations based on context and emotion (max 0.3s)
        if emotion in ["excited", "passionate", "challenging"]:
            # Faster pace for energetic emotions
            short_pause = "0.1"
            medium_pause = "0.2"
            long_pause = "0.3"
        elif emotion in ["thoughtful", "concerned", "neutral"]:
            # Normal pace for calm emotions
            short_pause = "0.15"
            medium_pause = "0.25"
            long_pause = "0.3"
        else:
            # Default pacing
            short_pause = "0.1"
            medium_pause = "0.2"
            long_pause = "0.3"

        if language == "zh":
            # Chinese punctuation and pause patterns
            # Add pauses after sentence endings
            text = re.sub(r'([。！？])', rf'\1<#{long_pause}#>', text)

            # Add pauses after commas and semicolons
            text = re.sub(r'([，；])', rf'\1<#{medium_pause}#>', text)

            # Add pauses after colons (often used for explanations)
            text = re.sub(r'([：])', rf'\1<#{medium_pause}#>', text)

            # Add breathing pauses in long sentences (after certain conjunctions)
            conjunctions = ['但是', '然而', '因此', '所以', '而且', '另外', '首先', '其次', '最后', '总之']
            for conj in conjunctions:
                text = re.sub(rf'({conj})', rf'<#{short_pause}#>\1', text)

            # Add emphasis pauses before important phrases
            emphasis_words = ['重要的是', '关键在于', '值得注意的是', '特别是', '尤其是', '比如说', '例如']
            for word in emphasis_words:
                text = re.sub(rf'({word})', rf'<#{medium_pause}#>\1', text)

            # Add pauses after questions for response time
            text = re.sub(r'([？])', rf'\1<#{long_pause}#>', text)

        else:
            # English punctuation and pause patterns
            # Add pauses after sentence endings
            text = re.sub(r'([.!?])', rf'\1<#{long_pause}#>', text)

            # Add pauses after commas and semicolons
            text = re.sub(r'([,;])', rf'\1<#{medium_pause}#>', text)

            # Add pauses after colons
            text = re.sub(r'([:])', rf'\1<#{medium_pause}#>', text)

            # Add breathing pauses before conjunctions and transitions
            transitions = ['however', 'therefore', 'moreover', 'furthermore', 'additionally',
                          'first', 'second', 'finally', 'in conclusion', 'for example', 'such as']
            for trans in transitions:
                # Case-insensitive replacement with word boundaries
                pattern = rf'\b({re.escape(trans)})\b'
                text = re.sub(pattern, rf'<#{short_pause}#>\1', text, flags=re.IGNORECASE)

            # Add emphasis pauses before important phrases
            emphasis_phrases = ['the key is', 'importantly', 'notably', 'specifically', 'particularly',
                              'for instance', 'in fact', 'actually']
            for phrase in emphasis_phrases:
                pattern = rf'\b({re.escape(phrase)})\b'
                text = re.sub(pattern, rf'<#{medium_pause}#>\1', text, flags=re.IGNORECASE)

        # Clean up multiple consecutive pauses
        text = re.sub(r'(<#[\d.]+#>)\s*(<#[\d.]+#>)', r'\2', text)

        # Remove pauses at the very beginning or end
        text = re.sub(r'^<#[\d.]+#>\s*', '', text)
        text = re.sub(r'\s*<#[\d.]+#>$', '', text)

        # Clean up extra spaces around pauses
        text = re.sub(r'\s*(<#[\d.]+#>)\s*', r'\1', text)

        return text.strip()

    def _create_fallback_script(self, report: ResearchReport, roles: List[PodcastRole], language: str = "en") -> PodcastScript:
        """Create an enhanced fallback script if generation fails - now with longer, more detailed content"""

        # Try to use enhanced demo script first
        try:
            from demo_data import get_enhanced_demo_script
            enhanced_script = get_enhanced_demo_script(report.topic, language)
            logger.info("Using enhanced demo script as fallback")
            return enhanced_script
        except Exception as e:
            logger.warning(f"Enhanced demo script failed: {e}, using basic fallback")

        # Basic fallback if enhanced demo fails
        lang_voices = self.voice_mappings.get(language, self.voice_mappings["en"])
        
        if language == "zh":
            welcome_text = "大家好！欢迎收听今天的节目。"[:100]
            response_text = "谢谢邀请，很高兴能和大家分享一些看法。"[:100] 
            closing_text = "今天的讨论很有收获，谢谢大家收听！"[:100]
            title = f"深度访谈：{report.topic}"
            description = f"{lang_voices[roles[0]]['name']}与{lang_voices[roles[1]]['name']}的深度对话"
        else:
            welcome_text = "Welcome to today's show, everyone!"[:100]
            response_text = "Thanks for having me. I'm excited to share some insights."[:100]
            closing_text = "This has been a great discussion. Thanks for listening!"[:100]
            title = f"In-Depth: {report.topic}"
            description = f"A conversation between {lang_voices[roles[0]]['name']} and {lang_voices[roles[1]]['name']}"
        
        dialogue = [
            DialogueLine(
                role=roles[0],
                speaker_name=lang_voices[roles[0]]['name'],
                text=welcome_text,
                voice_id=lang_voices[roles[0]]['voice_id'],
                emotion="happy"
            ),
            DialogueLine(
                role=roles[1],
                speaker_name=lang_voices[roles[1]]['name'],
                text=response_text,
                voice_id=lang_voices[roles[1]]['voice_id'],
                emotion="neutral"
            )
        ]
        
        # Add topic-specific content based on key findings
        if language == "zh":
            # Generate more specific content based on the report's key findings
            if report.key_findings:
                topic_lines = [
                    f"关于{report.topic}，根据最新研究，{report.key_findings[0].lower()}",
                    f"特别值得注意的是，{report.key_findings[1].lower() if len(report.key_findings) > 1 else '这个领域正在快速发展'}",
                    f"从实际应用角度来看，{report.topic}对我们日常生活的影响越来越明显。"
                ]
            else:
                # Fallback with more specific language
                topic_lines = [
                    f"关于{report.topic}，我们需要从多个角度来理解这个复杂的主题。",
                    f"在{report.topic}领域，最新的发展趋势显示出巨大的潜力。",
                    f"对于{report.topic}，听众朋友们可能也有自己独特的见解和体验。"
                ]
        else:
            # Generate more specific English content
            if report.key_findings:
                topic_lines = [
                    f"When examining {report.topic}, recent research shows that {report.key_findings[0].lower()}",
                    f"What's particularly interesting is that {report.key_findings[1].lower() if len(report.key_findings) > 1 else 'this field is rapidly evolving'}",
                    f"From a practical standpoint, {report.topic} is having an increasingly visible impact on our daily lives."
                ]
            else:
                # Fallback with more specific language
                topic_lines = [
                    f"When we examine {report.topic}, we need to consider multiple perspectives on this complex subject.",
                    f"In the field of {report.topic}, recent developments show tremendous potential.",
                    f"Regarding {report.topic}, our listeners likely have their own unique insights and experiences."
                ]
        
        for i, line in enumerate(topic_lines):
            speaker_role = roles[i % len(roles)]
            dialogue.append(DialogueLine(
                role=speaker_role,
                speaker_name=lang_voices[speaker_role]['name'],
                text=line[:100],  # Ensure under 100 characters
                voice_id=lang_voices[speaker_role]['voice_id'],
                emotion="neutral"
            ))
        
        # Add closing
        dialogue.append(DialogueLine(
            role=roles[0],
            speaker_name=lang_voices[roles[0]]['name'],
            text=closing_text,
            voice_id=lang_voices[roles[0]]['voice_id'],
            emotion="happy"
        ))
        
        return PodcastScript(
            title=title,
            description=description,
            dialogue=dialogue,
            duration_estimate=120
        )