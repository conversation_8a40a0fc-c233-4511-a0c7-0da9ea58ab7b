"""
TTS Service Factory and Manager
Provides unified interface for multiple TTS providers with configuration-driven selection
"""
import os
import logging
from typing import Dict, List, Optional, Any, Union
from enum import Enum

from .tts_base import TTSServiceBase, TTSVoiceConfig
from .minimax_tts_provider import MinimaxTTSProvider
from .elevenlabs_tts_provider import ElevenLabsTTSProvider
from .elevenlabs_v3_tts_provider import ElevenLabsV3TTSProvider
from ..models.podcast_models import PodcastScript, DialogueLine, TTSProvider

logger = logging.getLogger(__name__)


# Remove local TTSProvider enum since we're importing it from models


class TTSServiceFactory:
    """Factory for creating TTS service instances"""
    
    @staticmethod
    def create_service(provider: Union[TTSProvider, str], config: Dict[str, Any]) -> TTSServiceBase:
        """
        Create TTS service instance
        
        Args:
            provider: TTS provider type
            config: Provider-specific configuration
            
        Returns:
            TTS service instance
            
        Raises:
            ValueError: If provider is not supported
        """
        if isinstance(provider, str):
            try:
                provider = TTSProvider(provider.lower())
            except ValueError:
                raise ValueError(f"Unsupported TTS provider: {provider}")
        
        if provider == TTSProvider.MINIMAX:
            return MinimaxTTSProvider(config)
        elif provider == TTSProvider.ELEVENLABS:
            return ElevenLabsTTSProvider(config)
        elif provider == TTSProvider.ELEVENLABS_V3:
            return ElevenLabsV3TTSProvider(config)
        else:
            raise ValueError(f"Unsupported TTS provider: {provider}")
    
    @staticmethod
    def get_available_providers() -> List[TTSProvider]:
        """Get list of available TTS providers"""
        return list(TTSProvider)


class TTSServiceManager:
    """
    Unified TTS service manager with automatic provider selection and fallback
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize TTS service manager
        
        Args:
            config: Configuration dictionary with provider settings
        """
        self.config = config or {}
        self.services = {}
        self.primary_provider = None
        self.fallback_providers = []
        
        # Load configuration
        self._load_configuration()
        
        # Initialize services
        self._initialize_services()
    
    def _load_configuration(self):
        """Load TTS configuration from environment and config"""
        # Determine primary provider - prefer ElevenLabs for better reliability
        primary = self.config.get("primary_provider") or os.getenv("TTS_PRIMARY_PROVIDER", "elevenlabs")
        try:
            self.primary_provider = TTSProvider(primary.lower())
        except ValueError:
            logger.warning(f"Invalid primary TTS provider: {primary}, defaulting to elevenlabs")
            self.primary_provider = TTSProvider.ELEVENLABS
        
        # Determine fallback providers - improved fallback logic
        fallbacks = self.config.get("fallback_providers", [])
        if not fallbacks:
            # Improved fallback logic - avoid MiniMax when possible
            if self.primary_provider == TTSProvider.MINIMAX:
                fallbacks = ["elevenlabs_v3", "elevenlabs"]
            elif self.primary_provider == TTSProvider.ELEVENLABS_V3:
                fallbacks = ["elevenlabs"]  # Don't fallback to MiniMax for V3
            elif self.primary_provider == TTSProvider.ELEVENLABS:
                fallbacks = ["elevenlabs_v3"]  # Don't fallback to MiniMax for ElevenLabs
            else:
                fallbacks = ["elevenlabs", "elevenlabs_v3"]
        
        for fallback in fallbacks:
            try:
                provider = TTSProvider(fallback.lower())
                if provider != self.primary_provider:
                    self.fallback_providers.append(provider)
            except ValueError:
                logger.warning(f"Invalid fallback TTS provider: {fallback}")
        
        logger.info(f"TTS configuration: primary={self.primary_provider.value}, fallbacks={[p.value for p in self.fallback_providers]}")
    
    def _initialize_services(self):
        """Initialize TTS services based on configuration"""
        all_providers = [self.primary_provider] + self.fallback_providers
        
        for provider in all_providers:
            try:
                # Get provider-specific config
                provider_config = self._get_provider_config(provider)
                
                # Create service instance
                service = TTSServiceFactory.create_service(provider, provider_config)
                self.services[provider] = service
                
                logger.info(f"Initialized {provider.value} TTS service")
                
            except Exception as e:
                logger.error(f"Failed to initialize {provider.value} TTS service: {e}")
    
    def _get_provider_config(self, provider: TTSProvider) -> Dict[str, Any]:
        """Get configuration for specific provider"""
        base_config = {
            "output_dir": self.config.get("output_dir", "./audio_output"),
            "retry_attempts": self.config.get("retry_attempts", 3),
            "retry_delay": self.config.get("retry_delay", 1.0)
        }
        
        if provider == TTSProvider.MINIMAX:
            return {
                **base_config,
                "api_key": self.config.get("minimax_api_key") or os.getenv("MINIMAX_API_KEY"),
                "api_host": self.config.get("minimax_api_host") or os.getenv("MINIMAX_API_HOST", "https://api.minimaxi.chat"),
                "model": self.config.get("minimax_model", "speech-02-hd"),
                "max_text_length": self.config.get("minimax_max_text_length", 100)
            }
        
        elif provider == TTSProvider.ELEVENLABS:
            return {
                **base_config,
                "api_key": self.config.get("elevenlabs_api_key") or os.getenv("ELEVENLABS_API_KEY") or "***************************************************",
                "api_base": self.config.get("elevenlabs_api_base", "https://api.elevenlabs.io/v1"),
                "model_id": self.config.get("elevenlabs_model_id", "eleven_multilingual_v2"),
                "output_format": self.config.get("elevenlabs_output_format", "mp3_44100_128"),
                "max_text_length": self.config.get("elevenlabs_max_text_length", 2500)
            }

        elif provider == TTSProvider.ELEVENLABS_V3:
            return {
                **base_config,
                "api_key": self.config.get("elevenlabs_v3_api_key") or self.config.get("elevenlabs_api_key") or os.getenv("ELEVENLABS_API_KEY") or "***************************************************",
                "api_base": self.config.get("elevenlabs_v3_api_base", "https://api.elevenlabs.io/v1"),
                "model_id": self.config.get("elevenlabs_v3_model_id", "eleven_turbo_v2_5"),
                "output_format": self.config.get("elevenlabs_v3_output_format", "mp3_44100_128"),
                "max_text_length": self.config.get("elevenlabs_v3_max_text_length", 5000)
            }

        return base_config
    
    async def initialize(self) -> bool:
        """
        Initialize all TTS services
        
        Returns:
            bool: True if at least one service initialized successfully
        """
        success_count = 0
        
        for provider, service in self.services.items():
            try:
                if await service.initialize():
                    logger.info(f"{provider.value} TTS service initialized successfully")
                    success_count += 1
                else:
                    logger.error(f"Failed to initialize {provider.value} TTS service")
            except Exception as e:
                logger.error(f"Error initializing {provider.value} TTS service: {e}")
        
        if success_count == 0:
            logger.error("No TTS services could be initialized")
            return False
        
        logger.info(f"Successfully initialized {success_count}/{len(self.services)} TTS services")
        return True
    
    async def get_available_voices(self, language: Optional[str] = None) -> Dict[str, List[TTSVoiceConfig]]:
        """
        Get available voices from all initialized services
        
        Args:
            language: Optional language filter
            
        Returns:
            Dictionary mapping provider names to voice lists
        """
        all_voices = {}
        
        for provider, service in self.services.items():
            try:
                voices = await service.get_available_voices(language)
                all_voices[provider.value] = voices
            except Exception as e:
                logger.error(f"Error getting voices from {provider.value}: {e}")
                all_voices[provider.value] = []
        
        return all_voices
    
    async def synthesize_dialogue_line(
        self, 
        line: DialogueLine, 
        output_prefix: str,
        voice_mapping: Optional[Dict[str, TTSVoiceConfig]] = None,
        preferred_provider: Optional[Union[TTSProvider, str]] = None
    ) -> Dict[str, Any]:
        """
        Synthesize dialogue line with automatic fallback
        
        Args:
            line: DialogueLine to synthesize
            output_prefix: Output file prefix
            voice_mapping: Optional voice configuration mapping
            preferred_provider: Optional preferred provider override
            
        Returns:
            Dictionary with synthesis result and metadata
        """
        # Determine provider order
        providers_to_try = self._get_provider_order(preferred_provider)
        
        last_error = None
        
        for provider in providers_to_try:
            if provider not in self.services:
                continue
            
            service = self.services[provider]
            
            try:
                logger.info(f"Attempting synthesis with {provider.value} for {line.speaker_name}")
                
                result = await service.synthesize_dialogue_line(
                    line=line,
                    output_prefix=output_prefix,
                    voice_mapping=voice_mapping
                )
                
                if result.success:
                    logger.info(f"Successfully synthesized with {provider.value}")
                    return {
                        "success": True,
                        "provider": provider.value,
                        "audio_file": result.audio_file_path,
                        "duration": result.duration_seconds,
                        "metadata": result.metadata
                    }
                else:
                    logger.warning(f"{provider.value} synthesis failed: {result.error_message}")
                    last_error = result.error_message
                    
            except Exception as e:
                logger.error(f"Error with {provider.value} synthesis: {e}")
                last_error = str(e)
        
        # All providers failed
        return {
            "success": False,
            "error": last_error or "All TTS providers failed",
            "providers_tried": [p.value for p in providers_to_try]
        }
    
    async def synthesize_podcast(
        self, 
        script: PodcastScript,
        voice_mapping: Optional[Dict[str, TTSVoiceConfig]] = None,
        preferred_provider: Optional[Union[TTSProvider, str]] = None
    ) -> Dict[str, Any]:
        """
        Synthesize entire podcast with automatic fallback
        
        Args:
            script: PodcastScript to synthesize
            voice_mapping: Optional voice configuration mapping
            preferred_provider: Optional preferred provider override
            
        Returns:
            Dictionary with synthesis results and metadata
        """
        # Determine provider order
        providers_to_try = self._get_provider_order(preferred_provider)
        
        last_error = None
        
        for provider in providers_to_try:
            if provider not in self.services:
                continue
            
            service = self.services[provider]
            
            try:
                logger.info(f"Attempting podcast synthesis with {provider.value}")
                
                result = await service.synthesize_podcast(
                    script=script,
                    voice_mapping=voice_mapping
                )
                
                if result.get("success", False):
                    logger.info(f"Successfully synthesized podcast with {provider.value}")
                    result["provider"] = provider.value
                    return result
                else:
                    logger.warning(f"{provider.value} podcast synthesis failed: {result.get('error', 'Unknown error')}")
                    last_error = result.get("error", "Unknown error")
                    
            except Exception as e:
                logger.error(f"Error with {provider.value} podcast synthesis: {e}")
                last_error = str(e)
        
        # All providers failed
        return {
            "success": False,
            "error": last_error or "All TTS providers failed",
            "providers_tried": [p.value for p in providers_to_try]
        }
    
    def _get_provider_order(self, preferred_provider: Optional[Union[TTSProvider, str]] = None) -> List[TTSProvider]:
        """Get ordered list of providers to try"""
        if preferred_provider:
            if isinstance(preferred_provider, str):
                try:
                    preferred_provider = TTSProvider(preferred_provider.lower())
                except ValueError:
                    logger.warning(f"Invalid preferred provider: {preferred_provider}")
                    preferred_provider = None
            
            if preferred_provider and preferred_provider in self.services:
                # Put preferred provider first
                providers = [preferred_provider]
                providers.extend([p for p in [self.primary_provider] + self.fallback_providers if p != preferred_provider])
                return providers
        
        # Default order: primary then fallbacks
        return [self.primary_provider] + self.fallback_providers
    
    def get_service_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all TTS services"""
        status = {}
        
        for provider, service in self.services.items():
            status[provider.value] = {
                "initialized": service is not None,
                "is_primary": provider == self.primary_provider,
                "is_fallback": provider in self.fallback_providers,
                "config": {
                    "output_dir": str(service.output_dir) if service else None,
                    "max_text_length": getattr(service, 'max_text_length', None),
                    "retry_attempts": getattr(service, 'retry_attempts', None)
                }
            }
        
        return status


# Convenience function for backward compatibility
async def create_tts_service(provider: str = "minimax", config: Optional[Dict[str, Any]] = None) -> TTSServiceBase:
    """
    Create and initialize a single TTS service (backward compatibility)
    
    Args:
        provider: Provider name ("minimax" or "elevenlabs")
        config: Optional configuration
        
    Returns:
        Initialized TTS service
    """
    service = TTSServiceFactory.create_service(provider, config or {})
    await service.initialize()
    return service
