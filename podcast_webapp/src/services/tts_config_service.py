"""
TTS Configuration Service
Provides TTS provider information and configuration for the web interface
"""
import os
import logging
from typing import Dict, List, Optional
from ..models.podcast_models import TTSProvider, TTSProviderInfo

logger = logging.getLogger(__name__)


class TTSConfigService:
    """TTS配置服务，提供TTS提供商信息和配置"""
    
    def __init__(self):
        self._provider_info = self._initialize_provider_info()
        self._check_provider_availability()
    
    def _initialize_provider_info(self) -> Dict[TTSProvider, TTSProviderInfo]:
        """初始化TTS提供商信息"""
        return {
            TTSProvider.MINIMAX: TTSProviderInfo(
                provider=TTSProvider.MINIMAX,
                name="MiniMax TTS",
                description="高质量中英文语音合成，支持自然停顿和情感表达",
                features=[
                    "支持中英文双语",
                    "自然停顿标记 (≤0.3秒)",
                    "情感表达支持",
                    "角色标签自动清理",
                    "快速响应"
                ],
                languages=["zh", "en"],
                quality="high",
                max_text_length=100,
                audio_format="WAV",
                available=True
            ),
            
            TTSProvider.ELEVENLABS: TTSProviderInfo(
                provider=TTSProvider.ELEVENLABS,
                name="ElevenLabs TTS (V2)",
                description="世界领先的AI语音合成，超高质量英文语音",
                features=[
                    "超高质量语音合成",
                    "23+种英文语音",
                    "支持长文本 (2500字符)",
                    "自然情感表达",
                    "多种语音风格"
                ],
                languages=["en"],
                quality="very_high",
                max_text_length=2500,
                audio_format="MP3",
                available=True
            ),

            TTSProvider.ELEVENLABS_V3: TTSProviderInfo(
                provider=TTSProvider.ELEVENLABS_V3,
                name="ElevenLabs V3 (Latest)",
                description="最新V3模型，增强的多语言支持和专业级语音质量",
                features=[
                    "V3增强模型",
                    "专业级语音质量",
                    "多语言支持 (16+种语言)",
                    "高级情感控制",
                    "支持超长文本 (5000字符)",
                    "实时优化",
                    "语音克隆支持"
                ],
                languages=["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh", "ja", "hi", "ko"],
                quality="premium",
                max_text_length=5000,
                audio_format="MP3",
                available=True
            ),
            
            TTSProvider.AUTO: TTSProviderInfo(
                provider=TTSProvider.AUTO,
                name="智能选择",
                description="根据内容自动选择最佳TTS引擎，支持智能降级",
                features=[
                    "智能提供商选择",
                    "自动降级机制",
                    "最佳质量保证",
                    "高可用性",
                    "成本优化"
                ],
                languages=["zh", "en"],
                quality="adaptive",
                max_text_length=2500,
                audio_format="Auto",
                available=True
            )
        }
    
    def _check_provider_availability(self):
        """检查TTS提供商可用性"""
        # MiniMax - 假设总是可用（使用现有的TTS服务）
        # 不检查API密钥，因为现有系统已经有MiniMax集成
        self._provider_info[TTSProvider.MINIMAX].available = True
        logger.info("MiniMax TTS set as available (using existing integration)")

        # 检查ElevenLabs (有硬编码密钥，应该总是可用)
        elevenlabs_key = os.getenv("ELEVENLABS_API_KEY") or "***************************************************"
        if elevenlabs_key:
            self._provider_info[TTSProvider.ELEVENLABS].available = True
            self._provider_info[TTSProvider.ELEVENLABS_V3].available = True
            logger.info("ElevenLabs TTS (V2 & V3) set as available (hardcoded key)")
        else:
            self._provider_info[TTSProvider.ELEVENLABS].available = False
            self._provider_info[TTSProvider.ELEVENLABS_V3].available = False
            logger.warning("ElevenLabs API key not found")

        # 自动选择只有在至少一个提供商可用时才可用
        available_providers = sum(1 for info in self._provider_info.values()
                                if info.provider != TTSProvider.AUTO and info.available)
        if available_providers == 0:
            self._provider_info[TTSProvider.AUTO].available = False
            logger.warning("No TTS providers available, disabling auto selection")
        else:
            self._provider_info[TTSProvider.AUTO].available = True
    
    def get_provider_info(self, provider: TTSProvider) -> Optional[TTSProviderInfo]:
        """获取指定提供商信息"""
        return self._provider_info.get(provider)
    
    def get_all_providers(self) -> List[TTSProviderInfo]:
        """获取所有TTS提供商信息"""
        return list(self._provider_info.values())
    
    def get_available_providers(self) -> List[TTSProviderInfo]:
        """获取可用的TTS提供商"""
        return [info for info in self._provider_info.values() if info.available]
    
    def get_recommended_provider(self, language: str = "en") -> TTSProvider:
        """根据语言推荐最佳TTS提供商"""
        available = self.get_available_providers()
        
        if not available:
            return TTSProvider.MINIMAX  # 默认选择
        
        if language == "zh":
            # 中文优先选择MiniMax
            for info in available:
                if info.provider == TTSProvider.MINIMAX:
                    return TTSProvider.MINIMAX
        elif language == "en":
            # 英文优先选择ElevenLabs
            for info in available:
                if info.provider == TTSProvider.ELEVENLABS:
                    return TTSProvider.ELEVENLABS
        
        # 降级到第一个可用的提供商
        return available[0].provider
    

    
    def validate_provider_for_language(self, provider: TTSProvider, language: str) -> bool:
        """验证提供商是否支持指定语言"""
        info = self.get_provider_info(provider)
        if not info or not info.available:
            return False
        
        if provider == TTSProvider.AUTO:
            return True  # 自动选择支持所有语言
        
        return language in info.languages
    
    def get_fallback_strategy(self, primary_provider: TTSProvider, language: str) -> List[TTSProvider]:
        """获取降级策略"""
        if primary_provider == TTSProvider.AUTO:
            # 自动选择的降级策略
            if language == "zh":
                return [TTSProvider.MINIMAX, TTSProvider.ELEVENLABS]
            else:
                return [TTSProvider.ELEVENLABS, TTSProvider.MINIMAX]
        
        # 指定提供商的降级策略
        fallbacks = []
        available = self.get_available_providers()
        
        for info in available:
            if (info.provider != primary_provider and 
                info.provider != TTSProvider.AUTO and
                language in info.languages):
                fallbacks.append(info.provider)
        
        return fallbacks
    
    def get_provider_status(self) -> Dict[str, bool]:
        """获取所有提供商的状态"""
        return {
            info.name: info.available 
            for info in self._provider_info.values()
        }
    
    def refresh_availability(self):
        """刷新提供商可用性检查"""
        self._check_provider_availability()
        logger.info("TTS provider availability refreshed")


# 全局TTS配置服务实例
tts_config_service = TTSConfigService()
