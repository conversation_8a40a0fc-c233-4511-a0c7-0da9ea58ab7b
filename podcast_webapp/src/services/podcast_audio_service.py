"""
Podcast Audio Service
Integrates the unified TTS interface with the existing podcast generation pipeline
"""
import os
import logging
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

from .tts_factory import TTSServiceManager, TTSProvider
from .tts_base import TTSVoiceConfig
from ..models.podcast_models import PodcastScript, DialogueLine, PodcastRole

logger = logging.getLogger(__name__)


class PodcastAudioService:
    """
    High-level service for generating podcast audio using unified TTS interface
    Maintains backward compatibility with existing podcast generation pipeline
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize podcast audio service
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Initialize TTS manager
        tts_config = self._build_tts_config()
        self.tts_manager = TTSServiceManager(tts_config)
        
        # Voice mapping for backward compatibility
        self._voice_mapping = self._build_voice_mapping()
        
        # Service state
        self._initialized = False
    
    def _build_tts_config(self) -> Dict[str, Any]:
        """Build TTS configuration from service config and environment"""
        tts_config = {
            "output_dir": self.config.get("output_dir", "./audio_output"),
            "retry_attempts": self.config.get("retry_attempts", 3),
            "retry_delay": self.config.get("retry_delay", 1.0)
        }
        
        # Provider selection - prefer ElevenLabs over MiniMax for better reliability
        primary_provider = (
            self.config.get("tts_provider") or
            os.getenv("TTS_PRIMARY_PROVIDER", "elevenlabs")  # Changed default to elevenlabs
        )
        tts_config["primary_provider"] = primary_provider

        # Fallback providers - improved fallback logic
        fallback_providers = self.config.get("tts_fallback_providers", [])
        if not fallback_providers:
            # Improved fallback logic
            if primary_provider == "minimax":
                fallback_providers = ["elevenlabs_v3", "elevenlabs"]
            elif primary_provider == "elevenlabs":
                fallback_providers = ["elevenlabs_v3"]  # Don't fallback to MiniMax for ElevenLabs
            elif primary_provider == "elevenlabs_v3":
                fallback_providers = ["elevenlabs"]  # Don't fallback to MiniMax for V3
            else:
                fallback_providers = ["elevenlabs", "elevenlabs_v3"]
        tts_config["fallback_providers"] = fallback_providers
        
        # Provider-specific settings
        if "minimax" in [primary_provider] + fallback_providers:
            tts_config.update({
                "minimax_api_key": self.config.get("minimax_api_key"),
                "minimax_api_host": self.config.get("minimax_api_host"),
                "minimax_model": self.config.get("minimax_model", "speech-02-hd"),
                "minimax_max_text_length": self.config.get("minimax_max_text_length", 100)
            })
        
        if "elevenlabs" in [primary_provider] + fallback_providers:
            tts_config.update({
                "elevenlabs_api_key": self.config.get("elevenlabs_api_key"),
                "elevenlabs_model_id": self.config.get("elevenlabs_model_id", "eleven_multilingual_v2"),
                "elevenlabs_max_text_length": self.config.get("elevenlabs_max_text_length", 2500)
            })
        
        return tts_config
    
    def _build_voice_mapping(self) -> Dict[str, TTSVoiceConfig]:
        """Build voice mapping for backward compatibility"""
        # This will be populated after TTS services are initialized
        return {}
    
    async def initialize(self) -> bool:
        """
        Initialize the podcast audio service
        
        Returns:
            bool: True if initialization successful
        """
        try:
            # Initialize TTS manager
            if not await self.tts_manager.initialize():
                logger.error("Failed to initialize TTS services")
                return False
            
            # Build voice mapping from available voices
            await self._build_voice_mapping_from_services()
            
            self._initialized = True
            logger.info("Podcast audio service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize podcast audio service: {e}")
            return False
    
    async def _build_voice_mapping_from_services(self):
        """Build voice mapping from initialized TTS services"""
        try:
            # Get available voices from all services
            all_voices = await self.tts_manager.get_available_voices()
            
            # Create role-based voice mapping
            for provider, voices in all_voices.items():
                if not voices:
                    continue
                
                # Separate by language and gender
                zh_female = [v for v in voices if v.language == "zh" and v.gender == "female"]
                zh_male = [v for v in voices if v.language == "zh" and v.gender == "male"]
                en_female = [v for v in voices if v.language == "en" and v.gender == "female"]
                en_male = [v for v in voices if v.language == "en" and v.gender == "male"]
                
                # Map to roles (prefer first available voice of each type)
                if zh_female:
                    self._voice_mapping["zh_host_female"] = zh_female[0]
                    self._voice_mapping["zh_interviewer_female"] = zh_female[0]
                
                if zh_male:
                    self._voice_mapping["zh_expert_male"] = zh_male[0]
                    self._voice_mapping["zh_guest_male"] = zh_male[0]
                
                if en_female:
                    self._voice_mapping["en_host_female"] = en_female[0]
                    self._voice_mapping["en_interviewer_female"] = en_female[0]
                
                if en_male:
                    self._voice_mapping["en_expert_male"] = en_male[0]
                    self._voice_mapping["en_guest_male"] = en_male[0]
                
                # Break after first provider with voices (prefer primary)
                if self._voice_mapping:
                    break
            
            logger.info(f"Built voice mapping with {len(self._voice_mapping)} voice configurations")
            
        except Exception as e:
            logger.warning(f"Failed to build voice mapping: {e}")
    
    async def synthesize_podcast_audio(
        self, 
        script: PodcastScript,
        output_prefix: Optional[str] = None,
        preferred_provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Synthesize audio for entire podcast script (main public interface)
        
        Args:
            script: PodcastScript to synthesize
            output_prefix: Optional prefix for output files
            preferred_provider: Optional preferred TTS provider
            
        Returns:
            Dictionary with synthesis results
        """
        if not self._initialized:
            raise RuntimeError("Service not initialized. Call initialize() first.")
        
        try:
            logger.info(f"Starting podcast audio synthesis: {script.title}")
            
            # Use TTS manager to synthesize
            result = await self.tts_manager.synthesize_podcast(
                script=script,
                voice_mapping=self._voice_mapping,
                preferred_provider=preferred_provider
            )
            
            # Add service-level metadata
            result["service"] = "PodcastAudioService"
            result["script_title"] = script.title
            result["script_language"] = script.language
            
            if result["success"]:
                logger.info(f"Successfully synthesized podcast audio: {script.title}")
            else:
                logger.error(f"Failed to synthesize podcast audio: {result.get('error', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in podcast audio synthesis: {e}")
            return {
                "success": False,
                "error": str(e),
                "service": "PodcastAudioService"
            }
    
    async def synthesize_single_line(
        self,
        line: DialogueLine,
        output_prefix: Optional[str] = None,
        preferred_provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Synthesize audio for a single dialogue line
        
        Args:
            line: DialogueLine to synthesize
            output_prefix: Optional prefix for output file
            preferred_provider: Optional preferred TTS provider
            
        Returns:
            Dictionary with synthesis result
        """
        if not self._initialized:
            raise RuntimeError("Service not initialized. Call initialize() first.")
        
        try:
            # Generate output prefix if not provided
            if not output_prefix:
                safe_speaker = "".join(c for c in line.speaker_name if c.isalnum() or c in (' ', '-', '_')).strip()
                output_prefix = f"line_{safe_speaker.replace(' ', '_')}"
            
            # Use TTS manager to synthesize
            result = await self.tts_manager.synthesize_dialogue_line(
                line=line,
                output_prefix=output_prefix,
                voice_mapping=self._voice_mapping,
                preferred_provider=preferred_provider
            )
            
            # Add service-level metadata
            result["service"] = "PodcastAudioService"
            result["speaker_name"] = line.speaker_name
            result["role"] = line.role.value if hasattr(line.role, 'value') else str(line.role)
            
            return result
            
        except Exception as e:
            logger.error(f"Error synthesizing single line: {e}")
            return {
                "success": False,
                "error": str(e),
                "service": "PodcastAudioService"
            }
    
    def get_available_voices(self, language: Optional[str] = None) -> Dict[str, List[TTSVoiceConfig]]:
        """
        Get available voices from all TTS providers
        
        Args:
            language: Optional language filter
            
        Returns:
            Dictionary mapping provider names to voice lists
        """
        if not self._initialized:
            logger.warning("Service not initialized, returning empty voice list")
            return {}
        
        import asyncio
        return asyncio.run(self.tts_manager.get_available_voices(language))
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of the podcast audio service and underlying TTS services"""
        status = {
            "initialized": self._initialized,
            "voice_mapping_count": len(self._voice_mapping),
            "config": {
                "output_dir": self.config.get("output_dir", "./audio_output"),
                "primary_provider": self.tts_manager.primary_provider.value if self._initialized else None,
                "fallback_providers": [p.value for p in self.tts_manager.fallback_providers] if self._initialized else []
            }
        }
        
        if self._initialized:
            status["tts_services"] = self.tts_manager.get_service_status()
        
        return status
    
    # Backward compatibility methods
    async def synthesize_dialogue_line(
        self,
        line: DialogueLine,
        output_prefix: str = "line"
    ) -> Dict[str, Any]:
        """
        Backward compatibility method for existing code
        
        Args:
            line: DialogueLine to synthesize
            output_prefix: Output file prefix
            
        Returns:
            Dictionary with synthesis result
        """
        return await self.synthesize_single_line(line, output_prefix)
    
    async def generate_podcast_audio(
        self,
        script: PodcastScript,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Backward compatibility method for existing code
        
        Args:
            script: PodcastScript to synthesize
            output_dir: Optional output directory override
            
        Returns:
            Dictionary with synthesis results
        """
        # Temporarily override output directory if specified
        if output_dir:
            original_output_dir = self.tts_manager.config.get("output_dir")
            for service in self.tts_manager.services.values():
                service.output_dir = Path(output_dir)
        
        try:
            result = await self.synthesize_podcast_audio(script)
            return result
        finally:
            # Restore original output directory
            if output_dir and original_output_dir:
                for service in self.tts_manager.services.values():
                    service.output_dir = Path(original_output_dir)


# Convenience function for backward compatibility
async def create_podcast_audio_service(config: Optional[Dict[str, Any]] = None) -> PodcastAudioService:
    """
    Create and initialize podcast audio service
    
    Args:
        config: Optional configuration
        
    Returns:
        Initialized PodcastAudioService
    """
    service = PodcastAudioService(config)
    await service.initialize()
    return service
