"""
ElevenLabs Text-to-Dialogue TTS Provider
Implements the new Text-to-Dialogue API for natural conversation generation
with intelligent model fallback, emotion support, and audio events
"""
import os
import asyncio
import aiofiles
import httpx
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
import re
from enum import Enum

from .tts_base import TTS<PERSON>erviceBase, TTSVoiceConfig, TTSSynthesisRequest, TTSSynthesisResult
from ..models.podcast_models import DialogueLine, PodcastScript, PodcastRole

logger = logging.getLogger(__name__)


class ElevenLabsModel(str, Enum):
    """ElevenLabs model types for fallback"""
    V3 = "eleven_v3"
    V2_MULTILINGUAL = "eleven_multilingual_v2"
    V2_TURBO = "eleven_turbo_v2_5"


class EmotionTag(str, Enum):
    """Supported emotion tags for voice expression"""
    EXCITED = "excited"
    WHISPERING = "whispering"
    SAD = "sad"
    ANGRY = "angry"
    CALM = "calm"
    HAPPY = "happy"
    NEUTRAL = "neutral"
    LAUGHING = "laughing"
    CRYING = "crying"
    SHOUTING = "shouting"
    CONFUSED = "confused"
    SURPRISED = "surprised"


class AudioEventTag(str, Enum):
    """Supported audio event tags for environmental effects"""
    APPLAUSE = "applause"
    FOOTSTEPS = "footsteps"
    DOOR_CLOSING = "door closing"
    DOOR_OPENING = "door opening"
    PHONE_RINGING = "phone ringing"
    BACKGROUND_MUSIC = "background music"
    TYPING = "typing"
    PAPER_RUSTLING = "paper rustling"
    WIND = "wind"
    RAIN = "rain"
    CROWD_NOISE = "crowd noise"
    CAR_ENGINE = "car engine"


class ElevenLabsDialogueTTSProvider(TTSServiceBase):
    """
    ElevenLabs Text-to-Dialogue TTS service implementation
    with intelligent model fallback, emotion support, and audio events
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # API configuration
        self.api_key = config.get("api_key") or os.getenv("ELEVENLABS_API_KEY")
        self.api_base = config.get("api_base", "https://api.elevenlabs.io/v1")

        # Model configuration with fallback support
        self.preferred_model = ElevenLabsModel(config.get("model_id", ElevenLabsModel.V3))
        self.current_model = self.preferred_model
        self.fallback_models = [
            ElevenLabsModel.V2_MULTILINGUAL,
            ElevenLabsModel.V2_TURBO
        ]

        # Format and limits
        self.output_format = config.get("output_format", "mp3_44100_128")
        self.max_text_length = config.get("max_text_length", 10000)  # V3 limit
        self.max_speakers = config.get("max_speakers", 10)

        # Quality settings
        self.seed = config.get("seed")  # For deterministic output
        self.pronunciation_dictionaries = config.get("pronunciation_dictionaries", [])

        # Feature flags
        self.enable_emotion_tags = config.get("enable_emotion_tags", True)
        self.enable_audio_events = config.get("enable_audio_events", True)
        self.preserve_pause_marks = config.get("preserve_pause_marks", True)

        # Service state
        self._initialized = False
        self._v3_available = False
        self._v2_available = False
        self._model_capabilities = {}

        logger.info(f"ElevenLabs Text-to-Dialogue provider initialized with preferred model {self.preferred_model}")
        logger.info(f"Features enabled - Emotions: {self.enable_emotion_tags}, Audio Events: {self.enable_audio_events}")
    
    async def initialize(self) -> bool:
        """
        Initialize the Text-to-Dialogue service with model capability detection
        """
        try:
            if not self.api_key:
                logger.warning("ElevenLabs API key not provided")
                return False

            # Test API access and detect available models
            await self._detect_model_capabilities()

            # Determine the best available model
            if self._v3_available and self.preferred_model == ElevenLabsModel.V3:
                self.current_model = ElevenLabsModel.V3
                logger.info("✅ ElevenLabs v3 model available - Text-to-Dialogue fully supported")
            elif self._v2_available:
                self.current_model = ElevenLabsModel.V2_MULTILINGUAL
                logger.warning("⚠️ ElevenLabs v3 not available - falling back to v2 (limited Text-to-Dialogue features)")
            else:
                logger.error("❌ No compatible ElevenLabs models available")
                return False

            self._initialized = True
            logger.info(f"ElevenLabs Text-to-Dialogue initialized with model: {self.current_model}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize ElevenLabs Text-to-Dialogue: {e}")
            return False

    async def _detect_model_capabilities(self) -> None:
        """Detect which ElevenLabs models are available"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/models",
                    headers={"xi-api-key": self.api_key},
                    timeout=10.0
                )

                if response.status_code == 200:
                    models = response.json()
                    available_models = [model.get("model_id", "") for model in models]

                    # Check v3 availability
                    self._v3_available = ElevenLabsModel.V3 in available_models

                    # Check v2 availability
                    self._v2_available = (
                        ElevenLabsModel.V2_MULTILINGUAL in available_models or
                        ElevenLabsModel.V2_TURBO in available_models
                    )

                    # Store capabilities
                    self._model_capabilities = {
                        ElevenLabsModel.V3: {
                            "available": self._v3_available,
                            "text_to_dialogue": True,
                            "emotion_tags": True,
                            "audio_events": True,
                            "max_characters": 10000
                        },
                        ElevenLabsModel.V2_MULTILINGUAL: {
                            "available": ElevenLabsModel.V2_MULTILINGUAL in available_models,
                            "text_to_dialogue": False,  # V2 doesn't support Text-to-Dialogue API
                            "emotion_tags": False,
                            "audio_events": False,
                            "max_characters": 2500
                        },
                        ElevenLabsModel.V2_TURBO: {
                            "available": ElevenLabsModel.V2_TURBO in available_models,
                            "text_to_dialogue": False,
                            "emotion_tags": False,
                            "audio_events": False,
                            "max_characters": 2500
                        }
                    }

                    logger.info(f"Model availability - v3: {self._v3_available}, v2: {self._v2_available}")

                else:
                    logger.warning(f"Failed to fetch models: {response.status_code}")
                    # Assume basic availability for fallback
                    self._v2_available = True

        except Exception as e:
            logger.error(f"Error detecting model capabilities: {e}")
            # Assume basic availability for fallback
            self._v2_available = True
    
    def is_available(self) -> bool:
        """Check if the service is available"""
        return self._initialized and (self._v3_available or self._v2_available)

    def get_current_model_info(self) -> Dict[str, Any]:
        """Get information about the currently active model"""
        if not self._initialized:
            return {"model": "not_initialized", "capabilities": {}}

        capabilities = self._model_capabilities.get(self.current_model, {})
        return {
            "model": self.current_model,
            "preferred_model": self.preferred_model,
            "is_fallback": self.current_model != self.preferred_model,
            "capabilities": capabilities
        }

    def _extract_emotion_tags(self, text: str) -> Tuple[str, List[str]]:
        """
        Extract emotion tags from text
        Returns: (cleaned_text, emotion_tags)
        """
        emotions = []
        cleaned_text = text

        if not self.enable_emotion_tags:
            return cleaned_text, emotions

        # Pattern to match emotion tags like [excited], [whispering], etc.
        emotion_pattern = r'\[(' + '|'.join([e.value for e in EmotionTag]) + r')\]'

        def extract_emotion(match):
            emotion = match.group(1).lower()
            if emotion in [e.value for e in EmotionTag]:
                emotions.append(emotion)
            return ""  # Remove the tag from text

        cleaned_text = re.sub(emotion_pattern, extract_emotion, cleaned_text, flags=re.IGNORECASE)

        return cleaned_text.strip(), emotions

    def _extract_audio_events(self, text: str) -> Tuple[str, List[str]]:
        """
        Extract audio event tags from text
        Returns: (cleaned_text, audio_events)
        """
        events = []
        cleaned_text = text

        if not self.enable_audio_events:
            return cleaned_text, events

        # Pattern to match audio event tags
        event_values = [e.value.replace(' ', r'\s+') for e in AudioEventTag]
        event_pattern = r'\[(' + '|'.join(event_values) + r')\]'

        def extract_event(match):
            event = match.group(1).lower()
            # Normalize spaces
            event = re.sub(r'\s+', ' ', event)
            if event in [e.value for e in AudioEventTag]:
                events.append(event)
            return ""  # Remove the tag from text

        cleaned_text = re.sub(event_pattern, extract_event, cleaned_text, flags=re.IGNORECASE)

        return cleaned_text.strip(), events

    def _process_pause_marks(self, text: str) -> str:
        """
        Process pause marks and convert them appropriately
        """
        if not self.preserve_pause_marks:
            # Remove pause marks entirely
            return re.sub(r'<#([\d.]+)#>', '', text)

        # Convert pause marks to natural pauses for Text-to-Dialogue
        def convert_pause(match):
            duration = float(match.group(1))
            if duration <= 0.1:
                return ""  # Very short pause, remove
            elif duration <= 0.3:
                return " ... "  # Short pause with spaces
            else:
                return " ... ... "  # Longer pause with spaces

        result = re.sub(r'<#([\d.]+)#>', convert_pause, text)
        # Clean up multiple spaces
        result = re.sub(r'\s+', ' ', result)
        return result.strip()
    
    async def get_available_voices(self, language: Optional[str] = None) -> List[TTSVoiceConfig]:
        """Get available voices (delegates to standard ElevenLabs voices)"""
        # Text-to-Dialogue uses the same voices as regular TTS
        # This is a simplified implementation - in practice, you'd fetch from ElevenLabs
        voices = [
            TTSVoiceConfig(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                name="Rachel (Dialogue)",
                language="en",
                gender="female",
                description="Natural conversational voice optimized for dialogue"
            ),
            TTSVoiceConfig(
                voice_id="ErXwobaYiN019PkySvjV",
                name="Antoni (Dialogue)",
                language="en",
                gender="male",
                description="Professional male voice for dialogue"
            )
        ]
        
        if language:
            voices = [v for v in voices if v.language == language]
        
        return voices
    
    async def synthesize_text(self, request: TTSSynthesisRequest) -> TTSSynthesisResult:
        """
        Single text synthesis (fallback to regular TTS)
        Text-to-Dialogue is optimized for conversations, not single utterances
        """
        logger.info("Single text synthesis requested - using fallback to regular TTS")
        
        # For single text, we create a minimal dialogue
        dialogue_input = [{
            "text": request.text,
            "voice_id": request.voice_config.voice_id
        }]
        
        return await self._synthesize_dialogue_internal(
            dialogue_input,
            request.output_path
        )
    
    async def synthesize_podcast(
        self,
        script: PodcastScript,
        voice_mapping: Optional[Dict[str, TTSVoiceConfig]] = None
    ) -> Dict[str, Any]:
        """
        Synthesize entire podcast using Text-to-Dialogue API with enhanced features
        """
        try:
            if not self.is_available():
                return {
                    "success": False,
                    "error": "Text-to-Dialogue service not available"
                }

            model_info = self.get_current_model_info()
            logger.info(f"🎙️ Starting enhanced Text-to-Dialogue synthesis for: {script.title}")
            logger.info(f"📊 Using model: {model_info['model']} (fallback: {model_info['is_fallback']})")

            # Convert script to dialogue input format with enhancements
            dialogue_segments, script_metadata = self._prepare_dialogue_segments(script, voice_mapping)

            # Log enhancement information
            if script_metadata['has_enhancements']:
                logger.info(f"✨ Script contains enhancements:")
                if script_metadata['emotions_used']:
                    logger.info(f"   🎭 Emotions: {', '.join(script_metadata['emotions_used'])}")
                if script_metadata['audio_events_used']:
                    logger.info(f"   🎵 Audio events: {', '.join(script_metadata['audio_events_used'])}")

                if not script_metadata['supports_enhancements']:
                    logger.warning(f"⚠️ Current model ({model_info['model']}) doesn't support enhancements")

            # Process segments
            audio_files = []
            total_duration = 0.0
            segment_results = []

            for i, segment in enumerate(dialogue_segments):
                logger.info(f"🔄 Processing dialogue segment {i+1}/{len(dialogue_segments)}")

                output_path = str(self.output_dir / f"dialogue_segment_{i+1:03d}.{self._get_file_extension()}")

                result = await self._synthesize_dialogue_internal(segment, output_path)

                if result.success:
                    audio_files.append(result.audio_file_path)
                    if result.duration_seconds:
                        total_duration += result.duration_seconds

                    # Store segment result metadata
                    segment_results.append({
                        "segment": i+1,
                        "success": True,
                        "duration": result.duration_seconds,
                        "metadata": result.metadata
                    })
                else:
                    logger.error(f"❌ Failed to synthesize segment {i+1}: {result.error_message}")
                    return {
                        "success": False,
                        "error": f"Failed to synthesize segment {i+1}: {result.error_message}",
                        "model_info": model_info,
                        "script_metadata": script_metadata
                    }

            # Combine segments if multiple
            final_audio_path = None
            if len(audio_files) == 1:
                final_audio_path = audio_files[0]
            elif len(audio_files) > 1:
                final_audio_path = await self._combine_audio_files(
                    audio_files,
                    str(self.output_dir / f"podcast_{script.title.replace(' ', '_')[:20]}.{self._get_file_extension()}")
                )

            # Determine if any fallback was used
            fallback_used = any(
                segment.get("metadata", {}).get("fallback_used", False)
                for segment in segment_results
            )

            # Prepare comprehensive result
            result = {
                "success": True,
                "audio_file_path": final_audio_path,
                "duration_seconds": total_duration,
                "provider": "elevenlabs_dialogue",
                "model_used": model_info['model'],
                "preferred_model": model_info['preferred_model'],
                "is_model_fallback": model_info['is_fallback'],
                "fallback_used": fallback_used,
                "segments_processed": len(dialogue_segments),
                "enhancements": {
                    "supported": script_metadata['supports_enhancements'],
                    "used": script_metadata['has_enhancements'],
                    "emotions": script_metadata['emotions_used'],
                    "audio_events": script_metadata['audio_events_used']
                },
                "metadata": {
                    "title": script.title,
                    "dialogue_lines": len(script.dialogue),
                    "script_metadata": script_metadata,
                    "model_info": model_info,
                    "segment_results": segment_results
                }
            }

            # Add user-friendly messages
            if model_info['is_fallback']:
                result["user_message"] = f"⚠️ Using {model_info['model']} (v3 not available)"
            elif fallback_used:
                result["user_message"] = "⚠️ Some advanced features unavailable, used traditional TTS"
            elif script_metadata['has_enhancements']:
                result["user_message"] = "✨ Enhanced dialogue with emotions and audio events"
            else:
                result["user_message"] = "✅ High-quality dialogue generated"

            logger.info(f"🎉 Text-to-Dialogue synthesis completed successfully")
            logger.info(f"📊 Final result: {result['user_message']}")

            return result

        except Exception as e:
            logger.error(f"💥 Error in Text-to-Dialogue synthesis: {e}")
            return {
                "success": False,
                "error": str(e),
                "model_info": self.get_current_model_info() if hasattr(self, '_initialized') and self._initialized else {}
            }
    
    def _prepare_dialogue_segments(
        self,
        script: PodcastScript,
        voice_mapping: Optional[Dict[str, TTSVoiceConfig]] = None
    ) -> Tuple[List[List[Dict[str, Any]]], Dict[str, Any]]:
        """
        Convert PodcastScript to Text-to-Dialogue API format with enhanced features
        Returns: (segments, metadata)
        """
        segments = []
        current_segment = []
        current_length = 0

        # Track enhancements across the script
        total_emotions = set()
        total_audio_events = set()
        has_any_enhancements = False

        # Get current model capabilities
        model_info = self.get_current_model_info()
        supports_enhancements = model_info['capabilities'].get('text_to_dialogue', False)

        for line in script.dialogue:
            # Get voice for this line
            voice_config = self._get_voice_for_line(line, voice_mapping)

            # Clean and prepare text with enhancement support
            text_result = self._clean_text_for_dialogue(
                line.text,
                preserve_tags=supports_enhancements
            )

            # Track enhancements
            if text_result['emotions']:
                total_emotions.update(text_result['emotions'])
                has_any_enhancements = True
            if text_result['audio_events']:
                total_audio_events.update(text_result['audio_events'])
                has_any_enhancements = True

            # Create dialogue input with enhancements
            dialogue_input = {
                "text": text_result['text'],
                "voice_id": voice_config.voice_id
            }

            # Add enhancements if supported by current model
            if supports_enhancements and text_result['has_enhancements']:
                if text_result['emotions']:
                    dialogue_input["emotions"] = text_result['emotions']
                if text_result['audio_events']:
                    dialogue_input["audio_events"] = text_result['audio_events']

            # Check if adding this line would exceed limits
            line_length = len(text_result['text'])
            max_length = model_info['capabilities'].get('max_characters', self.max_text_length)

            if (current_length + line_length > max_length or
                len(current_segment) >= self.max_speakers) and current_segment:

                # Start new segment
                segments.append(current_segment)
                current_segment = [dialogue_input]
                current_length = line_length
            else:
                current_segment.append(dialogue_input)
                current_length += line_length

        # Add final segment
        if current_segment:
            segments.append(current_segment)

        # Prepare metadata
        metadata = {
            "total_segments": len(segments),
            "model_used": self.current_model,
            "is_fallback": model_info['is_fallback'],
            "supports_enhancements": supports_enhancements,
            "has_enhancements": has_any_enhancements,
            "emotions_used": list(total_emotions),
            "audio_events_used": list(total_audio_events),
            "total_dialogue_lines": len(script.dialogue)
        }

        logger.info(f"Created {len(segments)} dialogue segments using {self.current_model}")
        if has_any_enhancements and not supports_enhancements:
            logger.warning("⚠️ Script contains emotion/audio tags but current model doesn't support them")
        elif has_any_enhancements:
            logger.info(f"✨ Enhanced dialogue with {len(total_emotions)} emotions and {len(total_audio_events)} audio events")

        return segments, metadata
    
    def _clean_text_for_dialogue(self, text: str, preserve_tags: bool = True) -> Dict[str, Any]:
        """
        Clean text for Text-to-Dialogue API with emotion and audio event support
        Returns: {
            'text': cleaned_text,
            'emotions': [list of emotions],
            'audio_events': [list of audio events],
            'has_enhancements': bool
        }
        """
        original_text = text
        result = {
            'text': text,
            'emotions': [],
            'audio_events': [],
            'has_enhancements': False
        }

        # Step 1: Extract emotion tags if enabled
        if preserve_tags and self.enable_emotion_tags:
            text, emotions = self._extract_emotion_tags(text)
            result['emotions'] = emotions
            if emotions:
                result['has_enhancements'] = True
                logger.debug(f"Extracted emotions: {emotions}")

        # Step 2: Extract audio event tags if enabled
        if preserve_tags and self.enable_audio_events:
            text, audio_events = self._extract_audio_events(text)
            result['audio_events'] = audio_events
            if audio_events:
                result['has_enhancements'] = True
                logger.debug(f"Extracted audio events: {audio_events}")

        # Step 3: Remove role labels (but preserve emotion/audio tags if they weren't extracted)
        if not preserve_tags:
            # Remove all tags including emotions and audio events
            text = re.sub(r'\[[\u4e00-\u9fff\w\s]+\]', '', text)
            text = re.sub(r'\[[A-Za-z\s]+\]', '', text)
        else:
            # Only remove role labels, keep unrecognized tags
            role_patterns = [
                r'\[主持人\]', r'\[嘉宾\]', r'\[专家\]', r'\[客人\]',  # Chinese roles
                r'\[host\]', r'\[guest\]', r'\[expert\]', r'\[speaker\]'  # English roles
            ]
            for pattern in role_patterns:
                text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        # Step 4: Process pause marks
        text = self._process_pause_marks(text)

        # Step 5: Clean up extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        result['text'] = text

        # Log if we made significant changes
        if original_text != text:
            logger.debug(f"Text cleaned: '{original_text[:50]}...' -> '{text[:50]}...'")

        return result
    
    async def _synthesize_dialogue_internal(
        self,
        dialogue_inputs: List[Dict[str, Any]],
        output_path: str
    ) -> TTSSynthesisResult:
        """
        Internal method to call Text-to-Dialogue API with intelligent fallback
        """
        model_info = self.get_current_model_info()

        # Try Text-to-Dialogue API if v3 is available
        if model_info['capabilities'].get('text_to_dialogue', False):
            result = await self._try_text_to_dialogue_api(dialogue_inputs, output_path)
            if result.success:
                return result
            else:
                logger.warning(f"Text-to-Dialogue API failed: {result.error_message}")
                # Try fallback to traditional TTS
                return await self._fallback_to_traditional_tts(dialogue_inputs, output_path)
        else:
            # Use traditional TTS approach for v2 models
            logger.info("Using traditional TTS approach (v2 model)")
            return await self._fallback_to_traditional_tts(dialogue_inputs, output_path)

    async def _try_text_to_dialogue_api(
        self,
        dialogue_inputs: List[Dict[str, Any]],
        output_path: str
    ) -> TTSSynthesisResult:
        """Try the Text-to-Dialogue API"""
        try:
            # Prepare payload for Text-to-Dialogue API
            api_inputs = []
            for input_data in dialogue_inputs:
                api_input = {
                    "text": input_data["text"],
                    "voice_id": input_data["voice_id"]
                }

                # Add emotion tags if present and supported
                if "emotions" in input_data and input_data["emotions"]:
                    # Format emotions for API
                    emotion_text = input_data["text"]
                    for emotion in input_data["emotions"]:
                        emotion_text = f"[{emotion}] {emotion_text}"
                    api_input["text"] = emotion_text

                # Add audio events if present and supported
                if "audio_events" in input_data and input_data["audio_events"]:
                    # Format audio events for API
                    for event in input_data["audio_events"]:
                        api_input["text"] = f"[{event}] {api_input['text']}"

                api_inputs.append(api_input)

            payload = {
                "inputs": api_inputs,
                "model_id": self.current_model
            }

            # Add optional parameters
            if self.seed is not None:
                payload["seed"] = self.seed

            if self.pronunciation_dictionaries:
                payload["pronunciation_dictionary_locators"] = self.pronunciation_dictionaries

            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json"
            }

            params = {
                "output_format": self.output_format
            }

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.api_base}/text-to-dialogue",
                    headers=headers,
                    params=params,
                    json=payload
                )

            if response.status_code == 200:
                # Save audio file
                async with aiofiles.open(output_path, 'wb') as f:
                    await f.write(response.content)

                # Get duration
                duration = await self._get_audio_duration(output_path)

                return TTSSynthesisResult(
                    success=True,
                    audio_file_path=output_path,
                    duration_seconds=duration,
                    metadata={
                        "provider": "elevenlabs_dialogue",
                        "model": self.current_model,
                        "api_type": "text_to_dialogue",
                        "dialogue_lines": len(dialogue_inputs),
                        "has_enhancements": any("emotions" in inp or "audio_events" in inp for inp in dialogue_inputs)
                    }
                )
            else:
                error_msg = f"Text-to-Dialogue API error {response.status_code}: {response.text}"
                logger.error(error_msg)
                return TTSSynthesisResult(
                    success=False,
                    error_message=error_msg
                )

        except Exception as e:
            logger.error(f"Error in Text-to-Dialogue API call: {e}")
            return TTSSynthesisResult(
                success=False,
                error_message=str(e)
            )
    
    async def _fallback_to_traditional_tts(
        self,
        dialogue_inputs: List[Dict[str, Any]],
        output_path: str
    ) -> TTSSynthesisResult:
        """
        Fallback to traditional TTS when Text-to-Dialogue is not available
        """
        try:
            logger.info("🔄 Falling back to traditional TTS synthesis")

            audio_files = []
            total_duration = 0.0

            for i, input_data in enumerate(dialogue_inputs):
                # Create individual audio file for each line
                line_output_path = output_path.replace(
                    f".{self._get_file_extension()}",
                    f"_line_{i+1:03d}.{self._get_file_extension()}"
                )

                # Use traditional TTS API
                result = await self._synthesize_single_line_traditional(
                    input_data["text"],
                    input_data["voice_id"],
                    line_output_path
                )

                if result.success:
                    audio_files.append(result.audio_file_path)
                    if result.duration_seconds:
                        total_duration += result.duration_seconds
                else:
                    logger.error(f"Failed to synthesize line {i+1}: {result.error_message}")
                    return TTSSynthesisResult(
                        success=False,
                        error_message=f"Traditional TTS fallback failed on line {i+1}: {result.error_message}"
                    )

            # Combine audio files
            if len(audio_files) == 1:
                final_audio_path = audio_files[0]
            else:
                final_audio_path = await self._combine_audio_files(audio_files, output_path)

            return TTSSynthesisResult(
                success=True,
                audio_file_path=final_audio_path,
                duration_seconds=total_duration,
                metadata={
                    "provider": "elevenlabs_dialogue",
                    "model": self.current_model,
                    "api_type": "traditional_tts_fallback",
                    "dialogue_lines": len(dialogue_inputs),
                    "fallback_used": True,
                    "warning": "Text-to-Dialogue features not available with current model"
                }
            )

        except Exception as e:
            logger.error(f"Error in traditional TTS fallback: {e}")
            return TTSSynthesisResult(
                success=False,
                error_message=f"Traditional TTS fallback failed: {str(e)}"
            )

    async def _synthesize_single_line_traditional(
        self,
        text: str,
        voice_id: str,
        output_path: str
    ) -> TTSSynthesisResult:
        """Synthesize a single line using traditional TTS API"""
        try:
            payload = {
                "text": text,
                "model_id": self.current_model,
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.75
                }
            }

            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json"
            }

            params = {
                "output_format": self.output_format
            }

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.api_base}/text-to-speech/{voice_id}",
                    headers=headers,
                    params=params,
                    json=payload
                )

            if response.status_code == 200:
                # Save audio file
                async with aiofiles.open(output_path, 'wb') as f:
                    await f.write(response.content)

                duration = await self._get_audio_duration(output_path)

                return TTSSynthesisResult(
                    success=True,
                    audio_file_path=output_path,
                    duration_seconds=duration
                )
            else:
                error_msg = f"Traditional TTS API error {response.status_code}: {response.text}"
                return TTSSynthesisResult(
                    success=False,
                    error_message=error_msg
                )

        except Exception as e:
            return TTSSynthesisResult(
                success=False,
                error_message=str(e)
            )

    def _get_file_extension(self) -> str:
        """Get file extension based on output format"""
        if "mp3" in self.output_format:
            return "mp3"
        elif "pcm" in self.output_format:
            return "wav"
        else:
            return "mp3"  # Default
    
    # Required abstract methods implementation
    def _get_default_voice_config(self, line: DialogueLine) -> TTSVoiceConfig:
        """Get default voice configuration for a dialogue line"""
        # Default to Rachel for English, could be enhanced for other languages
        return TTSVoiceConfig(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            name="Rachel (Dialogue Default)",
            language="en",
            gender="female",
            description="Default voice for Text-to-Dialogue"
        )
    
    async def _combine_audio_files(self, audio_files: List[str], output_path: str) -> str:
        """Combine multiple audio files (simplified implementation)"""
        # For now, return first file - in production, use audio processing library
        logger.warning("Audio combination not implemented, returning first file")
        return audio_files[0] if audio_files else output_path
    
    async def _get_audio_duration(self, audio_file_path: str) -> Optional[float]:
        """Get audio duration (simplified implementation)"""
        # Return estimated duration - in production, use audio processing library
        return 10.0  # Placeholder
