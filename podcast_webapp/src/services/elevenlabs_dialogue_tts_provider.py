"""
ElevenLabs Text-to-Dialogue TTS Provider
Implements the new Text-to-Dialogue API for natural conversation generation
"""
import os
import asyncio
import aiofiles
import httpx
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import re

from .tts_base import TTSServiceBase, TTSVoiceConfig, TTSSynthesisRequest, TTSSynthesisResult
from ..models.podcast_models import DialogueLine, PodcastScript, PodcastRole

logger = logging.getLogger(__name__)


class ElevenLabsDialogueTTSProvider(TTSServiceBase):
    """ElevenLabs Text-to-Dialogue TTS service implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # API configuration
        self.api_key = config.get("api_key") or os.getenv("ELEVENLABS_API_KEY")
        self.api_base = config.get("api_base", "https://api.elevenlabs.io/v1")
        
        # Text-to-Dialogue specific settings
        self.model_id = config.get("model_id", "eleven_v3")
        self.output_format = config.get("output_format", "mp3_44100_128")
        self.max_text_length = config.get("max_text_length", 10000)  # V3 limit
        self.max_speakers = config.get("max_speakers", 10)  # Reasonable limit
        
        # Quality settings
        self.seed = config.get("seed")  # For deterministic output
        self.pronunciation_dictionaries = config.get("pronunciation_dictionaries", [])
        
        # Service state
        self._initialized = False
        self._available = False
        
        logger.info(f"ElevenLabs Text-to-Dialogue provider initialized with model {self.model_id}")
    
    async def initialize(self) -> bool:
        """Initialize the Text-to-Dialogue service"""
        try:
            if not self.api_key:
                logger.warning("ElevenLabs API key not provided")
                return False
            
            # Test API access
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/models",
                    headers={"xi-api-key": self.api_key},
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    models = response.json()
                    # Check if v3 model is available
                    v3_available = any(model.get("model_id") == "eleven_v3" for model in models)
                    if v3_available:
                        self._available = True
                        logger.info("ElevenLabs Text-to-Dialogue API access confirmed")
                    else:
                        logger.warning("Eleven v3 model not available in account")
                        return False
                else:
                    logger.warning(f"ElevenLabs API test failed: {response.status_code}")
                    return False
            
            self._initialized = True
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize ElevenLabs Text-to-Dialogue: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if the service is available"""
        return self._initialized and self._available
    
    async def get_available_voices(self, language: Optional[str] = None) -> List[TTSVoiceConfig]:
        """Get available voices (delegates to standard ElevenLabs voices)"""
        # Text-to-Dialogue uses the same voices as regular TTS
        # This is a simplified implementation - in practice, you'd fetch from ElevenLabs
        voices = [
            TTSVoiceConfig(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                name="Rachel (Dialogue)",
                language="en",
                gender="female",
                description="Natural conversational voice optimized for dialogue"
            ),
            TTSVoiceConfig(
                voice_id="ErXwobaYiN019PkySvjV",
                name="Antoni (Dialogue)",
                language="en",
                gender="male",
                description="Professional male voice for dialogue"
            )
        ]
        
        if language:
            voices = [v for v in voices if v.language == language]
        
        return voices
    
    async def synthesize_text(self, request: TTSSynthesisRequest) -> TTSSynthesisResult:
        """
        Single text synthesis (fallback to regular TTS)
        Text-to-Dialogue is optimized for conversations, not single utterances
        """
        logger.info("Single text synthesis requested - using fallback to regular TTS")
        
        # For single text, we create a minimal dialogue
        dialogue_input = [{
            "text": request.text,
            "voice_id": request.voice_config.voice_id
        }]
        
        return await self._synthesize_dialogue_internal(
            dialogue_input,
            request.output_path
        )
    
    async def synthesize_podcast(
        self, 
        script: PodcastScript,
        voice_mapping: Optional[Dict[str, TTSVoiceConfig]] = None
    ) -> Dict[str, Any]:
        """
        Synthesize entire podcast using Text-to-Dialogue API
        This is the main advantage of this provider
        """
        try:
            if not self.is_available():
                return {
                    "success": False,
                    "error": "Text-to-Dialogue service not available"
                }
            
            logger.info(f"Starting Text-to-Dialogue synthesis for: {script.title}")
            
            # Convert script to dialogue input format
            dialogue_segments = self._prepare_dialogue_segments(script, voice_mapping)
            
            # Process segments (handle length limits)
            audio_files = []
            total_duration = 0.0
            
            for i, segment in enumerate(dialogue_segments):
                logger.info(f"Processing dialogue segment {i+1}/{len(dialogue_segments)}")
                
                output_path = str(self.output_dir / f"dialogue_segment_{i+1:03d}.{self._get_file_extension()}")
                
                result = await self._synthesize_dialogue_internal(segment, output_path)
                
                if result.success:
                    audio_files.append(result.audio_file_path)
                    if result.duration_seconds:
                        total_duration += result.duration_seconds
                else:
                    return {
                        "success": False,
                        "error": f"Failed to synthesize segment {i+1}: {result.error_message}"
                    }
            
            # Combine segments if multiple
            final_audio_path = None
            if len(audio_files) == 1:
                final_audio_path = audio_files[0]
            elif len(audio_files) > 1:
                final_audio_path = await self._combine_audio_files(
                    audio_files, 
                    str(self.output_dir / f"podcast_{script.title.replace(' ', '_')[:20]}.{self._get_file_extension()}")
                )
            
            return {
                "success": True,
                "audio_file_path": final_audio_path,
                "duration_seconds": total_duration,
                "segments": len(dialogue_segments),
                "provider": "elevenlabs_dialogue",
                "model": self.model_id,
                "metadata": {
                    "title": script.title,
                    "dialogue_lines": len(script.dialogue),
                    "segments_processed": len(dialogue_segments)
                }
            }
            
        except Exception as e:
            logger.error(f"Error in Text-to-Dialogue synthesis: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _prepare_dialogue_segments(
        self, 
        script: PodcastScript, 
        voice_mapping: Optional[Dict[str, TTSVoiceConfig]] = None
    ) -> List[List[Dict[str, str]]]:
        """
        Convert PodcastScript to Text-to-Dialogue API format
        Handles text length limits by creating segments
        """
        segments = []
        current_segment = []
        current_length = 0
        
        for line in script.dialogue:
            # Get voice for this line
            voice_config = self._get_voice_for_line(line, voice_mapping)
            
            # Clean and prepare text
            cleaned_text = self._clean_text_for_dialogue(line.text)
            
            # Create dialogue input
            dialogue_input = {
                "text": cleaned_text,
                "voice_id": voice_config.voice_id
            }
            
            # Check if adding this line would exceed limits
            line_length = len(cleaned_text)
            if (current_length + line_length > self.max_text_length or 
                len(current_segment) >= self.max_speakers) and current_segment:
                
                # Start new segment
                segments.append(current_segment)
                current_segment = [dialogue_input]
                current_length = line_length
            else:
                current_segment.append(dialogue_input)
                current_length += line_length
        
        # Add final segment
        if current_segment:
            segments.append(current_segment)
        
        logger.info(f"Created {len(segments)} dialogue segments")
        return segments
    
    def _clean_text_for_dialogue(self, text: str) -> str:
        """
        Clean text for Text-to-Dialogue API
        Convert our pause marks to ElevenLabs audio tags
        """
        # Remove role labels
        text = re.sub(r'\[[\u4e00-\u9fff\w\s]+\]', '', text)
        text = re.sub(r'\[[A-Za-z\s]+\]', '', text)
        
        # Convert pause marks to natural pauses (Text-to-Dialogue handles this better)
        # Our <#0.3#> becomes natural speech pauses
        text = re.sub(r'<#([\d.]+)#>', lambda m: '...' if float(m.group(1)) <= 0.3 else '', text)
        
        # Clean up extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    async def _synthesize_dialogue_internal(
        self, 
        dialogue_inputs: List[Dict[str, str]], 
        output_path: str
    ) -> TTSSynthesisResult:
        """Internal method to call Text-to-Dialogue API"""
        try:
            payload = {
                "inputs": dialogue_inputs,
                "model_id": self.model_id
            }
            
            # Add optional parameters
            if self.seed is not None:
                payload["seed"] = self.seed
            
            if self.pronunciation_dictionaries:
                payload["pronunciation_dictionary_locators"] = self.pronunciation_dictionaries
            
            headers = {
                "xi-api-key": self.api_key,
                "Content-Type": "application/json"
            }
            
            params = {
                "output_format": self.output_format
            }
            
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.api_base}/text-to-dialogue",
                    headers=headers,
                    params=params,
                    json=payload
                )
            
            if response.status_code == 200:
                # Save audio file
                async with aiofiles.open(output_path, 'wb') as f:
                    await f.write(response.content)
                
                # Get duration (simplified)
                duration = await self._get_audio_duration(output_path)
                
                return TTSSynthesisResult(
                    success=True,
                    audio_file_path=output_path,
                    duration_seconds=duration,
                    metadata={
                        "provider": "elevenlabs_dialogue",
                        "model": self.model_id,
                        "dialogue_lines": len(dialogue_inputs)
                    }
                )
            else:
                error_msg = f"API error {response.status_code}: {response.text}"
                logger.error(error_msg)
                return TTSSynthesisResult(
                    success=False,
                    error_message=error_msg
                )
                
        except Exception as e:
            logger.error(f"Error in dialogue synthesis: {e}")
            return TTSSynthesisResult(
                success=False,
                error_message=str(e)
            )
    
    def _get_file_extension(self) -> str:
        """Get file extension based on output format"""
        if "mp3" in self.output_format:
            return "mp3"
        elif "pcm" in self.output_format:
            return "wav"
        else:
            return "mp3"  # Default
    
    # Required abstract methods implementation
    def _get_default_voice_config(self, line: DialogueLine) -> TTSVoiceConfig:
        """Get default voice configuration for a dialogue line"""
        # Default to Rachel for English, could be enhanced for other languages
        return TTSVoiceConfig(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            name="Rachel (Dialogue Default)",
            language="en",
            gender="female",
            description="Default voice for Text-to-Dialogue"
        )
    
    async def _combine_audio_files(self, audio_files: List[str], output_path: str) -> str:
        """Combine multiple audio files (simplified implementation)"""
        # For now, return first file - in production, use audio processing library
        logger.warning("Audio combination not implemented, returning first file")
        return audio_files[0] if audio_files else output_path
    
    async def _get_audio_duration(self, audio_file_path: str) -> Optional[float]:
        """Get audio duration (simplified implementation)"""
        # Return estimated duration - in production, use audio processing library
        return 10.0  # Placeholder
