"""
MiniMax TTS Service Implementation
Implements the abstract TTS interface for MiniMax API
"""
import os
import asyncio
import aiofiles
import httpx
import json
import base64
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

from .tts_base import TTSServiceBase, TTSVoiceConfig, TTSSynthesisRequest, TTSSynthesisResult
from ..models.podcast_models import DialogueLine, PodcastRole
from ..utils.audio_utils import build_output_path, build_output_file

logger = logging.getLogger(__name__)


class MinimaxTTSProvider(TTSServiceBase):
    """MiniMax TTS service implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        self.api_key = config.get("api_key") or os.getenv("MINIMAX_API_KEY")
        self.api_host = config.get("api_host") or os.getenv("MINIMAX_API_HOST", "https://api.minimaxi.chat")

        # Don't raise error here - let initialization handle it gracefully
        self._api_key_available = bool(self.api_key)
        
        # MiniMax specific settings
        self.max_text_length = config.get("max_text_length", 100)  # Conservative for URL encoding
        self.model = config.get("model", "speech-02-hd")
        
        # Voice mappings for different roles and languages
        self._voice_configs = self._initialize_voice_configs()
    
    def _initialize_voice_configs(self) -> Dict[str, TTSVoiceConfig]:
        """Initialize MiniMax voice configurations"""
        return {
            # Chinese voices
            "zh_host_female": TTSVoiceConfig(
                voice_id="Chinese (Mandarin)_Wise_Women",
                name="智慧女声",
                language="zh",
                gender="female",
                description="温暖、引导性的中文女声，适合主持人"
            ),
            "zh_expert_male": TTSVoiceConfig(
                voice_id="Chinese (Mandarin)_Reliable_Executive",
                name="可靠男声",
                language="zh", 
                gender="male",
                description="专业、可靠的中文男声，适合专家"
            ),
            "zh_interviewer_female": TTSVoiceConfig(
                voice_id="Chinese (Mandarin)_Gentle_Women",
                name="温柔女声",
                language="zh",
                gender="female", 
                description="温柔、亲和的中文女声，适合采访者"
            ),
            "zh_guest_male": TTSVoiceConfig(
                voice_id="Chinese (Mandarin)_Calm_Man",
                name="沉稳男声",
                language="zh",
                gender="male",
                description="沉稳、专业的中文男声，适合嘉宾"
            ),
            
            # English voices  
            "en_host_female": TTSVoiceConfig(
                voice_id="English (US)_Confident_Women",
                name="Confident Female",
                language="en",
                gender="female",
                description="Confident, engaging English female voice for hosts"
            ),
            "en_expert_male": TTSVoiceConfig(
                voice_id="English (US)_Professional_Man",
                name="Professional Male", 
                language="en",
                gender="male",
                description="Professional, authoritative English male voice for experts"
            ),
            "en_interviewer_female": TTSVoiceConfig(
                voice_id="English (US)_Warm_Women",
                name="Warm Female",
                language="en", 
                gender="female",
                description="Warm, friendly English female voice for interviewers"
            ),
            "en_guest_male": TTSVoiceConfig(
                voice_id="English (US)_Thoughtful_Man",
                name="Thoughtful Male",
                language="en",
                gender="male", 
                description="Thoughtful, articulate English male voice for guests"
            )
        }
    
    async def initialize(self) -> bool:
        """Initialize MiniMax TTS service"""
        try:
            # Check if API key is available
            if not self._api_key_available:
                logger.warning("MiniMax API key not available, service will not be functional")
                return False

            # Test API connection with a simple request
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Extract Group ID from JWT token if needed
            try:
                payload_b64 = self.api_key.split('.')[1]
                payload_b64 += '=' * (4 - len(payload_b64) % 4)
                jwt_payload = json.loads(base64.b64decode(payload_b64))
                group_id = jwt_payload.get("GroupID")
                if group_id:
                    headers["X-Group-ID"] = group_id
            except Exception:
                pass  # Continue without Group ID if extraction fails
            
            async with httpx.AsyncClient() as client:
                # Test with minimal request
                test_payload = {
                    "model": self.model,
                    "text": "test",
                    "voice_id": "Chinese (Mandarin)_Wise_Women",
                    "speed": 1.0,
                    "vol": 1.0,
                    "pitch": 0
                }
                
                response = await client.post(
                    f"{self.api_host}/v1/t2a_v2",
                    headers=headers,
                    json=test_payload,
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    logger.info("MiniMax TTS service initialized successfully")
                    return True
                else:
                    logger.error(f"MiniMax TTS initialization failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"Failed to initialize MiniMax TTS service: {e}")
            return False
    
    async def get_available_voices(self, language: Optional[str] = None) -> List[TTSVoiceConfig]:
        """Get available MiniMax voices"""
        voices = list(self._voice_configs.values())
        
        if language:
            voices = [v for v in voices if v.language == language]
        
        return voices
    
    async def synthesize_text(self, request: TTSSynthesisRequest) -> TTSSynthesisResult:
        """Synthesize text using MiniMax API"""
        try:
            # Validate input text
            if not request.text or not request.text.strip():
                logger.warning("Empty text provided to MiniMax TTS")
                return TTSSynthesisResult(
                    success=False,
                    error_message="Empty text provided for synthesis"
                )

            # Clean text for TTS
            cleaned_text = self._clean_text_for_tts(request.text)
            if not cleaned_text or not cleaned_text.strip():
                logger.warning(f"Text became empty after cleaning: '{request.text}' -> '{cleaned_text}'")
                return TTSSynthesisResult(
                    success=False,
                    error_message="Text became empty after cleaning"
                )

            # Prepare text (handle chunking if needed)
            text_chunks = self._chunk_text(cleaned_text)
            audio_files = []
            
            for i, chunk in enumerate(text_chunks):
                chunk_result = await self._synthesize_chunk(
                    text=chunk,
                    voice_config=request.voice_config,
                    output_path=request.output_path if len(text_chunks) == 1 else f"{request.output_path}_chunk_{i}",
                    emotion=request.emotion,
                    speed=request.speed,
                    pitch=request.pitch,
                    volume=request.volume
                )
                
                if chunk_result.success:
                    audio_files.append(chunk_result.audio_file_path)
                else:
                    return chunk_result  # Return first failure
            
            # If multiple chunks, combine them
            if len(audio_files) > 1:
                combined_path = await self._combine_audio_chunks(audio_files, request.output_path)
                return TTSSynthesisResult(
                    success=True,
                    audio_file_path=combined_path,
                    metadata={"chunks": len(audio_files)}
                )
            elif len(audio_files) == 1:
                return TTSSynthesisResult(
                    success=True,
                    audio_file_path=audio_files[0]
                )
            else:
                return TTSSynthesisResult(
                    success=False,
                    error_message="No audio chunks were successfully synthesized"
                )
                
        except Exception as e:
            logger.error(f"Error in MiniMax text synthesis: {e}")
            return TTSSynthesisResult(
                success=False,
                error_message=str(e)
            )
    
    async def _synthesize_chunk(
        self,
        text: str,
        voice_config: TTSVoiceConfig,
        output_path: str,
        emotion: str = "neutral",
        speed: float = 1.0,
        pitch: float = 1.0,
        volume: float = 1.0
    ) -> TTSSynthesisResult:
        """Synthesize a single text chunk"""
        try:
            # Validate input parameters
            if not text or not text.strip():
                logger.warning("Empty text chunk provided to MiniMax API")
                return TTSSynthesisResult(
                    success=False,
                    error_message="Empty text chunk provided"
                )

            if not voice_config or not voice_config.voice_id:
                logger.warning("Invalid voice configuration provided to MiniMax API")
                return TTSSynthesisResult(
                    success=False,
                    error_message="Invalid voice configuration"
                )

            # Prepare MiniMax API payload
            payload = {
                "model": self.model,
                "text": text.strip(),  # Ensure no leading/trailing whitespace
                "voice_id": voice_config.voice_id,
                "speed": speed,
                "vol": volume,
                "pitch": int(pitch * 10) - 10  # Convert to MiniMax pitch range
            }
            
            # Add emotion if supported
            if emotion and emotion != "neutral":
                payload["emotion"] = emotion
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Extract Group ID from JWT token if needed
            try:
                payload_b64 = self.api_key.split('.')[1]
                payload_b64 += '=' * (4 - len(payload_b64) % 4)
                jwt_payload = json.loads(base64.b64decode(payload_b64))
                group_id = jwt_payload.get("GroupID")
                if group_id:
                    headers["X-Group-ID"] = group_id
            except Exception:
                pass
            
            # Make API request with retries
            max_retries = self.retry_attempts
            retry_delay = self.retry_delay
            
            for attempt in range(max_retries):
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.post(
                            f"{self.api_host}/v1/t2a_v2",
                            headers=headers,
                            json=payload,
                            timeout=30.0
                        )
                    
                    if response.status_code == 200:
                        break
                    
                    # Handle rate limiting
                    if response.status_code == 429:
                        if attempt < max_retries - 1:
                            logger.warning(f"Rate limited, waiting {retry_delay}s before retry")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 2
                            continue
                    
                    # Handle URL too long error
                    response_text = response.text
                    if (response.status_code == 414 or "URL too long" in response_text):
                        # Reduce text length and retry
                        new_length = max(20, len(text) // 2)
                        text = text[:new_length].strip()
                        if not text.endswith(('.', '!', '?', '。', '！', '？')):
                            text += "."
                        payload["text"] = text
                        logger.warning(f"Reducing text length to {len(text)} chars and retrying")
                        continue
                    
                    # Other errors
                    logger.error(f"MiniMax API error {response.status_code}: {response_text}")
                    return TTSSynthesisResult(
                        success=False,
                        error_message=f"API error {response.status_code}: {response_text}"
                    )
                    
                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"Request failed, retrying in {retry_delay}s: {e}")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2
                        continue
                    else:
                        raise e
            
            # Process successful response
            if response.status_code != 200:
                return TTSSynthesisResult(
                    success=False,
                    error_message=f"Failed after {max_retries} attempts"
                )
            
            response_data = response.json()
            
            # Check for API-level errors
            base_resp = response_data.get('base_resp', {})
            if base_resp.get('status_code', 0) != 0:
                error_msg = base_resp.get('status_msg', 'Unknown API error')
                return TTSSynthesisResult(
                    success=False,
                    error_message=f"MiniMax API error: {error_msg}"
                )
            
            # Extract audio data
            audio_data = response_data.get('data', {})
            if 'audio' not in audio_data:
                return TTSSynthesisResult(
                    success=False,
                    error_message="No audio data in response"
                )
            
            # Save audio file
            audio_content = base64.b64decode(audio_data['audio'])
            
            # Ensure output directory exists
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(output_file, 'wb') as f:
                await f.write(audio_content)
            
            logger.info(f"Successfully synthesized audio: {output_path}")
            
            return TTSSynthesisResult(
                success=True,
                audio_file_path=str(output_file),
                duration_seconds=audio_data.get('duration', 0),
                metadata={
                    "voice_id": voice_config.voice_id,
                    "text_length": len(text),
                    "model": self.model
                }
            )
            
        except Exception as e:
            logger.error(f"Error synthesizing chunk: {e}")
            return TTSSynthesisResult(
                success=False,
                error_message=str(e)
            )
    
    def _chunk_text(self, text: str) -> List[str]:
        """Split text into chunks for MiniMax processing"""
        if len(text) <= self.max_text_length:
            return [text]
        
        chunks = []
        words = text.split()
        current_chunk = []
        current_length = 0
        
        for word in words:
            word_length = len(word) + 1  # +1 for space
            
            if current_length + word_length > self.max_text_length and current_chunk:
                # Finish current chunk
                chunks.append(' '.join(current_chunk))
                current_chunk = [word]
                current_length = len(word)
            else:
                current_chunk.append(word)
                current_length += word_length
        
        if current_chunk:
            chunks.append(' '.join(current_chunk))
        
        return chunks
    
    def _get_default_voice_config(self, line: DialogueLine) -> TTSVoiceConfig:
        """Get default voice configuration for a dialogue line"""
        # Determine language
        is_chinese = self._contains_chinese(line.text)
        lang_prefix = "zh" if is_chinese else "en"
        
        # Determine role
        role = line.role.value if hasattr(line.role, 'value') else str(line.role).lower()
        
        # Map role to voice
        voice_key = f"{lang_prefix}_{role}_female"  # Default to female
        
        if voice_key not in self._voice_configs:
            # Fallback to host voice
            voice_key = f"{lang_prefix}_host_female"
        
        if voice_key not in self._voice_configs:
            # Ultimate fallback
            voice_key = "zh_host_female" if is_chinese else "en_host_female"
        
        return self._voice_configs[voice_key]
    
    async def _combine_audio_chunks(self, audio_files: List[str], output_path: str) -> str:
        """Combine multiple audio chunks into single file"""
        # For now, just return the first file
        # In a full implementation, you would use audio processing library
        # like pydub or ffmpeg to concatenate the files
        logger.warning("Audio chunk combination not implemented, returning first chunk")
        return audio_files[0] if audio_files else output_path
    
    def _clean_text_for_tts(self, text: str) -> str:
        """Clean text for MiniMax TTS (preserve pause marks)"""
        import re
        
        # Remove role labels
        text = re.sub(r'\[[\u4e00-\u9fff\w\s]+\]', '', text)
        text = re.sub(r'\[[A-Za-z\s]+\]', '', text)
        
        # Convert pause marks to MiniMax format (keep ≤0.3s marks)
        def convert_pause(match):
            duration = float(match.group(1))
            if duration <= 0.3:
                return f"<break time='{duration}s'/>"
            else:
                return ""  # Remove longer pauses
        
        text = re.sub(r'<#([\d.]+)#>', convert_pause, text)
        
        # Clean up extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
