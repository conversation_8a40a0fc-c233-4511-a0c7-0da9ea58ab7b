"""
ElevenLabs V3 TTS Service Implementation
Implements the latest V3 API with enhanced features and voice models
"""
import os
import asyncio
import aiofiles
import httpx
import json
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

from .tts_base import TTSServiceBase, TTSVoiceConfig, TTSSynthesisRequest, TTSSynthesisResult
from ..models.podcast_models import DialogueLine, PodcastRole

logger = logging.getLogger(__name__)


class ElevenLabsV3TTSProvider(TTSServiceBase):
    """ElevenLabs V3 TTS service implementation with latest features"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)

        # API configuration
        self.api_key = config.get("api_key") or os.getenv("ELEVENLABS_API_KEY") or "***************************************************"
        self.api_base = config.get("api_base", "https://api.elevenlabs.io/v1")
        
        # V3 specific settings
        self.max_text_length = config.get("max_text_length", 5000)  # V3 supports longer text
        self.model_id = config.get("model_id", "eleven_turbo_v2_5")  # Latest V3 model
        self.output_format = config.get("output_format", "mp3_44100_128")
        
        # V3 features
        self.supports_multilingual = True
        self.supports_voice_cloning = True
        self.supports_emotion_control = True
        
        # Voice configurations
        self._voice_configs = {}
        self._available_voices = []
        self._v3_voices = []
        self._api_key_available = bool(self.api_key)
        self._working_headers = None
        self._v3_available = False
    
    async def initialize(self) -> bool:
        """Initialize ElevenLabs V3 TTS service"""
        try:
            if not self._api_key_available:
                logger.warning("ElevenLabs API key not available, service will not be functional")
                return False

            # Test V3 API availability
            await self._test_v3_availability()
            
            # Fetch available voices
            await self._fetch_voices()
            
            # Initialize voice configurations
            self._initialize_voice_configs()
            
            logger.info(f"ElevenLabs V3 TTS initialized with {len(self._available_voices)} voices")
            logger.info(f"V3 features available: {self._v3_available}")
            return True
                    
        except Exception as e:
            logger.error(f"Failed to initialize ElevenLabs V3 TTS service: {e}")
            return False
    
    async def _test_v3_availability(self):
        """Test if V3 API features are available"""
        header_variants = [
            {"xi-api-key": self.api_key, "Content-Type": "application/json"},
            {"X-API-Key": self.api_key, "Content-Type": "application/json"},
            {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        ]

        async with httpx.AsyncClient() as client:
            for headers in header_variants:
                try:
                    # Test basic API access
                    response = await client.get(
                        f"{self.api_base}/user",
                        headers=headers,
                        timeout=10.0
                    )

                    if response.status_code == 200:
                        self._working_headers = headers
                        
                        # Test V3 specific endpoints
                        models_response = await client.get(
                            f"{self.api_base}/models",
                            headers=headers,
                            timeout=10.0
                        )
                        
                        if models_response.status_code == 200:
                            models_data = models_response.json()
                            # Check for V3 models
                            v3_models = [m for m in models_data if "turbo" in m.get("model_id", "").lower() or "v2_5" in m.get("model_id", "")]
                            self._v3_available = len(v3_models) > 0
                            logger.info(f"Found {len(v3_models)} V3 models")
                        
                        break
                        
                except Exception as e:
                    logger.warning(f"Header variant failed: {e}")
                    continue

        if not self._working_headers:
            # Fallback to default headers
            self._working_headers = {"xi-api-key": self.api_key, "Content-Type": "application/json"}
    
    async def _fetch_voices(self):
        """Fetch available voices from ElevenLabs API"""
        if not self._working_headers:
            await self._test_v3_availability()
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/voices",
                    headers=self._working_headers,
                    timeout=15.0
                )
                
                if response.status_code == 200:
                    voices_data = response.json()
                    self._available_voices = voices_data.get("voices", [])
                    
                    # Categorize V3 voices
                    self._categorize_v3_voices()
                    
                else:
                    logger.warning(f"Failed to fetch voices: {response.status_code}")
                    self._create_default_v3_voices()
                    
        except Exception as e:
            logger.error(f"Error fetching voices: {e}")
            self._create_default_v3_voices()
    
    def _categorize_v3_voices(self):
        """Categorize voices for V3 features"""
        self._v3_voices = []
        
        for voice in self._available_voices:
            voice_info = {
                "voice_id": voice.get("voice_id"),
                "name": voice.get("name", "Unknown"),
                "category": self._determine_v3_category(voice),
                "languages": self._extract_languages(voice),
                "quality": self._determine_quality(voice),
                "features": self._extract_features(voice),
                "preview_url": voice.get("preview_url"),
                "labels": voice.get("labels", {}),
                "settings": voice.get("settings", {})
            }
            self._v3_voices.append(voice_info)
    
    def _determine_v3_category(self, voice: Dict) -> str:
        """Determine voice category for V3"""
        name = voice.get("name", "").lower()
        labels = voice.get("labels", {})
        
        # V3 Premium voices (high quality, latest models)
        if any(keyword in name for keyword in ["premium", "professional", "studio"]):
            return "v3_premium"
        
        # V3 Multilingual voices
        if labels.get("use_case") == "multilingual" or "multilingual" in name:
            return "v3_multilingual"
        
        # V3 Conversational voices
        if labels.get("use_case") == "conversational" or any(keyword in name for keyword in ["chat", "conversation"]):
            return "v3_conversational"
        
        # V3 Narration voices
        if labels.get("use_case") == "narration" or any(keyword in name for keyword in ["narrator", "audiobook"]):
            return "v3_narration"
        
        # Default V3 category
        return "v3_standard"
    
    def _extract_languages(self, voice: Dict) -> List[str]:
        """Extract supported languages from voice data"""
        labels = voice.get("labels", {})
        
        # Default to English
        languages = ["en"]
        
        # Check for multilingual support
        if labels.get("use_case") == "multilingual":
            languages = ["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh", "ja", "hi", "ko"]
        elif "chinese" in voice.get("name", "").lower():
            languages = ["zh", "en"]
        elif "spanish" in voice.get("name", "").lower():
            languages = ["es", "en"]
        
        return languages
    
    def _determine_quality(self, voice: Dict) -> str:
        """Determine voice quality level"""
        labels = voice.get("labels", {})
        
        if labels.get("category") == "professional":
            return "premium"
        elif labels.get("category") == "generated":
            return "standard"
        else:
            return "high"
    
    def _extract_features(self, voice: Dict) -> List[str]:
        """Extract voice features"""
        features = []
        labels = voice.get("labels", {})
        
        if labels.get("use_case") == "multilingual":
            features.append("Multilingual")
        if labels.get("category") == "professional":
            features.append("Professional Quality")
        if self._v3_available:
            features.append("V3 Enhanced")
        
        return features
    
    def _create_default_v3_voices(self):
        """Create default V3 voice configurations when API is unavailable"""
        default_voices = [
            {
                "voice_id": "21m00Tcm4TlvDq8ikWAM",
                "name": "Rachel (V3 Premium)",
                "category": "v3_premium",
                "languages": ["en"],
                "quality": "premium",
                "features": ["V3 Enhanced", "Professional Quality"],
                "labels": {"gender": "female", "age": "young_adult", "accent": "american"}
            },
            {
                "voice_id": "AZnzlk1XvdvUeBnXmlld", 
                "name": "Domi (V3 Multilingual)",
                "category": "v3_multilingual",
                "languages": ["en", "es", "fr", "de", "it", "pt"],
                "quality": "premium",
                "features": ["V3 Enhanced", "Multilingual", "Professional Quality"],
                "labels": {"gender": "female", "age": "young_adult", "accent": "american"}
            },
            {
                "voice_id": "EXAVITQu4vr4xnSDxMaL",
                "name": "Bella (V3 Conversational)",
                "category": "v3_conversational", 
                "languages": ["en"],
                "quality": "high",
                "features": ["V3 Enhanced", "Conversational"],
                "labels": {"gender": "female", "age": "young_adult", "accent": "american"}
            },
            {
                "voice_id": "ErXwobaYiN019PkySvjV",
                "name": "Antoni (V3 Narration)",
                "category": "v3_narration",
                "languages": ["en"],
                "quality": "premium", 
                "features": ["V3 Enhanced", "Narration", "Professional Quality"],
                "labels": {"gender": "male", "age": "middle_aged", "accent": "american"}
            },
            {
                "voice_id": "VR6AewLTigWG4xSOukaG",
                "name": "Arnold (V3 Premium)",
                "category": "v3_premium",
                "languages": ["en"],
                "quality": "premium",
                "features": ["V3 Enhanced", "Professional Quality"],
                "labels": {"gender": "male", "age": "middle_aged", "accent": "american"}
            }
        ]
        
        self._v3_voices = default_voices
        self._available_voices = [
            {
                "voice_id": voice["voice_id"],
                "name": voice["name"],
                "labels": voice["labels"]
            }
            for voice in default_voices
        ]
    
    def _initialize_voice_configs(self):
        """Initialize voice configurations from V3 voices"""
        self._voice_configs = {}
        
        for voice in self._v3_voices:
            voice_config = TTSVoiceConfig(
                voice_id=voice["voice_id"],
                name=voice["name"],
                language=voice["languages"][0] if voice["languages"] else "en",
                gender=voice.get("labels", {}).get("gender", "neutral"),
                description=f"{voice['quality'].title()} quality voice with {', '.join(voice['features'])}"
            )
            
            self._voice_configs[voice["voice_id"]] = voice_config
            
            # Create role-based mappings
            gender = voice.get("labels", {}).get("gender", "neutral")
            if gender == "female":
                self._voice_configs[f"en_host_female_v3"] = voice_config
                self._voice_configs[f"en_expert_female_v3"] = voice_config
            elif gender == "male":
                self._voice_configs[f"en_host_male_v3"] = voice_config
                self._voice_configs[f"en_expert_male_v3"] = voice_config

    async def get_available_voices(self, language: Optional[str] = None) -> List[TTSVoiceConfig]:
        """Get available V3 voices"""
        voices = []

        for voice in self._v3_voices:
            if language and language not in voice["languages"]:
                continue

            voice_config = TTSVoiceConfig(
                voice_id=voice["voice_id"],
                name=voice["name"],
                language=voice["languages"][0] if voice["languages"] else "en",
                gender=voice.get("labels", {}).get("gender", "neutral"),
                description=f"{voice['quality'].title()} quality - {', '.join(voice['features'])}"
            )
            voices.append(voice_config)

        return voices

    async def synthesize_text(self, request: TTSSynthesisRequest) -> TTSSynthesisResult:
        """Synthesize text using ElevenLabs V3 API"""
        try:
            # Prepare text (handle chunking if needed)
            text_chunks = self._chunk_text(request.text)
            audio_files = []

            for i, chunk in enumerate(text_chunks):
                chunk_result = await self._synthesize_chunk_v3(
                    text=chunk,
                    voice_config=request.voice_config,
                    output_path=request.output_path if len(text_chunks) == 1 else f"{request.output_path}_chunk_{i}",
                    emotion=request.emotion,
                    speed=request.speed,
                    pitch=request.pitch,
                    volume=request.volume
                )

                if chunk_result.success:
                    audio_files.append(chunk_result.audio_file_path)
                else:
                    return chunk_result  # Return first failure

            # If multiple chunks, combine them
            if len(audio_files) > 1:
                combined_path = await self._combine_audio_files(audio_files, request.output_path)
                final_audio_path = combined_path
            else:
                final_audio_path = audio_files[0] if audio_files else None

            if final_audio_path:
                return TTSSynthesisResult(
                    success=True,
                    audio_file_path=final_audio_path,
                    duration_seconds=await self._get_audio_duration(final_audio_path),
                    metadata={
                        "provider": "elevenlabs_v3",
                        "model": self.model_id,
                        "voice_id": request.voice_config.voice_id,
                        "voice_name": request.voice_config.name,
                        "v3_features": True,
                        "chunks_processed": len(text_chunks)
                    }
                )
            else:
                return TTSSynthesisResult(
                    success=False,
                    error_message="No audio files generated"
                )

        except Exception as e:
            logger.error(f"ElevenLabs V3 synthesis failed: {e}")
            return TTSSynthesisResult(
                success=False,
                error_message=f"ElevenLabs V3 synthesis error: {str(e)}"
            )

    async def _synthesize_chunk_v3(
        self,
        text: str,
        voice_config: TTSVoiceConfig,
        output_path: str,
        emotion: Optional[str] = None,
        speed: Optional[float] = None,
        pitch: Optional[float] = None,
        volume: Optional[float] = None
    ) -> TTSSynthesisResult:
        """Synthesize a single text chunk using V3 API"""
        try:
            # Prepare voice settings
            voice_settings = self._prepare_v3_voice_settings(emotion, speed, pitch, volume)

            # Prepare V3 API request with proper structure
            synthesis_data = {
                "text": text,
                "voice_settings": voice_settings
            }

            # Add model_id only if V3 is available, otherwise use default V2 behavior
            if self._v3_available:
                synthesis_data["model_id"] = self.model_id
                # Add V3 specific parameters
                synthesis_data.update({
                    "output_format": self.output_format,
                    "optimize_streaming_latency": 0,
                    "similar_to_sample": False
                })
            else:
                # Fallback to V2 compatible request
                synthesis_data["model_id"] = "eleven_multilingual_v2"

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base}/text-to-speech/{voice_config.voice_id}",
                    headers=self._working_headers,
                    json=synthesis_data,
                    timeout=60.0
                )

                if response.status_code == 200:
                    # Save audio file
                    output_file = Path(output_path)
                    output_file.parent.mkdir(parents=True, exist_ok=True)

                    async with aiofiles.open(output_file, 'wb') as f:
                        await f.write(response.content)

                    return TTSSynthesisResult(
                        success=True,
                        audio_file_path=str(output_file),
                        duration_seconds=await self._get_audio_duration(str(output_file)),
                        metadata={
                            "provider": "elevenlabs_v3",
                            "model": self.model_id,
                            "voice_id": voice_config.voice_id,
                            "text_length": len(text)
                        }
                    )
                else:
                    error_msg = f"ElevenLabs V3 API error: {response.status_code}"
                    try:
                        error_data = response.json()
                        error_msg += f" - {error_data.get('detail', {}).get('message', response.text)}"
                    except:
                        error_msg += f" - {response.text}"

                    return TTSSynthesisResult(
                        success=False,
                        error_message=error_msg
                    )

        except Exception as e:
            return TTSSynthesisResult(
                success=False,
                error_message=f"V3 synthesis error: {str(e)}"
            )

    def _prepare_v3_voice_settings(
        self,
        emotion: Optional[str] = None,
        speed: Optional[float] = None,
        pitch: Optional[float] = None,
        volume: Optional[float] = None
    ) -> Dict[str, Any]:
        """Prepare V3 voice settings with enhanced controls"""
        # Use basic settings that are compatible with both V2 and V3
        settings = {
            "stability": 0.5,
            "similarity_boost": 0.75
        }

        # Only add V3-specific settings if V3 is confirmed available
        if self._v3_available:
            settings.update({
                "style": 0.0,
                "use_speaker_boost": True
            })

        # V3 enhanced emotion control (only if V3 is available)
        if emotion and self._v3_available:
            emotion_mappings = {
                "happy": {"stability": 0.3, "similarity_boost": 0.8, "style": 0.2},
                "sad": {"stability": 0.7, "similarity_boost": 0.6, "style": 0.1},
                "angry": {"stability": 0.2, "similarity_boost": 0.9, "style": 0.3},
                "excited": {"stability": 0.1, "similarity_boost": 0.9, "style": 0.4},
                "calm": {"stability": 0.8, "similarity_boost": 0.7, "style": 0.0},
                "neutral": {"stability": 0.5, "similarity_boost": 0.75, "style": 0.0}
            }

            if emotion in emotion_mappings:
                settings.update(emotion_mappings[emotion])
        elif emotion:
            # Fallback for V2 compatibility - only adjust basic settings
            emotion_mappings = {
                "happy": {"stability": 0.3, "similarity_boost": 0.8},
                "sad": {"stability": 0.7, "similarity_boost": 0.6},
                "angry": {"stability": 0.2, "similarity_boost": 0.9},
                "excited": {"stability": 0.1, "similarity_boost": 0.9},
                "calm": {"stability": 0.8, "similarity_boost": 0.7},
                "neutral": {"stability": 0.5, "similarity_boost": 0.75}
            }

            if emotion in emotion_mappings:
                settings.update(emotion_mappings[emotion])

        # Speed control (only if V3 is available)
        if speed is not None and self._v3_available:
            # Map speed to stability (faster = less stable)
            settings["stability"] = max(0.1, min(1.0, settings["stability"] - (speed - 1.0) * 0.3))

        return settings

    def get_v3_voices_by_category(self) -> Dict[str, List[Dict]]:
        """Get V3 voices organized by category"""
        categories = {}

        for voice in self._v3_voices:
            category = voice["category"]
            if category not in categories:
                categories[category] = []
            categories[category].append(voice)

        return categories

    def is_v3_available(self) -> bool:
        """Check if V3 features are available"""
        return self._v3_available

    def get_v3_features(self) -> List[str]:
        """Get list of available V3 features"""
        features = [
            "Enhanced voice quality",
            "Improved multilingual support",
            "Advanced emotion control",
            "Longer text support (5000 chars)",
            "Professional voice models"
        ]

        if self._v3_available:
            features.extend([
                "Real-time optimization",
                "Voice cloning support",
                "Custom voice settings"
            ])

        return features

    def _chunk_text(self, text: str) -> List[str]:
        """Split text into chunks for ElevenLabs V3 processing"""
        if len(text) <= self.max_text_length:
            return [text]

        # Split by sentences first
        import re
        sentences = re.split(r'[.!?。！？]\s*', text)

        chunks = []
        current_chunk = []
        current_length = 0

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            sentence_length = len(sentence) + 2  # +2 for punctuation and space

            if current_length + sentence_length > self.max_text_length and current_chunk:
                # Finish current chunk
                chunk_text = '. '.join(current_chunk) + '.'
                chunks.append(chunk_text)
                current_chunk = [sentence]
                current_length = sentence_length
            else:
                current_chunk.append(sentence)
                current_length += sentence_length

        if current_chunk:
            chunk_text = '. '.join(current_chunk) + '.'
            chunks.append(chunk_text)

        return chunks

    async def _combine_audio_files(self, audio_files: List[str], output_path: str) -> str:
        """Combine multiple audio files into single file"""
        # For now, just return the first file
        # In a full implementation, you would use audio processing library
        # like pydub or ffmpeg to concatenate the files
        logger.warning("Audio file combination not implemented, returning first file")
        return audio_files[0] if audio_files else output_path

    async def _get_audio_duration(self, audio_file_path: str) -> Optional[float]:
        """Get duration of audio file in seconds"""
        try:
            # For now, return a default duration
            # In a full implementation, you would use audio processing library
            # like pydub or ffmpeg to get actual duration
            return 5.0  # Default 5 seconds
        except Exception as e:
            logger.warning(f"Could not get audio duration for {audio_file_path}: {e}")
            return None

    def _get_default_voice_config(self, line: DialogueLine) -> TTSVoiceConfig:
        """Get default voice configuration for a dialogue line"""
        # Determine language
        is_chinese = self._contains_chinese(line.text)
        lang_prefix = "zh" if is_chinese else "en"

        # Determine role
        role = line.role.value if hasattr(line.role, 'value') else str(line.role).lower()

        # Map role to voice for V3
        voice_key = f"{lang_prefix}_{role}_v3"

        if voice_key in self._voice_configs:
            return self._voice_configs[voice_key]

        # Fallback to host voice
        fallback_key = f"{lang_prefix}_host_v3"
        if fallback_key in self._voice_configs:
            return self._voice_configs[fallback_key]

        # Fallback to any available V3 voice
        available_voices = [v for v in self._voice_configs.values() if not v.voice_id.endswith("_v3")]
        if available_voices:
            return available_voices[0]

        # Create emergency fallback with V3 default voice
        return TTSVoiceConfig(
            voice_id="21m00Tcm4TlvDq8ikWAM",  # Rachel - V3 Premium
            name="Rachel (V3 Premium)",
            language="en",
            gender="female",
            description="Default V3 premium voice"
        )

    def _contains_chinese(self, text: str) -> bool:
        """Check if text contains Chinese characters"""
        for char in text:
            if '\u4e00' <= char <= '\u9fff':
                return True
        return False
