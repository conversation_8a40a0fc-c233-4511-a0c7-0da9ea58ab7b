from pydantic import BaseModel, Field
from typing import List, Optional, Dict
from datetime import datetime
from enum import Enum


class PodcastRole(str, Enum):
    HOST = "host"
    EXPERT = "expert"
    INTERVIEWER = "interviewer"
    GUEST = "guest"


class SearchRequest(BaseModel):
    topic: str = Field(..., min_length=1, max_length=500)
    num_results: int = Field(default=5, ge=1, le=10)


class SearchResult(BaseModel):
    title: str
    snippet: str
    url: str
    relevance_score: float = Field(ge=0, le=1)


class ResearchReport(BaseModel):
    topic: str
    summary: str
    key_findings: List[str]
    sources: List[SearchResult]
    generated_at: datetime = Field(default_factory=datetime.now)


class DialogueLine(BaseModel):
    role: PodcastRole
    speaker_name: str
    text: str
    voice_id: Optional[str] = None
    emotion: Optional[str] = "auto"  # auto / neutral / happy / sad / angry / fearful / disgusted / surprised
    audio_url: Optional[str] = None


class PodcastScript(BaseModel):
    title: str
    description: str
    dialogue: List[DialogueLine]
    duration_estimate: Optional[int] = None  # in seconds
    language: str = Field(default="en", pattern="^(en|zh)$")  # Language for TTS selection


class TTSProvider(str, Enum):
    MINIMAX = "minimax"
    ELEVENLABS = "elevenlabs"
    ELEVENLABS_V3 = "elevenlabs_v3"
    ELEVENLABS_DIALOGUE = "elevenlabs_dialogue"
    AUTO = "auto"


class PodcastGenerationRequest(BaseModel):
    topic: str
    script_style: str = Field(default="conversational", pattern="^(conversational|educational|debate|interview)$")
    num_speakers: int = Field(default=2, ge=2, le=3)
    duration_target: int = Field(default=300, ge=60, le=600)  # 1-10 minutes
    language: str = Field(default="en", pattern="^(en|zh)$")  # English or Chinese
    tts_provider: TTSProvider = Field(default=TTSProvider.MINIMAX)  # TTS engine selection


class PodcastGenerationResponse(BaseModel):
    request_id: str
    status: str
    report: Optional[ResearchReport] = None
    script: Optional[PodcastScript] = None
    audio_url: Optional[str] = None
    error: Optional[str] = None


class VoiceOption(BaseModel):
    voice_id: str
    name: str
    description: str
    gender: str
    preview_url: Optional[str] = None


class TTSProviderInfo(BaseModel):
    """TTS提供商信息"""
    provider: TTSProvider
    name: str
    description: str
    features: List[str]
    languages: List[str]
    quality: str  # "high", "very_high", etc.
    max_text_length: int
    audio_format: str
    available: bool = True


class TTSGenerationResult(BaseModel):
    """TTS生成结果"""
    success: bool
    provider_used: Optional[str] = None
    fallback_used: bool = False
    audio_files: List[str] = []
    total_duration: Optional[float] = None
    error_message: Optional[str] = None
    generation_time: Optional[float] = None