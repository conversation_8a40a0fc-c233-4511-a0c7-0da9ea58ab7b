#!/usr/bin/env python3
"""
Test Script for ElevenLabs V3 Upgrade
Verifies all V3 features and integration
"""
import asyncio
import httpx
import json


async def test_tts_providers_v3():
    """Test that V3 provider is available in TTS providers API"""
    print("🔧 Testing TTS Providers API (V3 Support)")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://127.0.0.1:8004/api/tts-providers")
            
            if response.status_code == 200:
                data = response.json()
                providers = data.get('providers', [])
                status = data.get('status', {})
                
                print(f"✅ TTS Providers API successful")
                print(f"   Total providers: {len(providers)}")
                
                # Check for V3 provider
                v3_provider = None
                for provider in providers:
                    if provider.get('provider') == 'elevenlabs_v3':
                        v3_provider = provider
                        break
                
                if v3_provider:
                    print(f"✅ ElevenLabs V3 provider found")
                    print(f"   Name: {v3_provider.get('name')}")
                    print(f"   Description: {v3_provider.get('description')}")
                    print(f"   Features: {len(v3_provider.get('features', []))}")
                    print(f"   Languages: {len(v3_provider.get('languages', []))}")
                    print(f"   Max text length: {v3_provider.get('max_text_length')}")
                    
                    # Check availability
                    v3_available = status.get(v3_provider.get('name'), False)
                    print(f"   Available: {'✅' if v3_available else '❌'}")
                    
                    return True
                else:
                    print(f"❌ ElevenLabs V3 provider not found")
                    return False
            else:
                print(f"❌ TTS Providers API failed: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ TTS Providers API error: {e}")
        return False


async def test_v3_voices_api():
    """Test V3 voices API endpoint"""
    print(f"\n🎵 Testing ElevenLabs V3 Voices API")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://127.0.0.1:8004/api/voices/elevenlabs_v3")
            
            if response.status_code == 200:
                data = response.json()
                voices = data.get('voices', [])
                
                print(f"✅ ElevenLabs V3 voices API successful")
                print(f"   Provider: {data.get('provider')}")
                print(f"   Available voices: {len(voices)}")
                
                # Check for V3 specific features
                v3_features_found = 0
                categories = set()
                
                for voice in voices[:3]:  # Show first 3 voices
                    print(f"\n   Voice: {voice.get('name')}")
                    print(f"   - ID: {voice.get('voice_id')}")
                    print(f"   - Gender: {voice.get('gender')}")
                    print(f"   - Category: {voice.get('category', 'N/A')}")
                    print(f"   - Features: {voice.get('features', [])}")
                    
                    if voice.get('category'):
                        categories.add(voice.get('category'))
                    if voice.get('features'):
                        v3_features_found += 1
                
                print(f"\n   V3 Categories found: {list(categories)}")
                print(f"   Voices with V3 features: {v3_features_found}")
                
                if v3_features_found > 0 and categories:
                    print(f"✅ V3 specific features detected")
                    return True
                else:
                    print(f"⚠️ No V3 specific features found")
                    return False
            else:
                print(f"❌ V3 voices API failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ V3 voices API error: {e}")
        return False


async def test_v2_vs_v3_comparison():
    """Compare V2 and V3 voice offerings"""
    print(f"\n🔄 Testing V2 vs V3 Voice Comparison")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient() as client:
            # Get V2 voices
            v2_response = await client.get("http://127.0.0.1:8004/api/voices/elevenlabs")
            v3_response = await client.get("http://127.0.0.1:8004/api/voices/elevenlabs_v3")
            
            if v2_response.status_code == 200 and v3_response.status_code == 200:
                v2_data = v2_response.json()
                v3_data = v3_response.json()
                
                v2_voices = v2_data.get('voices', [])
                v3_voices = v3_data.get('voices', [])
                
                print(f"✅ Both V2 and V3 APIs working")
                print(f"   V2 voices: {len(v2_voices)}")
                print(f"   V3 voices: {len(v3_voices)}")
                
                # Compare features
                v2_features = set()
                v3_features = set()
                
                for voice in v2_voices:
                    if voice.get('description'):
                        v2_features.add("Basic Description")
                
                for voice in v3_voices:
                    if voice.get('features'):
                        v3_features.update(voice.get('features', []))
                    if voice.get('category'):
                        v3_features.add(f"Category: {voice.get('category')}")
                
                print(f"\n   V2 Features: {list(v2_features)}")
                print(f"   V3 Features: {list(v3_features)}")
                
                if len(v3_features) > len(v2_features):
                    print(f"✅ V3 offers enhanced features over V2")
                    return True
                else:
                    print(f"⚠️ V3 features not significantly different from V2")
                    return False
            else:
                print(f"❌ Failed to get both V2 and V3 voices")
                return False
                
    except Exception as e:
        print(f"❌ V2 vs V3 comparison error: {e}")
        return False


async def test_web_interface_v3():
    """Test that web interface includes V3 option"""
    print(f"\n🌐 Testing Web Interface V3 Integration")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://127.0.0.1:8004/")
            
            if response.status_code == 200:
                html_content = response.text
                
                # Check for V3 related elements
                checks = [
                    ("V3 option in select", 'elevenlabs_v3' in html_content),
                    ("V3 label present", 'ElevenLabs V3' in html_content),
                    ("Latest indicator", '⭐' in html_content or 'Latest' in html_content),
                    ("TTS provider select", 'id="tts-provider"' in html_content)
                ]
                
                print(f"✅ Web interface loaded")
                print(f"   Content length: {len(html_content)} chars")
                
                all_passed = True
                for check_name, passed in checks:
                    status = "✅" if passed else "❌"
                    print(f"   {status} {check_name}")
                    if not passed:
                        all_passed = False
                
                if all_passed:
                    print(f"✅ All V3 web interface elements present")
                    return True
                else:
                    print(f"⚠️ Some V3 elements missing from web interface")
                    return False
            else:
                print(f"❌ Web interface failed: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Web interface test error: {e}")
        return False


async def test_auto_mode_v3():
    """Test that auto mode includes V3 voices"""
    print(f"\n🤖 Testing Auto Mode V3 Integration")
    print("=" * 50)
    
    try:
        async with httpx.AsyncClient() as client:
            # Test auto mode by checking if it would load V3 voices
            # This simulates what the frontend JavaScript would do
            
            providers_to_test = ['minimax', 'elevenlabs', 'elevenlabs_v3']
            total_voices = 0
            v3_voices_found = False
            
            for provider in providers_to_test:
                try:
                    response = await client.get(f"http://127.0.0.1:8004/api/voices/{provider}")
                    if response.status_code == 200:
                        data = response.json()
                        voices = data.get('voices', [])
                        total_voices += len(voices)
                        
                        if provider == 'elevenlabs_v3' and voices:
                            v3_voices_found = True
                            print(f"   ✅ {provider}: {len(voices)} voices")
                        elif voices:
                            print(f"   ✅ {provider}: {len(voices)} voices")
                        else:
                            print(f"   ⚠️ {provider}: No voices")
                    else:
                        print(f"   ❌ {provider}: API failed ({response.status_code})")
                except Exception as e:
                    print(f"   ❌ {provider}: Error ({e})")
            
            print(f"\n   Total voices available for auto mode: {total_voices}")
            
            if v3_voices_found and total_voices > 10:
                print(f"✅ Auto mode has access to V3 voices")
                return True
            else:
                print(f"⚠️ Auto mode may not have full V3 integration")
                return False
                
    except Exception as e:
        print(f"❌ Auto mode test error: {e}")
        return False


async def main():
    """Run all V3 upgrade tests"""
    print("🎯 ElevenLabs V3 Upgrade Verification Test Suite")
    print("Testing V3 API integration, voice selection, and UI updates")
    print("=" * 80)
    
    tests = [
        ("TTS Providers V3 Support", test_tts_providers_v3),
        ("V3 Voices API", test_v3_voices_api),
        ("V2 vs V3 Comparison", test_v2_vs_v3_comparison),
        ("Web Interface V3 Integration", test_web_interface_v3),
        ("Auto Mode V3 Integration", test_auto_mode_v3)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Final summary
    print(f"\n" + "=" * 80)
    print("🏁 V3 UPGRADE VERIFICATION SUMMARY")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed >= total * 0.8:  # 80% success rate
        print("\n🎉 ElevenLabs V3 upgrade successful!")
        print("✅ V3 provider is available and functional")
        print("✅ V3 voices are accessible through API")
        print("✅ Web interface supports V3 selection")
        print("✅ Enhanced features are properly integrated")
    else:
        print("\n⚠️ V3 upgrade may need additional work")
        print("Check the failed tests above for details")
    
    print(f"\n💡 Manual Testing:")
    print("1. Open http://127.0.0.1:8004 in your browser")
    print("2. Select 'ElevenLabs V3 (Latest) ⭐' from TTS Engine dropdown")
    print("3. Generate a podcast and verify V3 voices appear")
    print("4. Check that V3 voices are categorized (Premium, Multilingual, etc.)")
    print("5. Verify V3 features are described in voice selection")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
