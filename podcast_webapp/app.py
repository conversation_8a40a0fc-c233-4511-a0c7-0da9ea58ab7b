import os
import uuid
import async<PERSON>
from pathlib import Path
from datetime import datetime
from typing import Optional

from fastapi import FastAPI, HTTPException, Request, Form, File, UploadFile
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from pydantic import ValidationError
import uvicorn
from dotenv import load_dotenv

from src.models.podcast_models import (
    PodcastGenerationRequest, PodcastGenerationResponse,
    SearchRequest, PodcastScript, DialogueLine, TTSProvider
)
from src.services.openrouter_service import OpenRouterService
from src.services.podcast_script_service import PodcastScriptService
from src.services.minimax_tts_service import MiniMaxTTSService
from src.services.podcast_audio_service import PodcastAudioService
from src.services.tts_config_service import tts_config_service
from demo_data import get_demo_report, get_demo_script

# Load environment variables
load_dotenv()

app = FastAPI(title="Podcast Generator", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/audio", StaticFiles(directory=os.path.join(os.getenv("MINIMAX_MCP_BASE_PATH", "./audio_output"), "podcasts")), name="audio")

# Setup templates
templates = Jinja2Templates(directory="templates")

# Initialize services with error handling
try:
    openrouter_service = OpenRouterService()
except Exception as e:
    print(f"Warning: OpenRouter service failed to initialize: {e}")
    openrouter_service = None

try:
    script_service = PodcastScriptService()
except Exception as e:
    print(f"Warning: Script service failed to initialize: {e}")
    script_service = None

# Initialize TTS services
tts_enabled = os.getenv("TTS_ENABLED", "True").lower() == "true"
if tts_enabled:
    try:
        # Initialize unified TTS service
        unified_tts_service = PodcastAudioService()
        print("Unified TTS service created")

        # Initialize legacy TTS service for backward compatibility
        legacy_tts_service = MiniMaxTTSService()
        print("Legacy TTS service initialized")

        # Use legacy service as fallback
        tts_service = legacy_tts_service

    except Exception as e:
        print(f"Warning: TTS services failed to initialize: {e}")
        unified_tts_service = None
        tts_service = None
else:
    print("TTS disabled via configuration")
    unified_tts_service = None
    tts_service = None


# Async initialization function for unified TTS service
async def initialize_unified_tts():
    """Initialize unified TTS service asynchronously"""
    global unified_tts_service
    if unified_tts_service:
        try:
            await unified_tts_service.initialize()
            print("✅ Unified TTS service initialized successfully")
        except Exception as e:
            print(f"⚠️ Unified TTS service initialization failed: {e}")


# Initialize unified TTS service on startup
@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    await initialize_unified_tts()

# Store generation sessions in memory (in production, use Redis or similar)
generation_sessions = {}


@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Render the main application page"""
    demo_mode = not openrouter_service or not script_service
    return templates.TemplateResponse("index.html", {
        "request": request,
        "demo_mode": demo_mode
    })

@app.get("/enhanced", response_class=HTMLResponse)
async def enhanced_home(request: Request):
    """Render the enhanced application page"""
    demo_mode = not openrouter_service or not script_service
    return templates.TemplateResponse("enhanced_index.html", {
        "request": request,
        "demo_mode": demo_mode
    })

# Commercial Grade Routes
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request):
    """Render the commercial dashboard page"""
    return templates.TemplateResponse("dashboard.html", {
        "request": request
    })

@app.get("/projects/new", response_class=HTMLResponse)
async def project_create(request: Request):
    """Render the project creation page"""
    return templates.TemplateResponse("project_create.html", {
        "request": request
    })

@app.get("/projects", response_class=HTMLResponse)
async def projects_list(request: Request):
    """Render the projects list page"""
    # For now, redirect to dashboard
    return RedirectResponse(url="/dashboard")

@app.get("/library", response_class=HTMLResponse)
async def library(request: Request):
    """Render the template library page"""
    # For now, redirect to dashboard
    return RedirectResponse(url="/dashboard")

@app.get("/analytics", response_class=HTMLResponse)
async def analytics(request: Request):
    """Render the analytics page"""
    # For now, redirect to dashboard
    return RedirectResponse(url="/dashboard")

@app.get("/settings", response_class=HTMLResponse)
async def settings(request: Request):
    """Render the settings page"""
    # For now, redirect to dashboard
    return RedirectResponse(url="/dashboard")

# Enhanced API Endpoints for the new UI
@app.get("/api/tts/providers")
async def get_tts_providers():
    """Get available TTS providers with their capabilities and status"""
    try:
        providers = []

        # Enhanced ElevenLabs Provider
        if unified_tts_service:
            # For now, assume available - in production, implement proper health check
            elevenlabs_available = True
            providers.append({
                "id": "enhanced_elevenlabs",
                "name": "Enhanced ElevenLabs",
                "description": "Advanced TTS with emotion tags and audio events",
                "available": elevenlabs_available,
                "status": "online" if elevenlabs_available else "offline",
                "model": "v3",
                "fallback_model": "v2",
                "features": [
                    {
                        "name": "Emotion Tags",
                        "enhanced": True,
                        "icon": "fas fa-theater-masks",
                        "description": "Support for [excited], [calm], [whispering], etc."
                    },
                    {
                        "name": "Audio Events",
                        "enhanced": True,
                        "icon": "fas fa-music",
                        "description": "Support for {applause}, {music}, {footsteps}, etc."
                    },
                    {
                        "name": "Text-to-Dialogue",
                        "enhanced": True,
                        "icon": "fas fa-comments",
                        "description": "Advanced dialogue synthesis"
                    }
                ],
                "supported_languages": ["en", "zh", "es", "fr"],
                "max_characters": 5000,
                "pricing_tier": "premium"
            })

        # MiniMax Provider
        if tts_service:
            minimax_available = True  # Assume available if service exists
            providers.append({
                "id": "minimax",
                "name": "MiniMax TTS",
                "description": "Standard TTS with pause optimization",
                "available": minimax_available,
                "status": "online" if minimax_available else "offline",
                "model": "standard",
                "features": [
                    {
                        "name": "Pause Optimization",
                        "enhanced": False,
                        "icon": "fas fa-pause",
                        "description": "Optimized pause markers ≤0.3s"
                    },
                    {
                        "name": "Voice Selection",
                        "enhanced": False,
                        "icon": "fas fa-microphone",
                        "description": "Multiple voice options"
                    }
                ],
                "supported_languages": ["zh", "en"],
                "max_characters": 3000,
                "pricing_tier": "standard"
            })

        return {
            "success": True,
            "providers": providers
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/api/tts/voices")
async def get_tts_voices(provider: str = "enhanced_elevenlabs"):
    """Get available voices for a specific provider"""
    try:
        voices = []

        if provider == "enhanced_elevenlabs":
            voices = [
                {
                    "id": "voice_001",
                    "name": "Sarah",
                    "description": "Professional female voice, clear and articulate",
                    "gender": "female",
                    "age_range": "adult",
                    "accent": "american",
                    "style": "professional",
                    "sample_url": "/static/samples/sarah_sample.mp3",
                    "supported_features": ["emotion_tags", "audio_events"],
                    "language": "en",
                    "premium": False
                },
                {
                    "id": "voice_002",
                    "name": "Marcus",
                    "description": "Warm male voice with natural intonation",
                    "gender": "male",
                    "age_range": "adult",
                    "accent": "british",
                    "style": "conversational",
                    "sample_url": "/static/samples/marcus_sample.mp3",
                    "supported_features": ["emotion_tags", "audio_events", "dialogue_mode"],
                    "language": "en",
                    "premium": True
                }
            ]
        elif provider == "minimax":
            voices = [
                {
                    "id": "minimax_voice_001",
                    "name": "小雅",
                    "description": "清晰的女性声音，适合播客",
                    "gender": "female",
                    "age_range": "adult",
                    "accent": "standard",
                    "style": "professional",
                    "sample_url": "/static/samples/xiaoya_sample.mp3",
                    "supported_features": ["pause_optimization"],
                    "language": "zh",
                    "premium": False
                }
            ]

        return {
            "success": True,
            "provider": provider,
            "voices": voices
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/api/tts/preview")
async def preview_voice(request: dict):
    """Generate voice preview"""
    try:
        voice_id = request.get("voice_id")
        provider = request.get("provider", "enhanced_elevenlabs")
        text = request.get("text", "Hello, this is a preview of my voice.")

        # For demo purposes, return a mock response
        # In production, this would generate actual audio
        return {
            "success": True,
            "audio_url": f"/static/previews/preview_{voice_id}.mp3",
            "duration": 3.2,
            "file_size": "156KB",
            "model_used": "v3" if provider == "enhanced_elevenlabs" else "standard"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/api/debug/services")
async def debug_services():
    """Debug endpoint to check service status"""
    return {
        "openrouter_service": {
            "available": openrouter_service is not None,
            "api_key_configured": bool(os.getenv("OPENROUTER_API_KEY"))
        },
        "script_service": {
            "available": script_service is not None,
            "openrouter_dependency": openrouter_service is not None
        },
        "tts_service": {
            "available": tts_service is not None,
            "api_key_configured": bool(os.getenv("MINIMAX_API_KEY"))
        },
        "demo_mode": not openrouter_service or not script_service,
        "environment_variables": {
            "OPENROUTER_API_KEY": "SET" if os.getenv("OPENROUTER_API_KEY") else "NOT SET",
            "MINIMAX_API_KEY": "SET" if os.getenv("MINIMAX_API_KEY") else "NOT SET"
        }
    }


@app.get("/api/test")
async def test_endpoint():
    """Test endpoint to verify API is working"""
    return {"status": "ok", "message": "API is working"}


@app.post("/api/test-tts")
async def test_tts(text: str = "Hello, this is a test", voice_id: str = "audiobook_male_1"):
    """Test MiniMax TTS API directly"""
    if not tts_service:
        return {"error": "TTS service not available"}
    
    try:
        from src.models.podcast_models import DialogueLine, PodcastRole
        test_line = DialogueLine(
            role=PodcastRole.HOST,
            speaker_name="Test",
            text=text,
            voice_id=voice_id,
            emotion="neutral"
        )
        
        audio_path = await tts_service.synthesize_dialogue_line(test_line, "test")
        return {"success": True, "audio_path": audio_path, "voice_id": voice_id}
    except Exception as e:
        return {"error": str(e), "voice_id": voice_id}


@app.post("/api/test-voice-list")
async def test_voice_list():
    """Test multiple voice IDs to find working ones"""
    if not tts_service:
        return {"error": "TTS service not available"}
    
    test_voices = [
        "audiobook_male_1",
        "audiobook_female_1", 
        "presenter_male",
        "presenter_female",
        "male-qn-qingse",
        "female-shaonv",
        "male-qn-jingying", 
        "female-yujie",
        "Chinese (Mandarin)_Reliable_Executive",
        "Chinese (Mandarin)_Wise_Women"
    ]
    
    results = []
    
    for voice_id in test_voices:
        try:
            from src.models.podcast_models import DialogueLine, PodcastRole
            test_line = DialogueLine(
                role=PodcastRole.HOST,
                speaker_name="Test",
                text="测试语音",
                voice_id=voice_id,
                emotion="neutral"
            )
            
            audio_path = await tts_service.synthesize_dialogue_line(test_line, f"test_{voice_id}")
            results.append({"voice_id": voice_id, "status": "success", "audio_path": audio_path})
        except Exception as e:
            results.append({"voice_id": voice_id, "status": "error", "error": str(e)})
    
    return {"results": results}


@app.post("/api/generate-script")
async def generate_script(request: PodcastGenerationRequest):
    """Generate podcast script only (Phase 1 - HITL workflow)"""
    session_id = str(uuid.uuid4())
    
    # Check if services are available - if not, use demo data
    use_demo = not openrouter_service or not script_service
    
    try:
        # Store initial session
        generation_sessions[session_id] = {
            "status": "searching",
            "created_at": datetime.now(),
            "request": request,
            "phase": "script_generation"
        }
        
        # Start async script generation process
        import asyncio
        asyncio.create_task(generate_script_async(session_id, request, use_demo))
        
        # Return immediately with session ID
        return PodcastGenerationResponse(
            request_id=session_id,
            status="searching",
            report=None,
            script=None,
            audio_url=None
        )
        
    except Exception as e:
        return PodcastGenerationResponse(
            request_id=session_id,
            status="error",
            error=str(e)
        )


@app.post("/api/generate-podcast")
async def generate_podcast(request: PodcastGenerationRequest):
    """Main endpoint to generate a complete podcast from a topic (legacy - full workflow)"""
    session_id = str(uuid.uuid4())
    
    # Check if services are available - if not, use demo data
    use_demo = not openrouter_service or not script_service
    
    try:
        # Store initial session
        generation_sessions[session_id] = {
            "status": "searching",
            "created_at": datetime.now(),
            "request": request,
            "phase": "full_generation"
        }
        
        # Start async generation process
        import asyncio
        asyncio.create_task(generate_podcast_async(session_id, request, use_demo))
        
        # Return immediately with session ID
        return PodcastGenerationResponse(
            request_id=session_id,
            status="searching",
            report=None,
            script=None,
            audio_url=None
        )
        
    except Exception as e:
        return PodcastGenerationResponse(
            request_id=session_id,
            status="error",
            error=str(e)
        )


async def generate_script_async(session_id: str, request: PodcastGenerationRequest, use_demo: bool):
    """Async script generation process (Phase 1 - HITL workflow)"""
    try:
        if use_demo:
            # Use demo data
            print(f"🔧 DEMO MODE: Using demo data for topic '{request.topic}' in language '{request.language}'")
            import asyncio
            await asyncio.sleep(1)  # Simulate processing time

            generation_sessions[session_id]["status"] = "generating_report"
            await asyncio.sleep(1)

            report = get_demo_report(request.topic)
            generation_sessions[session_id]["report"] = report

            generation_sessions[session_id]["status"] = "generating_script"
            await asyncio.sleep(1)

            script = get_demo_script(request.topic, request.language)
            generation_sessions[session_id]["script"] = script
            print(f"🔧 DEMO MODE: Generated script with {len(script.dialogue)} dialogue lines")

            generation_sessions[session_id]["status"] = "script_ready"  # Ready for review
            
        else:
            # Use real services
            # Step 1: Search the web
            search_request = SearchRequest(topic=request.topic, num_results=5)
            search_results = await openrouter_service.search_web(search_request)
            
            generation_sessions[session_id]["status"] = "generating_report"
            
            # Step 2: Generate report
            report = await openrouter_service.generate_report(request.topic, search_results, request.language)
            generation_sessions[session_id]["report"] = report
            
            generation_sessions[session_id]["status"] = "generating_script"
            
            # Step 3: Generate podcast script
            script = await script_service.generate_podcast_script(report, request)
            generation_sessions[session_id]["script"] = script
            
            generation_sessions[session_id]["status"] = "script_ready"  # Ready for review
        
    except Exception as e:
        generation_sessions[session_id]["status"] = "error"
        generation_sessions[session_id]["error"] = str(e)
        print(f"Error in async script generation: {e}")


async def generate_podcast_async(session_id: str, request: PodcastGenerationRequest, use_demo: bool):
    """Async podcast generation process (full workflow)"""
    try:
        if use_demo:
            # Use demo data
            import asyncio
            await asyncio.sleep(1)  # Simulate processing time
            
            generation_sessions[session_id]["status"] = "generating_report"
            await asyncio.sleep(1)
            
            report = get_demo_report(request.topic)
            generation_sessions[session_id]["report"] = report
            
            generation_sessions[session_id]["status"] = "generating_script"
            await asyncio.sleep(1)
            
            script = get_demo_script(request.topic, request.language)
            generation_sessions[session_id]["script"] = script
            
            generation_sessions[session_id]["status"] = "completed"
            
            # Return response without audio (demo mode)
            return PodcastGenerationResponse(
                request_id=session_id,
                status="completed",
                report=report,
                script=script,
                audio_url=None
            )
        else:
            # Use real services
            # Step 1: Search the web
            search_request = SearchRequest(topic=request.topic, num_results=5)
            search_results = await openrouter_service.search_web(search_request)
            
            generation_sessions[session_id]["status"] = "generating_report"
            
            # Step 2: Generate report
            report = await openrouter_service.generate_report(request.topic, search_results, request.language)
            generation_sessions[session_id]["report"] = report
            
            generation_sessions[session_id]["status"] = "generating_script"
            
            # Step 3: Generate podcast script
            script = await script_service.generate_podcast_script(report, request)
            generation_sessions[session_id]["script"] = script
            
            generation_sessions[session_id]["status"] = "synthesizing_audio"
            
            # Step 4: Synthesize audio
            if tts_service:
                audio_result = await tts_service.synthesize_podcast(script)
                generation_sessions[session_id]["audio"] = audio_result
                audio_url = audio_result["audio_url"]
            else:
                audio_url = None
        
            generation_sessions[session_id]["status"] = "completed"
            generation_sessions[session_id]["audio"] = audio_result if tts_service else None
        
    except Exception as e:
        generation_sessions[session_id]["status"] = "error"
        generation_sessions[session_id]["error"] = str(e)
        print(f"Error in async generation: {e}")


@app.get("/api/status/{session_id}")
async def get_generation_status(session_id: str):
    """Check the status of a podcast generation session"""
    if session_id not in generation_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = generation_sessions[session_id]
    return {
        "session_id": session_id,
        "status": session["status"],
        "created_at": session["created_at"],
        "error": session.get("error")
    }


@app.get("/api/session/{session_id}")
async def get_session_results(session_id: str):
    """Get the complete results of a podcast generation session"""
    if session_id not in generation_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = generation_sessions[session_id]
    
    # Allow access to results when script is ready or fully completed
    if session["status"] not in ["script_ready", "completed"]:
        return {"error": "Session not ready yet", "status": session["status"]}
    
    return PodcastGenerationResponse(
        request_id=session_id,
        status=session["status"],
        report=session.get("report"),
        script=session.get("script"),
        audio_url=session.get("audio", {}).get("audio_url") if session.get("audio") else None
    )


@app.post("/api/update-script")
async def update_script(
    session_id: str = Form(...),
    updated_script: str = Form(...)
):
    """Update the podcast script and re-synthesize audio"""
    if session_id not in generation_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    try:
        import json
        script_data = json.loads(updated_script)
        
        # Parse the updated dialogue
        dialogue_lines = []
        for line in script_data.get("dialogue", []):
            dialogue_lines.append(DialogueLine(
                role=line["role"],
                speaker_name=line["speaker_name"],
                text=line["text"],
                voice_id=line.get("voice_id")
            ))
        
        # Create updated script
        updated_podcast_script = PodcastScript(
            title=script_data.get("title", "Updated Podcast"),
            description=script_data.get("description", ""),
            dialogue=dialogue_lines
        )
        
        # Re-synthesize audio
        audio_result = await tts_service.synthesize_podcast(updated_podcast_script)
        
        # Update session
        generation_sessions[session_id]["script"] = updated_podcast_script
        generation_sessions[session_id]["audio"] = audio_result
        
        return {
            "success": True,
            "audio_url": audio_result["audio_url"],
            "script": updated_podcast_script
        }
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/tts-providers")
async def get_tts_providers():
    """Get available TTS providers and their information"""
    try:
        providers = tts_config_service.get_all_providers()
        return {
            "providers": [provider.dict() for provider in providers],
            "status": tts_config_service.get_provider_status()
        }
    except Exception as e:
        return {"error": str(e)}


@app.get("/api/voices")
async def get_available_voices():
    """Get list of available TTS voices from MiniMax API"""
    if not tts_service:
        return {"error": "TTS service not available"}

    try:
        voices = await tts_service.get_available_voices()
        return {"voices": voices}
    except Exception as e:
        return {"error": str(e)}


@app.get("/api/voices/{provider}")
async def get_provider_voices(provider: str):
    """Get voices for a specific TTS provider"""
    try:
        voices = []

        if provider == "minimax":
            # Return MiniMax voices
            voices = [
                {
                    "voice_id": "audiobook_male_1",
                    "name": "男性有声书1",
                    "gender": "male",
                    "language": "zh",
                    "description": "专业男性有声书音色"
                },
                {
                    "voice_id": "audiobook_female_1",
                    "name": "女性有声书1",
                    "gender": "female",
                    "language": "zh",
                    "description": "专业女性有声书音色"
                },
                {
                    "voice_id": "presenter_male",
                    "name": "男性主持人",
                    "gender": "male",
                    "language": "zh",
                    "description": "专业男性主持人音色"
                },
                {
                    "voice_id": "presenter_female",
                    "name": "女性主持人",
                    "gender": "female",
                    "language": "zh",
                    "description": "专业女性主持人音色"
                },
                {
                    "voice_id": "male-botong",
                    "name": "English Male",
                    "gender": "male",
                    "language": "en",
                    "description": "English male voice"
                },
                {
                    "voice_id": "female-shaonv",
                    "name": "English Female",
                    "gender": "female",
                    "language": "en",
                    "description": "English female voice"
                }
            ]

        elif provider == "elevenlabs":
            # Try to get ElevenLabs V2 voices from the service
            if unified_tts_service:
                try:
                    if not hasattr(unified_tts_service, '_initialized') or not unified_tts_service._initialized:
                        await unified_tts_service.initialize()

                    # Get ElevenLabs voices from the service
                    elevenlabs_voices = await unified_tts_service.tts_manager.get_available_voices("en")
                    if elevenlabs_voices:
                        voices = [voice.dict() for voice in elevenlabs_voices]
                except Exception as e:
                    print(f"Failed to get ElevenLabs voices from service: {e}")

            # Fallback to default ElevenLabs V2 voices if service fails
            if not voices:
                voices = [
                    {
                        "voice_id": "21m00Tcm4TlvDq8ikWAM",
                        "name": "Rachel",
                        "gender": "female",
                        "language": "en",
                        "description": "Young American female voice"
                    },
                    {
                        "voice_id": "AZnzlk1XvdvUeBnXmlld",
                        "name": "Domi",
                        "gender": "female",
                        "language": "en",
                        "description": "Strong American female voice"
                    },
                    {
                        "voice_id": "EXAVITQu4vr4xnSDxMaL",
                        "name": "Bella",
                        "gender": "female",
                        "language": "en",
                        "description": "Soft American female voice"
                    },
                    {
                        "voice_id": "ErXwobaYiN019PkySvjV",
                        "name": "Antoni",
                        "gender": "male",
                        "language": "en",
                        "description": "Well-rounded American male voice"
                    },
                    {
                        "voice_id": "VR6AewLTigWG4xSOukaG",
                        "name": "Arnold",
                        "gender": "male",
                        "language": "en",
                        "description": "Crisp American male voice"
                    }
                ]

        elif provider == "elevenlabs_dialogue":
            # ElevenLabs Text-to-Dialogue voices optimized for conversation
            voices = [
                {
                    "voice_id": "21m00Tcm4TlvDq8ikWAM",
                    "name": "Rachel (Dialogue)",
                    "gender": "female",
                    "language": "en",
                    "category": "dialogue_premium",
                    "description": "Premium conversational voice optimized for natural dialogue",
                    "features": ["Text-to-Dialogue", "Natural Conversation", "Emotion Control"]
                },
                {
                    "voice_id": "ErXwobaYiN019PkySvjV",
                    "name": "Antoni (Dialogue)",
                    "gender": "male",
                    "language": "en",
                    "category": "dialogue_narration",
                    "description": "Professional male voice for dialogue and narration",
                    "features": ["Text-to-Dialogue", "Professional Quality", "Natural Flow"]
                },
                {
                    "voice_id": "EXAVITQu4vr4xnSDxMaL",
                    "name": "Bella (Dialogue)",
                    "gender": "female",
                    "language": "en",
                    "category": "dialogue_conversational",
                    "description": "Natural conversational voice for dialogue",
                    "features": ["Text-to-Dialogue", "Conversational", "Expressive"]
                },
                {
                    "voice_id": "VR6AewLTigWG4xSOukaG",
                    "name": "Arnold (Dialogue)",
                    "gender": "male",
                    "language": "en",
                    "category": "dialogue_premium",
                    "description": "Premium male voice for dialogue",
                    "features": ["Text-to-Dialogue", "Professional Quality", "Clear Audio"]
                }
            ]

        elif provider == "elevenlabs_v3":
            # For V3, we'll use the same voice IDs as V2 but with V3 model
            # The difference is in the model_id parameter, not the voice_id
            voices = [
                {
                    "voice_id": "21m00Tcm4TlvDq8ikWAM",
                    "name": "Rachel (V3 Premium)",
                    "gender": "female",
                    "language": "en",
                    "category": "v3_premium",
                    "description": "Premium quality young American female voice with V3 enhancements",
                    "features": ["V3 Enhanced", "Professional Quality", "Emotion Control"]
                },
                {
                    "voice_id": "AZnzlk1XvdvUeBnXmlld",
                    "name": "Domi (V3 Multilingual)",
                    "gender": "female",
                    "language": "en",
                    "category": "v3_multilingual",
                    "description": "Multilingual V3 voice supporting 16+ languages",
                    "features": ["V3 Enhanced", "Multilingual", "Professional Quality"]
                },
                {
                    "voice_id": "EXAVITQu4vr4xnSDxMaL",
                    "name": "Bella (V3 Conversational)",
                    "gender": "female",
                    "language": "en",
                    "category": "v3_conversational",
                    "description": "Natural conversational V3 voice for dialogue",
                    "features": ["V3 Enhanced", "Conversational", "Natural Flow"]
                },
                {
                    "voice_id": "ErXwobaYiN019PkySvjV",
                    "name": "Antoni (V3 Narration)",
                    "gender": "male",
                    "language": "en",
                    "category": "v3_narration",
                    "description": "Professional V3 narration voice for storytelling",
                    "features": ["V3 Enhanced", "Narration", "Professional Quality"]
                },
                {
                    "voice_id": "VR6AewLTigWG4xSOukaG",
                    "name": "Arnold (V3 Premium)",
                    "gender": "male",
                    "language": "en",
                    "category": "v3_premium",
                    "description": "Premium quality male voice with V3 enhancements",
                    "features": ["V3 Enhanced", "Professional Quality", "Crisp Audio"]
                },
                {
                    "voice_id": "pNInz6obpgDQGcFmaJgB",
                    "name": "Adam (V3 Multilingual)",
                    "gender": "male",
                    "language": "en",
                    "category": "v3_multilingual",
                    "description": "Deep male voice with multilingual V3 support",
                    "features": ["V3 Enhanced", "Multilingual", "Deep Voice"]
                }
            ]

        return {
            "provider": provider,
            "voices": voices
        }

    except Exception as e:
        return {"error": str(e)}


@app.post("/api/generate-audio")
async def generate_audio(
    session_id: str = Form(...),
    approved_script: str = Form(...),
    voice_selections: str = Form(...),
    tts_provider: str = Form(default="minimax")
):
    """Generate audio with specified TTS provider"""
    if session_id not in generation_sessions:
        raise HTTPException(status_code=404, detail="Session not found")

    session = generation_sessions[session_id]

    try:
        import json
        import time

        # Parse approved script
        script_data = json.loads(approved_script)

        # Parse voice selections
        voice_data = json.loads(voice_selections)

        # Update dialogue with selected voices
        dialogue_lines = []
        for line in script_data.get("dialogue", []):
            speaker_name = line["speaker_name"]
            selected_voice = voice_data.get(speaker_name, {})

            dialogue_lines.append(DialogueLine(
                role=line["role"],
                speaker_name=speaker_name,
                text=line["text"],
                voice_id=selected_voice.get("voice_id"),
                emotion=line.get("emotion", "neutral")
            ))

        # Create updated script
        updated_script = PodcastScript(
            title=script_data.get("title", "Generated Podcast"),
            description=script_data.get("description", ""),
            dialogue=dialogue_lines,
            language=session["request"].language
        )

        # Update session status
        generation_sessions[session_id]["status"] = "synthesizing_audio"
        generation_sessions[session_id]["tts_provider"] = tts_provider
        generation_sessions[session_id]["script"] = updated_script

        # Choose TTS service based on provider
        start_time = time.time()

        if tts_provider == "auto" or (unified_tts_service and tts_provider in ["elevenlabs", "elevenlabs_v3", "elevenlabs_dialogue", "minimax"]):
            # Use unified TTS service
            if not unified_tts_service:
                raise Exception("Unified TTS service not available")

            # Initialize if needed
            if not hasattr(unified_tts_service, '_initialized') or not unified_tts_service._initialized:
                await unified_tts_service.initialize()

            # Configure TTS provider
            preferred_provider = None if tts_provider == "auto" else tts_provider

            # Generate audio
            audio_result = await unified_tts_service.synthesize_podcast_audio(
                script=updated_script,
                preferred_provider=preferred_provider
            )

        else:
            # Use legacy TTS service (MiniMax)
            if not tts_service:
                raise Exception("TTS service not available")

            audio_result = await tts_service.synthesize_podcast(updated_script)

        generation_time = time.time() - start_time

        # Update session with results
        generation_sessions[session_id]["audio"] = audio_result
        generation_sessions[session_id]["generation_time"] = generation_time

        if audio_result.get("success", False):
            generation_sessions[session_id]["status"] = "completed"
            generation_sessions[session_id]["provider_used"] = audio_result.get("provider", tts_provider)
            generation_sessions[session_id]["fallback_used"] = audio_result.get("fallback_used", False)
        else:
            generation_sessions[session_id]["status"] = "error"
            generation_sessions[session_id]["error"] = audio_result.get("error", "Audio generation failed")

        return {
            "success": audio_result.get("success", False),
            "audio_url": audio_result.get("audio_url"),
            "provider_used": audio_result.get("provider", tts_provider),
            "fallback_used": audio_result.get("fallback_used", False),
            "generation_time": generation_time,
            "error": audio_result.get("error") if not audio_result.get("success") else None
        }

    except Exception as e:
        generation_sessions[session_id]["status"] = "error"
        generation_sessions[session_id]["error"] = str(e)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/approve-script")
async def approve_script(
    session_id: str = Form(...),
    approved_script: str = Form(...),
    voice_selections: str = Form(...)
):
    """Approve script with voice selections and start audio generation (Phase 2 - HITL workflow)"""
    if session_id not in generation_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = generation_sessions[session_id]
    
    # Verify session is in correct state
    if session["status"] != "script_ready":
        raise HTTPException(status_code=400, detail=f"Session not ready for approval. Current status: {session['status']}")
    
    try:
        import json
        
        # Parse approved script
        script_data = json.loads(approved_script)
        
        # Parse voice selections
        voice_data = json.loads(voice_selections)
        
        # Update dialogue with selected voices
        dialogue_lines = []
        for i, line in enumerate(script_data.get("dialogue", [])):
            speaker_name = line["speaker_name"]
            selected_voice = voice_data.get(speaker_name, {})
            
            dialogue_lines.append(DialogueLine(
                role=line["role"],
                speaker_name=speaker_name,
                text=line["text"],
                voice_id=selected_voice.get("voice_id"),
                emotion=selected_voice.get("emotion", "auto")
            ))
        
        # Create updated script with voice selections
        approved_podcast_script = PodcastScript(
            title=script_data.get("title", "Approved Podcast"),
            description=script_data.get("description", ""),
            dialogue=dialogue_lines
        )
        
        # Update session with approved script
        generation_sessions[session_id]["script"] = approved_podcast_script
        generation_sessions[session_id]["status"] = "synthesizing_audio"
        
        # Start audio generation asynchronously
        import asyncio
        asyncio.create_task(generate_audio_async(session_id, approved_podcast_script))
        
        return {"success": True, "message": "Script approved, audio generation started"}
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error processing approval: {str(e)}")


async def generate_audio_async(session_id: str, script: PodcastScript):
    """Generate audio for approved script"""
    try:
        if tts_service:
            generation_sessions[session_id]["status"] = "synthesizing_audio"
            audio_result = await tts_service.synthesize_podcast(script)
            generation_sessions[session_id]["audio"] = audio_result
            generation_sessions[session_id]["status"] = "completed"
        else:
            generation_sessions[session_id]["status"] = "error"
            generation_sessions[session_id]["error"] = "TTS service not available"
            
    except Exception as e:
        generation_sessions[session_id]["status"] = "error"
        generation_sessions[session_id]["error"] = str(e)
        print(f"Error in async audio generation: {e}")


@app.delete("/api/session/{session_id}")
async def delete_session(session_id: str):
    """Clean up a generation session"""
    if session_id in generation_sessions:
        # TODO: Also clean up audio files
        del generation_sessions[session_id]
        return {"success": True}
    else:
        raise HTTPException(status_code=404, detail="Session not found")


if __name__ == "__main__":
    port = int(os.getenv("APP_PORT", 8000))
    host = os.getenv("APP_HOST", "0.0.0.0")
    debug = os.getenv("DEBUG", "False").lower() == "true"
    
    uvicorn.run(
        "app:app",
        host=host,
        port=port,
        reload=debug
    )