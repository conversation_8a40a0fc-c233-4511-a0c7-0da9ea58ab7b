/**
 * Commercial AI Podcast Studio - Main JavaScript
 * Professional-grade interactions and functionality
 */

class CommercialPodcastStudio {
    constructor() {
        this.currentUser = null;
        this.projects = [];
        this.systemStatus = {};
        this.analytics = {};
        
        this.init();
    }

    init() {
        this.bindGlobalEvents();
        this.initializeComponents();
        this.loadUserData();
        this.startStatusMonitoring();
    }

    bindGlobalEvents() {
        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Command/Ctrl + K for search
            if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                e.preventDefault();
                this.focusSearch();
            }
            
            // Escape to close modals/dropdowns
            if (e.key === 'Escape') {
                this.closeAllDropdowns();
            }
        });

        // Click outside to close dropdowns
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.user-menu')) {
                this.closeUserDropdown();
            }
        });

        // Handle responsive navigation
        window.addEventListener('resize', () => {
            this.handleResponsiveLayout();
        });
    }

    initializeComponents() {
        this.initializeSearch();
        this.initializeNotifications();
        this.initializeUserMenu();
        this.initializeDashboard();
    }

    // Search Functionality
    initializeSearch() {
        const searchInput = document.querySelector('.search-input');
        if (!searchInput) return;

        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });

        searchInput.addEventListener('focus', () => {
            this.showSearchSuggestions();
        });
    }

    focusSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    performSearch(query) {
        if (query.length < 2) {
            this.hideSearchResults();
            return;
        }

        // Simulate search API call
        console.log('Searching for:', query);
        
        // In a real implementation, this would call the search API
        this.displaySearchResults([
            { type: 'project', title: 'AI in Healthcare', status: 'in-progress' },
            { type: 'project', title: 'Market Analysis', status: 'completed' },
            { type: 'template', title: 'Interview Template', category: 'template' }
        ]);
    }

    showSearchSuggestions() {
        // Show recent searches or popular items
        console.log('Showing search suggestions');
    }

    displaySearchResults(results) {
        // Display search results dropdown
        console.log('Search results:', results);
    }

    hideSearchResults() {
        // Hide search results dropdown
        console.log('Hiding search results');
    }

    // Notifications
    initializeNotifications() {
        const notificationBtn = document.querySelector('.notifications-btn');
        if (!notificationBtn) return;

        notificationBtn.addEventListener('click', () => {
            this.toggleNotifications();
        });

        // Simulate real-time notifications
        this.simulateNotifications();
    }

    toggleNotifications() {
        console.log('Toggle notifications panel');
        // In a real implementation, this would show/hide notifications panel
    }

    simulateNotifications() {
        // Simulate receiving notifications
        setTimeout(() => {
            this.addNotification({
                type: 'success',
                title: 'Project Completed',
                message: 'AI in Healthcare podcast has been generated successfully',
                timestamp: new Date()
            });
        }, 5000);
    }

    addNotification(notification) {
        console.log('New notification:', notification);
        // Update notification badge
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            const count = parseInt(badge.textContent) + 1;
            badge.textContent = count;
        }
    }

    // User Menu
    initializeUserMenu() {
        const userMenuBtn = document.querySelector('.user-avatar-btn');
        if (!userMenuBtn) return;

        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleUserDropdown();
        });
    }

    toggleUserDropdown() {
        const dropdown = document.querySelector('.user-dropdown');
        if (!dropdown) return;

        const isVisible = dropdown.style.opacity === '1';
        if (isVisible) {
            this.closeUserDropdown();
        } else {
            this.openUserDropdown();
        }
    }

    openUserDropdown() {
        const dropdown = document.querySelector('.user-dropdown');
        if (dropdown) {
            dropdown.style.opacity = '1';
            dropdown.style.visibility = 'visible';
            dropdown.style.transform = 'translateY(0)';
        }
    }

    closeUserDropdown() {
        const dropdown = document.querySelector('.user-dropdown');
        if (dropdown) {
            dropdown.style.opacity = '0';
            dropdown.style.visibility = 'hidden';
            dropdown.style.transform = 'translateY(-0.5rem)';
        }
    }

    closeAllDropdowns() {
        this.closeUserDropdown();
        this.hideSearchResults();
    }

    // Dashboard Functionality
    initializeDashboard() {
        if (!document.querySelector('.dashboard-page')) return;

        this.initializeMetrics();
        this.initializeProjectList();
        this.initializeQuickActions();
        this.initializeUsageChart();
    }

    initializeMetrics() {
        // Animate metric values on load
        const metricValues = document.querySelectorAll('.metric-value');
        metricValues.forEach(metric => {
            this.animateCounter(metric);
        });
    }

    animateCounter(element) {
        const target = parseInt(element.textContent);
        const duration = 1000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }

    initializeProjectList() {
        const projectItems = document.querySelectorAll('.project-item');
        projectItems.forEach(item => {
            item.addEventListener('click', () => {
                const projectId = item.dataset.projectId;
                this.openProject(projectId);
            });
        });
    }

    openProject(projectId) {
        // Navigate to project page
        window.location.href = `/projects/${projectId}`;
    }

    initializeQuickActions() {
        const actionItems = document.querySelectorAll('.action-item');
        actionItems.forEach(item => {
            item.addEventListener('click', () => {
                const action = item.dataset.action;
                this.handleQuickAction(action);
            });
        });
    }

    handleQuickAction(action) {
        switch (action) {
            case 'new-project':
                window.location.href = '/projects/new';
                break;
            case 'browse-templates':
                window.location.href = '/library';
                break;
            case 'import-script':
                this.showImportDialog();
                break;
            case 'view-analytics':
                window.location.href = '/analytics';
                break;
            default:
                console.log('Unknown action:', action);
        }
    }

    showImportDialog() {
        // Show import script dialog
        console.log('Show import dialog');
    }

    initializeUsageChart() {
        const chartCanvas = document.getElementById('usageChart');
        if (!chartCanvas) return;

        // Initialize Chart.js chart
        const ctx = chartCanvas.getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [
                    {
                        label: 'Projects Created',
                        data: [12, 19, 15, 25],
                        borderColor: 'rgb(14, 165, 233)',
                        backgroundColor: 'rgba(14, 165, 233, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Audio Generated',
                        data: [8, 15, 12, 20],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Data Loading
    async loadUserData() {
        try {
            // Simulate API calls
            this.currentUser = await this.fetchUserProfile();
            this.projects = await this.fetchUserProjects();
            this.analytics = await this.fetchAnalytics();
        } catch (error) {
            console.error('Failed to load user data:', error);
            this.showErrorNotification('Failed to load user data');
        }
    }

    async fetchUserProfile() {
        // Simulate API call
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    id: 'user_123',
                    name: 'John Doe',
                    email: '<EMAIL>',
                    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
                    plan: 'pro',
                    usage: {
                        current: 94,
                        limit: 100
                    }
                });
            }, 500);
        });
    }

    async fetchUserProjects() {
        // Simulate API call
        return new Promise(resolve => {
            setTimeout(() => {
                resolve([
                    {
                        id: 'proj_1',
                        title: 'AI in Healthcare: Future Trends',
                        status: 'in-progress',
                        progress: 75,
                        lastModified: '2 hours ago'
                    },
                    {
                        id: 'proj_2',
                        title: 'Market Analysis Q4 2024',
                        status: 'completed',
                        progress: 100,
                        lastModified: '5 hours ago'
                    }
                ]);
            }, 700);
        });
    }

    async fetchAnalytics() {
        // Simulate API call
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    activeProjects: 24,
                    completedThisMonth: 156,
                    totalAudioHours: 47.2,
                    usagePercentage: 94
                });
            }, 600);
        });
    }

    // System Status Monitoring
    startStatusMonitoring() {
        this.checkSystemStatus();
        
        // Check status every 30 seconds
        setInterval(() => {
            this.checkSystemStatus();
        }, 30000);
    }

    async checkSystemStatus() {
        try {
            const status = await this.fetchSystemStatus();
            this.updateStatusDisplay(status);
        } catch (error) {
            console.error('Failed to check system status:', error);
        }
    }

    async fetchSystemStatus() {
        // In a real implementation, this would call the API
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    elevenlabs: { status: 'online', latency: 120 },
                    minimax: { status: 'online', latency: 85 },
                    openrouter: { status: 'online', latency: 95 },
                    voiceLibrary: { status: 'syncing', progress: 78 }
                });
            }, 200);
        });
    }

    updateStatusDisplay(status) {
        this.systemStatus = status;
        
        // Update status indicators in the UI
        Object.keys(status).forEach(service => {
            const serviceElement = document.querySelector(`[data-service="${service}"]`);
            if (serviceElement) {
                const statusElement = serviceElement.querySelector('.service-status');
                if (statusElement) {
                    statusElement.className = `service-status ${status[service].status}`;
                    statusElement.querySelector('span').textContent = 
                        status[service].status.charAt(0).toUpperCase() + status[service].status.slice(1);
                }
            }
        });
    }

    // Responsive Layout
    handleResponsiveLayout() {
        const width = window.innerWidth;
        
        if (width < 768) {
            this.enableMobileLayout();
        } else {
            this.enableDesktopLayout();
        }
    }

    enableMobileLayout() {
        // Adjust layout for mobile
        console.log('Enabling mobile layout');
    }

    enableDesktopLayout() {
        // Adjust layout for desktop
        console.log('Enabling desktop layout');
    }

    // Utility Methods
    showErrorNotification(message) {
        console.error('Error:', message);
        // In a real implementation, this would show a toast notification
    }

    showSuccessNotification(message) {
        console.log('Success:', message);
        // In a real implementation, this would show a toast notification
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.podcastStudio = new CommercialPodcastStudio();
});
