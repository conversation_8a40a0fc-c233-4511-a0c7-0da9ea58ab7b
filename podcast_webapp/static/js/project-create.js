/**
 * Project Creation Wizard - JavaScript
 * Multi-step project creation with validation and state management
 */

class ProjectCreationWizard {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 3;
        this.projectData = {
            template: 'interview',
            name: '',
            description: '',
            language: 'en',
            category: 'technology',
            settings: {
                collaboration: true,
                autoSave: true,
                versionHistory: false
            },
            content: {},
            ttsProvider: null
        };
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeStep1();
        this.updateProgress();
    }

    bindEvents() {
        // Template selection
        document.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', () => this.selectTemplate(card));
        });

        // Form inputs
        document.getElementById('projectName')?.addEventListener('input', (e) => {
            this.projectData.name = e.target.value;
            this.validateCurrentStep();
        });

        document.getElementById('projectDescription')?.addEventListener('input', (e) => {
            this.projectData.description = e.target.value;
        });

        document.getElementById('projectLanguage')?.addEventListener('change', (e) => {
            this.projectData.language = e.target.value;
        });

        document.getElementById('projectCategory')?.addEventListener('change', (e) => {
            this.projectData.category = e.target.value;
        });

        // Checkboxes
        document.querySelectorAll('.checkbox-item input').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const setting = e.target.id.replace('enable', '').replace('auto', '').toLowerCase();
                this.projectData.settings[setting] = e.target.checked;
            });
        });

        // Navigation buttons
        document.getElementById('nextBtn')?.addEventListener('click', () => this.nextStep());
        document.getElementById('prevBtn')?.addEventListener('click', () => this.prevStep());

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                this.nextStep();
            }
        });
    }

    initializeStep1() {
        // Pre-select interview template
        const interviewCard = document.querySelector('[data-template="interview"]');
        if (interviewCard) {
            this.selectTemplate(interviewCard);
        }
    }

    selectTemplate(card) {
        // Remove previous selection
        document.querySelectorAll('.template-card').forEach(c => {
            c.classList.remove('selected');
        });

        // Select new template
        card.classList.add('selected');
        this.projectData.template = card.dataset.template;

        // Apply template defaults
        this.applyTemplateDefaults(this.projectData.template);
        this.validateCurrentStep();
    }

    applyTemplateDefaults(templateType) {
        const templates = {
            blank: {
                speakers: 1,
                duration: 600,
                style: 'conversational'
            },
            interview: {
                speakers: 2,
                duration: 900,
                style: 'interview',
                targetAudience: 'professional'
            },
            educational: {
                speakers: 1,
                duration: 1200,
                style: 'educational',
                targetAudience: 'general'
            },
            news: {
                speakers: 2,
                duration: 300,
                style: 'news',
                targetAudience: 'general'
            },
            storytelling: {
                speakers: 3,
                duration: 1800,
                style: 'storytelling',
                targetAudience: 'general'
            },
            business: {
                speakers: 3,
                duration: 1500,
                style: 'business',
                targetAudience: 'professional'
            }
        };

        const defaults = templates[templateType] || templates.blank;
        this.projectData.content = { ...this.projectData.content, ...defaults };
    }

    validateCurrentStep() {
        let isValid = false;

        switch (this.currentStep) {
            case 1:
                isValid = this.validateStep1();
                break;
            case 2:
                isValid = this.validateStep2();
                break;
            case 3:
                isValid = this.validateStep3();
                break;
        }

        this.updateNextButton(isValid);
        return isValid;
    }

    validateStep1() {
        const hasTemplate = this.projectData.template !== null;
        const hasName = this.projectData.name.trim().length >= 3;
        
        return hasTemplate && hasName;
    }

    validateStep2() {
        // Step 2 validation will be implemented when we create step 2
        return true;
    }

    validateStep3() {
        // Step 3 validation will be implemented when we create step 3
        return true;
    }

    updateNextButton(isValid) {
        const nextBtn = document.getElementById('nextBtn');
        if (!nextBtn) return;

        nextBtn.disabled = !isValid;
        nextBtn.classList.toggle('disabled', !isValid);

        // Update button text based on step
        const buttonTexts = {
            1: 'Next: Content Configuration',
            2: 'Next: TTS Provider',
            3: 'Create Project'
        };

        const span = nextBtn.querySelector('span');
        if (span) {
            span.textContent = buttonTexts[this.currentStep] || 'Next';
        }
    }

    nextStep() {
        if (!this.validateCurrentStep()) {
            this.showValidationError();
            return;
        }

        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.showStep(this.currentStep);
            this.updateProgress();
        } else {
            this.createProject();
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.showStep(this.currentStep);
            this.updateProgress();
        }
    }

    showStep(stepNumber) {
        // Hide all steps
        document.querySelectorAll('.wizard-step').forEach(step => {
            step.classList.remove('active');
        });

        // Show current step
        const currentStepElement = document.getElementById(`step${stepNumber}`);
        if (currentStepElement) {
            currentStepElement.classList.add('active');
        }

        // Update navigation buttons
        this.updateNavigationButtons();

        // Load step content if needed
        this.loadStepContent(stepNumber);
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        if (prevBtn) {
            prevBtn.style.display = this.currentStep > 1 ? 'flex' : 'none';
        }

        if (nextBtn) {
            const isLastStep = this.currentStep === this.totalSteps;
            const icon = nextBtn.querySelector('i');
            
            if (isLastStep) {
                nextBtn.classList.remove('btn-secondary');
                nextBtn.classList.add('btn-primary');
                if (icon) {
                    icon.className = 'fas fa-check';
                }
            } else {
                nextBtn.classList.remove('btn-primary');
                nextBtn.classList.add('btn-primary');
                if (icon) {
                    icon.className = 'fas fa-arrow-right';
                }
            }
        }

        this.validateCurrentStep();
    }

    updateProgress() {
        // Update progress steps
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
            }
        });

        // Update progress connectors
        document.querySelectorAll('.progress-connector').forEach((connector, index) => {
            connector.classList.remove('completed');
            if (index + 1 < this.currentStep) {
                connector.classList.add('completed');
            }
        });
    }

    loadStepContent(stepNumber) {
        switch (stepNumber) {
            case 2:
                this.loadStep2Content();
                break;
            case 3:
                this.loadStep3Content();
                break;
        }
    }

    loadStep2Content() {
        // This would load the content configuration step
        console.log('Loading step 2 content');
        // For now, we'll just validate
        this.validateCurrentStep();
    }

    loadStep3Content() {
        // This would load the TTS provider selection step
        console.log('Loading step 3 content');
        // For now, we'll just validate
        this.validateCurrentStep();
    }

    showValidationError() {
        let message = 'Please complete all required fields.';

        switch (this.currentStep) {
            case 1:
                if (!this.projectData.template) {
                    message = 'Please select a template.';
                } else if (this.projectData.name.trim().length < 3) {
                    message = 'Project name must be at least 3 characters long.';
                }
                break;
        }

        this.showNotification(message, 'error');
    }

    async createProject() {
        try {
            this.showLoading('Creating your project...');

            // Simulate API call
            const response = await this.submitProject();
            
            if (response.success) {
                this.showNotification('Project created successfully!', 'success');
                
                // Redirect to the new project
                setTimeout(() => {
                    window.location.href = `/projects/${response.projectId}`;
                }, 1500);
            } else {
                throw new Error(response.error || 'Failed to create project');
            }
        } catch (error) {
            console.error('Project creation failed:', error);
            this.showNotification('Failed to create project. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async submitProject() {
        // Simulate API call
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    projectId: 'proj_' + Date.now(),
                    message: 'Project created successfully'
                });
            }, 2000);
        });
    }

    showLoading(message) {
        // Create loading overlay
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-text">${message}</div>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            .loading-content {
                background: white;
                padding: 2rem;
                border-radius: 1rem;
                text-align: center;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            }
            .loading-spinner {
                width: 3rem;
                height: 3rem;
                border: 3px solid #e5e5e5;
                border-top: 3px solid #0ea5e9;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 1rem;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .loading-text {
                color: #404040;
                font-weight: 500;
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(overlay);
    }

    hideLoading() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    showNotification(message, type = 'info') {
        // Create notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .notification {
                position: fixed;
                top: 2rem;
                right: 2rem;
                background: white;
                border: 1px solid #e5e5e5;
                border-radius: 0.75rem;
                padding: 1rem 1.5rem;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                animation: slideIn 0.3s ease;
            }
            .notification-success {
                border-left: 4px solid #22c55e;
            }
            .notification-error {
                border-left: 4px solid #ef4444;
            }
            .notification-content {
                display: flex;
                align-items: center;
                gap: 0.75rem;
                color: #404040;
            }
            .notification-success .fa-check-circle {
                color: #22c55e;
            }
            .notification-error .fa-exclamation-circle {
                color: #ef4444;
            }
            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateX(100%);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize the wizard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.projectWizard = new ProjectCreationWizard();
});
