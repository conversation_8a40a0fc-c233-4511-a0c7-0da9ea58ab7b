/**
 * TrendCast AI - Gen Z Pop Culture Platform
 * Interactive features and animations
 */

class TrendCastApp {
    constructor() {
        this.isLoaded = false;
        this.trendingData = [];
        this.userInteractions = [];
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initAnimations();
        this.loadTrendingData();
        this.startInteractiveElements();
        this.setupNavigationScroll();

        // Mark as loaded after initial setup
        setTimeout(() => {
            this.isLoaded = true;
            this.triggerLoadAnimations();
        }, 500);
    }

    bindEvents() {
        // Navigation interactions
        this.bindNavigation();
        
        // Hero section interactions
        this.bindHeroActions();
        
        // Trending section interactions
        this.bindTrendingActions();
        
        // Feature interactions
        this.bindFeatureActions();
        
        // Global interactions
        this.bindGlobalActions();
    }

    bindNavigation() {
        // Mobile menu toggle (for future mobile menu)
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                // Remove active class from all links
                navLinks.forEach(l => l.classList.remove('active'));
                // Add active class to clicked link
                e.currentTarget.classList.add('active');
                
                // Smooth scroll to section
                const href = e.currentTarget.getAttribute('href');
                if (href.startsWith('#')) {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        });

        // Search functionality
        const searchBtn = document.querySelector('.search-btn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.openSearchModal();
            });
        }

        // Profile menu
        const profileBtn = document.querySelector('.profile-btn');
        if (profileBtn) {
            profileBtn.addEventListener('click', () => {
                this.toggleProfileMenu();
            });
        }
    }

    bindHeroActions() {
        // Primary CTA button
        const ctaPrimary = document.querySelector('.cta-primary');
        if (ctaPrimary) {
            ctaPrimary.addEventListener('click', () => {
                this.startContentCreation();
            });
        }

        // Secondary CTA button (demo)
        const ctaSecondary = document.querySelector('.cta-secondary');
        if (ctaSecondary) {
            ctaSecondary.addEventListener('click', () => {
                this.playDemo();
            });
        }

        // Add hover effects to hero stats
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                this.animateStatNumber(item);
            });
        });
    }

    bindTrendingActions() {
        // Enhanced analyze buttons with loading states
        const analyzeButtons = document.querySelectorAll('.analyze-btn');
        analyzeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const trendingItem = e.currentTarget.closest('.trending-item');
                this.analyzeTrendWithLoading(btn, trendingItem);
            });
        });

        // Enhanced trending item interactions
        const trendingItems = document.querySelectorAll('.trending-item');
        trendingItems.forEach(item => {
            // Mouse enter with micro-interactions
            item.addEventListener('mouseenter', () => {
                this.enhancedHighlightTrendingItem(item);
            });

            // Mouse leave with smooth transitions
            item.addEventListener('mouseleave', () => {
                this.enhancedUnhighlightTrendingItem(item);
            });

            // Click interactions with ripple effect
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.analyze-btn')) {
                    this.createRippleEffect(e, item);
                    this.previewTrendingItem(item);
                }
            });
        });
    }

    bindFeatureActions() {
        // Feature card interactions
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach(card => {
            card.addEventListener('click', () => {
                this.exploreFeature(card);
            });
        });
    }

    bindGlobalActions() {
        // Floating Action Button
        const fab = document.getElementById('createFab');
        if (fab) {
            fab.addEventListener('click', () => {
                this.quickCreate();
            });
        }

        // CTA Section button
        const ctaButton = document.querySelector('.cta-button');
        if (ctaButton) {
            ctaButton.addEventListener('click', () => {
                this.startContentCreation();
            });
        }

        // Scroll effects
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    initAnimations() {
        // Initialize intersection observer for scroll animations
        this.observeElements();
        
        // Start background animations
        this.animateBackground();
        
        // Initialize audio visualizer
        this.animateAudioVisualizer();
    }

    observeElements() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe sections for scroll animations
        const sections = document.querySelectorAll('section');
        sections.forEach(section => {
            observer.observe(section);
        });
    }

    animateBackground() {
        // Add subtle movement to gradient orbs
        const orbs = document.querySelectorAll('.gradient-orb');
        orbs.forEach((orb, index) => {
            setInterval(() => {
                const randomX = Math.random() * 20 - 10;
                const randomY = Math.random() * 20 - 10;
                orb.style.transform = `translate(${randomX}px, ${randomY}px)`;
            }, 3000 + index * 1000);
        });
    }

    animateAudioVisualizer() {
        const waveBars = document.querySelectorAll('.wave-bar');
        
        setInterval(() => {
            waveBars.forEach(bar => {
                const randomHeight = Math.random() * 40 + 10;
                bar.style.height = `${randomHeight}px`;
            });
        }, 150);
    }

    triggerLoadAnimations() {
        // Trigger entrance animations
        const heroContent = document.querySelector('.hero-content');
        if (heroContent) {
            heroContent.classList.add('slide-up');
        }

        const heroVisual = document.querySelector('.hero-visual');
        if (heroVisual) {
            heroVisual.classList.add('bounce-in');
        }
    }

    // Action Handlers
    startContentCreation() {
        this.showNotification('🚀 Launching content creator...', 'success');
        
        // Simulate navigation to content creation
        setTimeout(() => {
            // In a real app, this would navigate to the creation page
            window.location.href = '/create';
        }, 1000);
    }

    playDemo() {
        this.showNotification('🎬 Loading demo video...', 'info');
        
        // Create demo modal
        this.createDemoModal();
    }

    // Enhanced analyze trend with real API integration
    async analyzeTrendWithLoading(button, trendingItem) {
        const title = trendingItem.querySelector('h3').textContent;
        const platform = this.getPlatformFromBadge(trendingItem);

        // Add sophisticated loading state
        this.setButtonLoadingState(button, true);
        this.addCardLoadingState(trendingItem);

        // Show enhanced notification
        this.showNotification(`🧠 AI正在分析"${title}"...`, 'info');

        try {
            // Call real API
            const response = await fetch('/api/genz/analyze-trend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    topic: title,
                    platform: platform
                })
            });

            const result = await response.json();

            if (result.success) {
                // Success state
                this.setButtonSuccessState(button);
                this.removeCardLoadingState(trendingItem);
                this.showAnalysisResults(result.data);
                this.showNotification(`✅ "${title}"分析完成！`, 'success');
            } else {
                throw new Error(result.error || '分析失败');
            }

        } catch (error) {
            // Error state
            this.setButtonErrorState(button);
            this.removeCardLoadingState(trendingItem);
            this.showNotification(`❌ 分析失败: ${error.message}`, 'error');
        } finally {
            // Reset button after delay
            setTimeout(() => {
                this.resetButtonState(button);
            }, 3000);
        }
    }

    getPlatformFromBadge(trendingItem) {
        const badge = trendingItem.querySelector('.trending-badge');
        if (badge.classList.contains('tiktok-badge')) return 'tiktok';
        if (badge.classList.contains('instagram-badge')) return 'instagram';
        if (badge.classList.contains('youtube-badge')) return 'youtube';
        return 'tiktok'; // default
    }

    setButtonLoadingState(button, loading) {
        if (loading) {
            button.classList.add('btn-loading');
            button.disabled = true;
            button.style.pointerEvents = 'none';
        } else {
            button.classList.remove('btn-loading');
            button.disabled = false;
            button.style.pointerEvents = 'auto';
        }
    }

    setButtonSuccessState(button) {
        button.classList.remove('btn-loading');
        button.classList.add('state-success');
        button.innerHTML = '<i class="fas fa-check"></i> Analyzed!';
    }

    setButtonErrorState(button) {
        button.classList.remove('btn-loading');
        button.classList.add('state-error');
        button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
    }

    resetButtonState(button) {
        button.classList.remove('state-success', 'state-error', 'btn-loading');
        button.innerHTML = '<i class="fas fa-chart-bar"></i> Analyze';
        button.disabled = false;
        button.style.pointerEvents = 'auto';
    }

    addCardLoadingState(card) {
        card.style.opacity = '0.7';
        card.style.pointerEvents = 'none';

        // Add subtle pulse animation
        card.classList.add('pulse-loading');
    }

    removeCardLoadingState(card) {
        card.style.opacity = '1';
        card.style.pointerEvents = 'auto';
        card.classList.remove('pulse-loading');
    }

    async simulateAnalysisProgress(item) {
        const steps = [
            'Gathering data from social platforms...',
            'Analyzing engagement patterns...',
            'Processing sentiment analysis...',
            'Generating insights...'
        ];

        for (let i = 0; i < steps.length; i++) {
            await new Promise(resolve => setTimeout(resolve, 800));
            // Could update a progress indicator here
        }
    }

    exploreFeature(featureCard) {
        const title = featureCard.querySelector('.feature-title').textContent;
        this.showNotification(`✨ Exploring ${title}...`, 'info');
        
        // Add pulse animation
        featureCard.style.animation = 'pulse 0.6s ease';
        setTimeout(() => {
            featureCard.style.animation = '';
        }, 600);
    }

    quickCreate() {
        // Animate FAB
        const fab = document.getElementById('createFab');
        fab.style.transform = 'scale(0.9)';
        setTimeout(() => {
            fab.style.transform = 'scale(1.1)';
            setTimeout(() => {
                fab.style.transform = 'scale(1)';
                this.showQuickCreateMenu();
            }, 150);
        }, 150);
    }

    // UI Helpers
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // Enhanced notification with better visual hierarchy
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas ${this.getNotificationIcon(type)}"></i>
                </div>
                <div class="notification-text">
                    <span>${message}</span>
                </div>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Enhanced styles with glassmorphism
        Object.assign(notification.style, {
            position: 'fixed',
            top: '24px',
            right: '24px',
            background: this.getNotificationBackground(type),
            color: 'white',
            padding: '0',
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.4), 0 0 20px rgba(255, 0, 110, 0.2)',
            zIndex: '10000',
            animation: 'notificationSlideIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
            backdropFilter: 'blur(20px) saturate(180%)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            minWidth: '320px',
            maxWidth: '400px'
        });

        // Add enhanced notification styles
        this.addNotificationStyles();

        document.body.appendChild(notification);

        // Enhanced auto-remove with fade out
        setTimeout(() => {
            notification.style.animation = 'notificationSlideOut 0.4s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 400);
        }, 4000);

        // Enhanced close button
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.style.animation = 'notificationSlideOut 0.4s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 400);
        });

        // Add hover effects
        notification.addEventListener('mouseenter', () => {
            notification.style.transform = 'translateX(-4px) scale(1.02)';
        });

        notification.addEventListener('mouseleave', () => {
            notification.style.transform = 'translateX(0) scale(1)';
        });
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    getNotificationBackground(type) {
        const backgrounds = {
            success: 'linear-gradient(135deg, rgba(67, 233, 123, 0.9), rgba(56, 249, 215, 0.9))',
            error: 'linear-gradient(135deg, rgba(250, 112, 154, 0.9), rgba(254, 225, 64, 0.9))',
            warning: 'linear-gradient(135deg, rgba(255, 159, 67, 0.9), rgba(255, 206, 84, 0.9))',
            info: 'linear-gradient(135deg, rgba(79, 172, 254, 0.9), rgba(0, 242, 254, 0.9))'
        };
        return backgrounds[type] || backgrounds.info;
    }

    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification-content {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px 20px;
            }

            .notification-icon {
                font-size: 20px;
                opacity: 0.9;
            }

            .notification-text {
                flex: 1;
                font-weight: 500;
                font-size: 14px;
                line-height: 1.4;
            }

            .notification-close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                opacity: 0.7;
                transition: all 0.2s ease;
            }

            .notification-close:hover {
                opacity: 1;
                background: rgba(255, 255, 255, 0.1);
                transform: scale(1.1);
            }

            @keyframes notificationSlideIn {
                from {
                    opacity: 0;
                    transform: translateX(100%) scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                }
            }

            @keyframes notificationSlideOut {
                from {
                    opacity: 1;
                    transform: translateX(0) scale(1);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%) scale(0.8);
                }
            }
        `;
        document.head.appendChild(style);
    }

    createDemoModal() {
        const modal = document.createElement('div');
        modal.className = 'demo-modal';
        modal.innerHTML = `
            <div class="modal-backdrop"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🎬 TrendCast AI Demo</h3>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="demo-video-placeholder">
                        <i class="fas fa-play-circle"></i>
                        <p>Demo video would play here</p>
                        <p class="demo-description">
                            See how TrendCast AI analyzes trending topics and creates 
                            engaging podcast content in minutes!
                        </p>
                    </div>
                </div>
            </div>
        `;

        // Add modal styles
        const style = document.createElement('style');
        style.textContent = `
            .demo-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .modal-backdrop {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(10px);
            }
            .modal-content {
                position: relative;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 20px;
                max-width: 600px;
                width: 90%;
                max-height: 80vh;
                overflow: hidden;
                animation: modalSlideIn 0.3s ease;
            }
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1.5rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            .modal-header h3 {
                color: white;
                margin: 0;
                font-size: 1.5rem;
            }
            .modal-close {
                background: none;
                border: none;
                color: white;
                font-size: 1.5rem;
                cursor: pointer;
                padding: 0.5rem;
                border-radius: 50%;
                transition: background 0.3s ease;
            }
            .modal-close:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            .modal-body {
                padding: 2rem;
            }
            .demo-video-placeholder {
                text-align: center;
                color: white;
                padding: 3rem;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 12px;
                border: 2px dashed rgba(255, 255, 255, 0.2);
            }
            .demo-video-placeholder i {
                font-size: 4rem;
                margin-bottom: 1rem;
                color: #ff006e;
            }
            .demo-description {
                margin-top: 1rem;
                color: #b0b0b0;
                line-height: 1.6;
            }
            @keyframes modalSlideIn {
                from { transform: scale(0.8); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(modal);

        // Close modal events
        const closeBtn = modal.querySelector('.modal-close');
        const backdrop = modal.querySelector('.modal-backdrop');
        
        [closeBtn, backdrop].forEach(element => {
            element.addEventListener('click', () => {
                modal.style.animation = 'modalSlideOut 0.3s ease';
                setTimeout(() => {
                    if (modal.parentNode) {
                        modal.parentNode.removeChild(modal);
                        document.head.removeChild(style);
                    }
                }, 300);
            });
        });
    }

    showAnalysisResults(analysisData) {
        // Create and show enhanced analysis modal with real data
        const modal = document.createElement('div');
        modal.className = 'analysis-modal';
        modal.innerHTML = `
            <div class="modal-backdrop" onclick="this.closest('.analysis-modal').remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>📊 AI分析结果: ${analysisData.topic}</h3>
                    <button class="modal-close" onclick="this.closest('.analysis-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="analysis-overview">
                        <div class="platform-badge ${analysisData.platform}-badge">
                            <i class="fab fa-${analysisData.platform}"></i>
                            <span>${analysisData.platform.toUpperCase()}</span>
                        </div>
                        <div class="viral-score">
                            <div class="score-circle">
                                <span class="score-number">${analysisData.engagement_score}</span>
                                <span class="score-label">病毒指数</span>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-grid">
                        <div class="metric-card">
                            <div class="metric-icon">🚀</div>
                            <div class="metric-value">${analysisData.viral_potential}</div>
                            <div class="metric-label">病毒潜力</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">👥</div>
                            <div class="metric-value">${analysisData.target_audience}</div>
                            <div class="metric-label">目标受众</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-icon">📈</div>
                            <div class="metric-value">${analysisData.estimated_reach}</div>
                            <div class="metric-label">预估触达</div>
                        </div>
                    </div>

                    <div class="insights-section">
                        <h4><i class="fas fa-lightbulb"></i> 关键洞察</h4>
                        <ul class="insights-list">
                            ${analysisData.key_insights.map(insight => `<li>${insight}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="suggestions-section">
                        <h4><i class="fas fa-magic"></i> 内容建议</h4>
                        <div class="suggestions-grid">
                            ${analysisData.content_suggestions.map(suggestion => `
                                <div class="suggestion-card">
                                    <i class="fas fa-arrow-right"></i>
                                    <span>${suggestion}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="hashtags-section">
                        <h4><i class="fas fa-hashtag"></i> 推荐标签</h4>
                        <div class="hashtags-container">
                            ${analysisData.hashtags.map(tag => `
                                <span class="hashtag-pill">${tag}</span>
                            `).join('')}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.analysis-modal').remove()">
                        <i class="fas fa-times"></i>
                        关闭
                    </button>
                    <button class="btn-primary" onclick="window.trendcastApp.createContentFromAnalysis('${analysisData.topic}')">
                        <i class="fas fa-magic"></i>
                        开始创作
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        setTimeout(() => modal.classList.add('active'), 10);

        // Add enhanced modal styles
        this.addAnalysisModalStyles();
    }

    createContentFromAnalysis(topic) {
        // Close modal and navigate to create page with topic
        document.querySelector('.analysis-modal')?.remove();
        window.location.href = `/genz/create?topic=${encodeURIComponent(topic)}`;
    }

    setupNavigationScroll() {
        const nav = document.querySelector('.glass-nav');
        if (!nav) return;

        let lastScrollY = window.scrollY;
        let ticking = false;

        const updateNavigation = () => {
            const scrollY = window.scrollY;

            if (scrollY > 50) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }

            lastScrollY = scrollY;
            ticking = false;
        };

        const onScroll = () => {
            if (!ticking) {
                requestAnimationFrame(updateNavigation);
                ticking = true;
            }
        };

        window.addEventListener('scroll', onScroll, { passive: true });
    }

    showQuickCreateMenu() {
        // Show quick create options
        this.showNotification('⚡ Quick create menu coming soon!', 'info');
    }

    // Utility methods
    animateStatNumber(statItem) {
        const numberElement = statItem.querySelector('.stat-number');
        const currentText = numberElement.textContent;
        
        // Add glow effect
        numberElement.style.textShadow = '0 0 20px currentColor';
        setTimeout(() => {
            numberElement.style.textShadow = '';
        }, 500);
    }

    // Enhanced highlighting with sophisticated effects
    enhancedHighlightTrendingItem(item) {
        item.style.transform = 'translateY(-8px) scale(1.02)';
        item.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 0, 110, 0.2)';
        item.style.borderColor = 'rgba(255, 0, 110, 0.3)';

        // Add glow effect to title
        const title = item.querySelector('h3');
        if (title) {
            title.style.textShadow = '0 0 10px rgba(255, 0, 110, 0.5)';
        }

        // Animate badge
        const badge = item.querySelector('.trending-badge');
        if (badge) {
            badge.style.transform = 'scale(1.05)';
        }
    }

    enhancedUnhighlightTrendingItem(item) {
        item.style.transform = '';
        item.style.boxShadow = '';
        item.style.borderColor = '';

        // Remove glow effect
        const title = item.querySelector('h3');
        if (title) {
            title.style.textShadow = '';
        }

        // Reset badge
        const badge = item.querySelector('.trending-badge');
        if (badge) {
            badge.style.transform = '';
        }
    }

    // Ripple effect for interactions
    createRippleEffect(event, element) {
        const ripple = document.createElement('div');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: radial-gradient(circle, rgba(255, 0, 110, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 1;
        `;

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        // Add ripple animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Remove ripple after animation
        setTimeout(() => {
            ripple.remove();
            style.remove();
        }, 600);
    }

    previewTrendingItem(item) {
        const title = item.querySelector('h3').textContent;

        // Add micro-bounce animation
        item.classList.add('micro-bounce');
        setTimeout(() => {
            item.classList.remove('micro-bounce');
        }, 600);

        this.showNotification(`👀 Previewing "${title}"...`, 'info');
    }

    handleScroll() {
        const scrollY = window.scrollY;
        
        // Parallax effect for hero section
        const heroVisual = document.querySelector('.hero-visual');
        if (heroVisual) {
            heroVisual.style.transform = `translateY(${scrollY * 0.1}px)`;
        }
        
        // Update navigation background opacity
        const nav = document.querySelector('.glass-nav');
        if (nav) {
            const opacity = Math.min(scrollY / 100, 1);
            nav.style.background = `rgba(0, 0, 0, ${0.8 + opacity * 0.2})`;
        }
    }

    handleKeyboardShortcuts(e) {
        // Cmd/Ctrl + K for search
        if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
            e.preventDefault();
            this.openSearchModal();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.demo-modal');
            modals.forEach(modal => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            });
        }
    }

    openSearchModal() {
        this.showNotification('🔍 Search functionality coming soon!', 'info');
    }

    toggleProfileMenu() {
        this.showNotification('👤 Profile menu coming soon!', 'info');
    }

    loadTrendingData() {
        // Simulate loading trending data
        this.trendingData = [
            { platform: 'tiktok', title: 'AI Art Challenge', views: '45.2M' },
            { platform: 'instagram', title: 'Sustainable Fashion', views: '28.7M' },
            { platform: 'youtube', title: 'Mental Health Awareness', views: '67.3M' },
            { platform: 'spotify', title: 'Bedroom Pop Revival', streams: '156M' }
        ];
    }

    startInteractiveElements() {
        // Start any ongoing animations or interactive elements
        this.animateAudioVisualizer();
    }
}

// Enhanced demo functionality
function showDemo() {
    const modal = createDemoModal();
    document.body.appendChild(modal);

    // Animate modal in
    setTimeout(() => {
        modal.classList.add('modal-active');
    }, 10);
}

function createDemoModal() {
    const modal = document.createElement('div');
    modal.className = 'demo-modal';
    modal.innerHTML = `
        <div class="modal-backdrop" onclick="closeDemoModal(this)"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>🎬 TrendCast AI 演示</h3>
                <button class="modal-close" onclick="closeDemoModal(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="demo-video">
                    <div class="video-placeholder">
                        <i class="fas fa-play-circle"></i>
                        <p>AI驱动的内容创作演示</p>
                        <span>观看如何在60秒内创建病毒式播客</span>
                    </div>
                </div>
                <div class="demo-features">
                    <div class="feature-item">
                        <i class="fas fa-brain"></i>
                        <span>AI智能分析</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-rocket"></i>
                        <span>快速生成</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-fire"></i>
                        <span>病毒传播</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" onclick="window.location.href='/genz/create'">
                    <i class="fas fa-magic"></i>
                    立即开始创作
                </button>
            </div>
        </div>
    `;

    // Add modal styles
    addDemoModalStyles();

    return modal;
}

function closeDemoModal(element) {
    const modal = element.closest('.demo-modal') || element;
    if (modal.classList.contains('demo-modal')) {
        modal.classList.remove('modal-active');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }
}

function addDemoModalStyles() {
    if (document.getElementById('demo-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'demo-modal-styles';
    style.textContent = `
        .demo-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .demo-modal.modal-active {
            opacity: 1;
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            cursor: pointer;
        }

        .modal-content {
            position: relative;
            background: var(--bg-glass-darker);
            backdrop-filter: var(--glass-backdrop-strong);
            border: var(--glass-border-strong);
            border-radius: var(--radius-2xl);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: var(--shadow-depth-5);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .demo-modal.modal-active .modal-content {
            transform: scale(1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-lg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-header h3 {
            color: var(--text-primary);
            font-size: 1.3rem;
            font-weight: 700;
        }

        .modal-close {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--space-xs);
            border-radius: var(--radius-sm);
            transition: all var(--transition-fast);
        }

        .modal-close:hover {
            color: var(--neon-pink);
            background: var(--bg-glass);
        }

        .modal-body {
            padding: var(--space-lg);
        }

        .video-placeholder {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--space-3xl);
            text-align: center;
            margin-bottom: var(--space-lg);
            border: 2px dashed rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .video-placeholder:hover {
            border-color: var(--neon-pink-soft);
            background: var(--bg-tertiary);
        }

        .video-placeholder i {
            font-size: 3rem;
            color: var(--neon-pink);
            margin-bottom: var(--space-md);
            transition: transform var(--transition-fast);
        }

        .video-placeholder:hover i {
            transform: scale(1.1);
        }

        .video-placeholder p {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-sm);
        }

        .video-placeholder span {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .demo-features {
            display: flex;
            gap: var(--space-lg);
            justify-content: center;
        }

        .feature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-xs);
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 600;
        }

        .feature-item i {
            font-size: 1.5rem;
            color: var(--neon-blue);
        }

        .modal-footer {
            padding: var(--space-lg);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .btn-primary {
            background: var(--gradient-secondary);
            border: none;
            color: white;
            padding: var(--space-md) var(--space-xl);
            border-radius: var(--radius-full);
            font-weight: 700;
            cursor: pointer;
            transition: all var(--transition-normal);
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-depth-3);
        }
    `;
    document.head.appendChild(style);
}

// Add this method to TrendCastApp class
TrendCastApp.prototype.addAnalysisModalStyles = function() {
    // Add enhanced styles for analysis modal
    if (document.getElementById('analysis-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'analysis-modal-styles';
    style.textContent = `
        .analysis-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .analysis-modal.active {
            opacity: 1;
        }

        .analysis-modal .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            cursor: pointer;
        }

        .analysis-modal .modal-content {
            position: relative;
            background: var(--bg-glass-darker);
            backdrop-filter: var(--glass-backdrop-strong);
            border: var(--glass-border-strong);
            border-radius: var(--radius-2xl);
            max-width: 900px;
            width: 95%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-depth-5);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .analysis-modal.active .modal-content {
            transform: scale(1);
        }

        .analysis-overview {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-xl);
            padding: var(--space-lg);
            background: var(--bg-glass);
            border-radius: var(--radius-lg);
            border: var(--glass-border);
        }

        .viral-score {
            text-align: center;
        }

        .score-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, var(--neon-pink), var(--neon-blue), var(--neon-green), var(--neon-pink));
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .score-circle::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            right: 3px;
            bottom: 3px;
            background: var(--bg-secondary);
            border-radius: 50%;
        }

        .score-number {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--text-primary);
            position: relative;
            z-index: 1;
        }

        .score-label {
            font-size: 0.7rem;
            color: var(--text-secondary);
            position: relative;
            z-index: 1;
        }

        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-lg);
            margin: var(--space-lg) 0;
        }

        .metric-card {
            background: var(--bg-glass);
            border: var(--glass-border);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            text-align: center;
            transition: all var(--transition-normal);
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-depth-2);
            border-color: var(--neon-pink-soft);
        }

        .metric-icon {
            font-size: 2rem;
            margin-bottom: var(--space-sm);
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .insights-section, .suggestions-section, .hashtags-section {
            margin: var(--space-xl) 0;
        }

        .insights-section h4, .suggestions-section h4, .hashtags-section h4 {
            color: var(--text-primary);
            margin-bottom: var(--space-md);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-size: 1.1rem;
        }

        .insights-list {
            list-style: none;
            padding: 0;
        }

        .insights-list li {
            background: var(--bg-glass);
            border: var(--glass-border);
            border-radius: var(--radius-md);
            padding: var(--space-md);
            margin-bottom: var(--space-sm);
            color: var(--text-secondary);
            position: relative;
            padding-left: var(--space-xl);
        }

        .insights-list li::before {
            content: '💡';
            position: absolute;
            left: var(--space-md);
            top: var(--space-md);
        }

        .suggestions-grid {
            display: grid;
            gap: var(--space-sm);
        }

        .suggestion-card {
            background: var(--bg-glass);
            border: var(--glass-border);
            border-radius: var(--radius-md);
            padding: var(--space-md);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            color: var(--text-secondary);
            transition: all var(--transition-normal);
        }

        .suggestion-card:hover {
            border-color: var(--neon-blue-soft);
            transform: translateX(4px);
        }

        .suggestion-card i {
            color: var(--neon-blue);
            font-size: 0.9rem;
        }

        .hashtags-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-sm);
        }

        .hashtag-pill {
            background: var(--bg-glass);
            border: var(--glass-border);
            color: var(--neon-pink);
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-full);
            font-size: 0.85rem;
            font-weight: 600;
            transition: all var(--transition-normal);
        }

        .hashtag-pill:hover {
            background: var(--neon-pink-glow);
            color: white;
            transform: scale(1.05);
        }

        .modal-footer {
            display: flex;
            gap: var(--space-md);
            justify-content: flex-end;
            padding: var(--space-lg);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .btn-secondary {
            background: var(--bg-glass);
            border: var(--glass-border);
            color: var(--text-secondary);
            padding: var(--space-sm) var(--space-lg);
            border-radius: var(--radius-md);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
        }

        .btn-secondary:hover {
            color: var(--text-primary);
            border-color: var(--glass-border-strong);
            background: var(--bg-glass-strong);
        }

        @media (max-width: 768px) {
            .analysis-modal .modal-content {
                width: 98%;
                max-height: 95vh;
            }

            .analysis-overview {
                flex-direction: column;
                gap: var(--space-md);
                text-align: center;
            }

            .analysis-grid {
                grid-template-columns: 1fr;
            }

            .modal-footer {
                flex-direction: column;
            }

            .btn-primary, .btn-secondary {
                width: 100%;
                justify-content: center;
            }
        }
    `;
    document.head.appendChild(style);
};

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.trendcastApp = new TrendCastApp();

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        @keyframes modalSlideOut {
            from { transform: scale(1); opacity: 1; }
            to { transform: scale(0.8); opacity: 0; }
        }
        .animate-in {
            animation: slideUp 0.6s ease;
        }
    `;
    document.head.appendChild(style);
});
