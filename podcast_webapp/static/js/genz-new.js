// TrendCast AI - New Design JavaScript

class TrendCastApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupScrollEffects();
        this.setupAnimations();
        this.setupInteractions();
        this.setupNavigation();
    }

    setupScrollEffects() {
        // Navbar scroll effect
        const navbar = document.querySelector('.navbar');

        const handleScroll = this.throttle(() => {
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
                navbar.style.backdropFilter = 'blur(20px)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.boxShadow = 'none';
                navbar.style.backdropFilter = 'blur(10px)';
            }
        }, 10);

        window.addEventListener('scroll', handleScroll);

        // Enhanced parallax effect for hero images
        const imageCards = document.querySelectorAll('.image-card');

        const handleParallax = this.throttle(() => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.3;

            imageCards.forEach((card, index) => {
                const rect = card.getBoundingClientRect();
                const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

                if (isVisible) {
                    const offset = rate * (index + 1) * 0.05;
                    const rotation = scrolled * 0.01 * (index % 2 === 0 ? 1 : -1);
                    card.style.transform = `translateY(${offset}px) rotate(${rotation}deg)`;
                }
            });
        }, 16);

        window.addEventListener('scroll', handleParallax);

        // Reveal animations on scroll
        this.setupRevealAnimations();
    }

    setupRevealAnimations() {
        const revealElements = document.querySelectorAll('.feature-card, .stat-card, .gallery-item, .section-title, .section-subtitle');

        const revealObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                    revealObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        revealElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            revealObserver.observe(el);
        });
    }

    setupAnimations() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        const animatedElements = document.querySelectorAll('.feature-card, .stat-card, .gallery-item');
        animatedElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Stagger animation for feature cards
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach((card, index) => {
            card.style.transitionDelay = `${index * 0.1}s`;
        });

        // Counter animation for stats
        this.animateCounters();
    }

    animateCounters() {
        const statNumbers = document.querySelectorAll('.stat-card .stat-number');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.countUp(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        statNumbers.forEach(stat => observer.observe(stat));
    }

    countUp(element) {
        const target = element.textContent;
        const isNumber = /^\d+/.test(target);
        
        if (!isNumber) return;
        
        const number = parseInt(target.replace(/\D/g, ''));
        const suffix = target.replace(/[\d,]/g, '');
        const duration = 2000;
        const steps = 60;
        const increment = number / steps;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= number) {
                current = number;
                clearInterval(timer);
            }
            
            const formatted = Math.floor(current).toLocaleString();
            element.textContent = formatted + suffix;
        }, duration / steps);
    }

    setupInteractions() {
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Enhanced button interactions
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
        buttons.forEach(button => {
            // Mouse enter effect
            button.addEventListener('mouseenter', () => {
                button.style.transform = 'translateY(-3px)';
                if (button.classList.contains('btn-primary')) {
                    button.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';
                } else {
                    button.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.1)';
                }
            });

            // Mouse leave effect
            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translateY(0)';
                button.style.boxShadow = 'none';
            });

            // Click effect
            button.addEventListener('mousedown', () => {
                button.style.transform = 'translateY(-1px) scale(0.98)';
            });

            button.addEventListener('mouseup', () => {
                button.style.transform = 'translateY(-3px) scale(1)';
            });
        });

        // Enhanced gallery interactions
        const galleryItems = document.querySelectorAll('.gallery-item');
        galleryItems.forEach((item, index) => {
            item.addEventListener('mouseenter', () => {
                item.style.transform = 'scale(1.03) translateZ(0)';
                item.style.zIndex = '10';
                item.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.2)';

                // Add subtle rotation
                const rotation = (index % 2 === 0) ? '2deg' : '-2deg';
                item.style.transform += ` rotate(${rotation})`;
            });

            item.addEventListener('mouseleave', () => {
                item.style.transform = 'scale(1) translateZ(0) rotate(0deg)';
                item.style.zIndex = '1';
                item.style.boxShadow = 'none';
            });

            // Click effect for gallery items
            item.addEventListener('click', () => {
                this.showImageModal(item.querySelector('img').src);
            });
        });

        // Enhanced feature card interactions
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach((card, index) => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-15px)';
                card.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.15)';

                // Animate the icon
                const icon = card.querySelector('.feature-icon');
                if (icon) {
                    icon.style.transform = 'scale(1.1) rotate(5deg)';
                }
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';

                // Reset icon
                const icon = card.querySelector('.feature-icon');
                if (icon) {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                }
            });
        });

        // Image card hover effects
        const imageCards = document.querySelectorAll('.image-card');
        imageCards.forEach((card, index) => {
            card.addEventListener('mouseenter', () => {
                // Create a subtle floating effect
                card.style.animation = 'float 3s ease-in-out infinite';
                card.style.animationDelay = `${index * 0.2}s`;
            });

            card.addEventListener('mouseleave', () => {
                card.style.animation = 'none';
            });
        });

        // Add cursor effects
        this.setupCursorEffects();
    }

    setupCursorEffects() {
        // Create custom cursor for interactive elements
        const interactiveElements = document.querySelectorAll('.btn-primary, .btn-secondary, .gallery-item, .feature-card, .image-card');

        interactiveElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                document.body.style.cursor = 'pointer';
            });

            element.addEventListener('mouseleave', () => {
                document.body.style.cursor = 'default';
            });
        });
    }

    showImageModal(imageSrc) {
        // Simple image modal implementation
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        const img = document.createElement('img');
        img.src = imageSrc;
        img.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            border-radius: 16px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        `;

        modal.appendChild(img);
        document.body.appendChild(modal);

        // Fade in
        setTimeout(() => modal.style.opacity = '1', 10);

        // Close on click
        modal.addEventListener('click', () => {
            modal.style.opacity = '0';
            setTimeout(() => document.body.removeChild(modal), 300);
        });
    }

    setupNavigation() {
        // Mobile menu toggle (if needed)
        const navToggle = document.querySelector('.nav-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
            });
        }

        // Active navigation highlighting
        const navItems = document.querySelectorAll('.nav-item');
        const sections = document.querySelectorAll('section[id]');
        
        window.addEventListener('scroll', () => {
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });
            
            navItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('href') === `#${current}`) {
                    item.classList.add('active');
                }
            });
        });
    }

    // Utility methods
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TrendCastApp();
});

// Loading animation
window.addEventListener('load', () => {
    document.body.classList.add('loaded');
    
    // Trigger initial animations
    setTimeout(() => {
        const heroTitle = document.querySelector('.hero-title');
        const heroSubtitle = document.querySelector('.hero-subtitle');
        const heroActions = document.querySelector('.hero-actions');
        
        if (heroTitle) {
            heroTitle.style.opacity = '1';
            heroTitle.style.transform = 'translateY(0)';
        }
        
        if (heroSubtitle) {
            setTimeout(() => {
                heroSubtitle.style.opacity = '1';
                heroSubtitle.style.transform = 'translateY(0)';
            }, 200);
        }
        
        if (heroActions) {
            setTimeout(() => {
                heroActions.style.opacity = '1';
                heroActions.style.transform = 'translateY(0)';
            }, 400);
        }
    }, 100);
});

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TrendCastApp;
}
