// Enhanced AI Podcast Studio JavaScript
class EnhancedPodcastStudio {
    constructor() {
        this.currentStep = 1;
        this.maxSteps = 3;
        this.ttsProviders = [];
        this.selectedProvider = null;
        this.generatedScript = '';
        this.selectedVoices = {};
        this.isGenerating = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadTTSProviders();
        this.updateWorkflowProgress();
    }

    bindEvents() {
        // Step navigation
        document.getElementById('generateScriptBtn')?.addEventListener('click', () => this.generateScript());
        document.getElementById('backToConfigBtn')?.addEventListener('click', () => this.goToStep(1));
        document.getElementById('proceedToVoicesBtn')?.addEventListener('click', () => this.goToStep(3));
        document.getElementById('backToScriptBtn')?.addEventListener('click', () => this.goToStep(2));
        document.getElementById('generateAudioBtn')?.addEventListener('click', () => this.generateAudio());
        document.getElementById('createNewBtn')?.addEventListener('click', () => this.resetToStart());

        // Script enhancement
        document.getElementById('regenerateBtn')?.addEventListener('click', () => this.regenerateScript());
        document.getElementById('enhanceBtn')?.addEventListener('click', () => this.autoEnhanceScript());
        
        // Enhancement toggles
        document.getElementById('emotionToggle')?.addEventListener('change', (e) => this.toggleEnhancement('emotion', e.target.checked));
        document.getElementById('audioEventsToggle')?.addEventListener('change', (e) => this.toggleEnhancement('audioEvents', e.target.checked));

        // Download and share
        document.getElementById('downloadBtn')?.addEventListener('click', () => this.downloadAudio());
        document.getElementById('shareBtn')?.addEventListener('click', () => this.shareAudio());

        // Enhancement palette
        this.bindEnhancementPalette();
    }

    bindEnhancementPalette() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('emotion-tag') || e.target.classList.contains('audio-tag')) {
                this.insertEnhancementTag(e.target.dataset.tag, e.target.classList.contains('emotion-tag') ? 'emotion' : 'audio');
            }
        });
    }

    async loadTTSProviders() {
        try {
            const response = await fetch('/api/tts/providers');
            const data = await response.json();
            this.ttsProviders = data.providers || [];
            this.renderTTSProviders();
            this.updateTTSStatus();
        } catch (error) {
            console.error('Failed to load TTS providers:', error);
            this.showError('Failed to load TTS providers');
        }
    }

    renderTTSProviders() {
        const container = document.getElementById('ttsProviders');
        if (!container) return;

        container.innerHTML = this.ttsProviders.map(provider => `
            <div class="tts-provider-card ${provider.available ? 'available' : 'unavailable'}" 
                 data-provider="${provider.id}" 
                 onclick="podcastStudio.selectProvider('${provider.id}')">
                <div class="provider-header">
                    <div class="provider-name">${provider.name}</div>
                    <div class="provider-status ${provider.available ? 'available' : 'unavailable'}">
                        ${provider.available ? 'Available' : 'Unavailable'}
                    </div>
                </div>
                <div class="provider-description">${provider.description}</div>
                <div class="provider-features">
                    ${provider.features.map(feature => `
                        <span class="feature-badge ${feature.enhanced ? 'enhanced' : ''}">${feature.name}</span>
                    `).join('')}
                </div>
            </div>
        `).join('');

        // Auto-select first available provider
        const firstAvailable = this.ttsProviders.find(p => p.available);
        if (firstAvailable && !this.selectedProvider) {
            this.selectProvider(firstAvailable.id);
        }
    }

    selectProvider(providerId) {
        // Remove previous selection
        document.querySelectorAll('.tts-provider-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Select new provider
        const card = document.querySelector(`[data-provider="${providerId}"]`);
        if (card) {
            card.classList.add('selected');
            this.selectedProvider = providerId;
            this.updateFeaturesPreview();
            this.updateTTSStatus();
        }
    }

    updateFeaturesPreview() {
        const provider = this.ttsProviders.find(p => p.id === this.selectedProvider);
        if (!provider) return;

        const container = document.getElementById('featureTags');
        if (!container) return;

        const enhancedFeatures = provider.features.filter(f => f.enhanced);
        container.innerHTML = enhancedFeatures.map(feature => `
            <div class="feature-tag">
                <i class="${feature.icon || 'fas fa-star'}"></i>
                ${feature.name}
            </div>
        `).join('');
    }

    updateTTSStatus() {
        const indicator = document.getElementById('ttsStatusIndicator');
        const activeProviderEl = document.getElementById('activeProvider');
        const activeModelEl = document.getElementById('activeModel');
        const enhancedFeaturesEl = document.getElementById('enhancedFeatures');

        if (indicator) {
            const dot = indicator.querySelector('.status-dot');
            const availableCount = this.ttsProviders.filter(p => p.available).length;
            
            if (availableCount > 0) {
                dot.style.color = 'var(--success-color)';
                indicator.title = `${availableCount} TTS provider(s) available`;
            } else {
                dot.style.color = 'var(--error-color)';
                indicator.title = 'No TTS providers available';
            }
        }

        if (this.selectedProvider) {
            const provider = this.ttsProviders.find(p => p.id === this.selectedProvider);
            if (provider) {
                if (activeProviderEl) activeProviderEl.textContent = provider.name;
                if (activeModelEl) activeModelEl.textContent = provider.model || 'Standard';
                if (enhancedFeaturesEl) {
                    const enhancedCount = provider.features.filter(f => f.enhanced).length;
                    enhancedFeaturesEl.textContent = enhancedCount > 0 ? `${enhancedCount} Active` : 'None';
                }
            }
        }
    }

    goToStep(step) {
        if (step < 1 || step > this.maxSteps) return;
        
        // Hide all panels
        document.querySelectorAll('.step-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        // Show target panel
        const targetPanel = document.getElementById(`step${step}Panel`);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }

        this.currentStep = step;
        this.updateWorkflowProgress();
    }

    updateWorkflowProgress() {
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
            }
        });

        // Update connectors
        document.querySelectorAll('.progress-connector').forEach((connector, index) => {
            connector.classList.remove('completed');
            if (index + 1 < this.currentStep) {
                connector.classList.add('completed');
            }
        });
    }

    async generateScript() {
        const topic = document.getElementById('topic')?.value;
        const language = document.getElementById('language')?.value;
        const duration = document.getElementById('duration')?.value;
        const style = document.getElementById('style')?.value;
        const speakers = document.getElementById('speakers')?.value;

        if (!topic) {
            this.showError('Please enter a podcast topic');
            return;
        }

        if (!this.selectedProvider) {
            this.showError('Please select a TTS provider');
            return;
        }

        this.showLoading('Generating podcast script...', 'AI is researching your topic and creating engaging dialogue');

        try {
            const response = await fetch('/api/generate_script', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    topic,
                    language,
                    duration: parseInt(duration),
                    style,
                    speakers: parseInt(speakers),
                    provider: this.selectedProvider
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.generatedScript = data.script;
                this.renderScript();
                this.goToStep(2);
            } else {
                this.showError(data.error || 'Failed to generate script');
            }
        } catch (error) {
            console.error('Script generation error:', error);
            this.showError('Failed to generate script. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    renderScript() {
        const editor = document.getElementById('scriptEditor');
        if (!editor || !this.generatedScript) return;

        // Parse script and render with syntax highlighting
        const lines = this.generatedScript.split('\n');
        const renderedLines = lines.map((line, index) => {
            if (line.trim() === '') return '<div class="script-line empty-line">&nbsp;</div>';
            
            // Detect speaker labels
            const speakerMatch = line.match(/^([^:]+):\s*(.+)$/);
            if (speakerMatch) {
                const [, speaker, content] = speakerMatch;
                const enhancedContent = this.highlightEnhancements(content);
                return `
                    <div class="script-line" data-line="${index}">
                        <span class="speaker-label">${speaker}:</span>
                        <span class="line-content">${enhancedContent}</span>
                    </div>
                `;
            }
            
            return `<div class="script-line" data-line="${index}">${this.highlightEnhancements(line)}</div>`;
        }).join('');

        editor.innerHTML = renderedLines;
        this.bindScriptEditor();
    }

    highlightEnhancements(text) {
        // Highlight emotion tags [emotion]
        text = text.replace(/\[([^\]]+)\]/g, '<span class="emotion-tag-inline">[$1]</span>');
        
        // Highlight audio events {event}
        text = text.replace(/\{([^}]+)\}/g, '<span class="audio-tag-inline">{$1}</span>');
        
        // Highlight pause markers <#0.3#>
        text = text.replace(/<#([^#]+)#>/g, '<span class="pause-marker">&lt;#$1#&gt;</span>');
        
        return text;
    }

    bindScriptEditor() {
        document.querySelectorAll('.script-line').forEach(line => {
            line.addEventListener('click', () => {
                document.querySelectorAll('.script-line').forEach(l => l.classList.remove('selected'));
                line.classList.add('selected');
            });
        });
    }

    insertEnhancementTag(tag, type) {
        const selectedLine = document.querySelector('.script-line.selected');
        if (!selectedLine) {
            this.showError('Please select a line in the script first');
            return;
        }

        const lineIndex = parseInt(selectedLine.dataset.line);
        const lines = this.generatedScript.split('\n');
        
        if (type === 'emotion') {
            lines[lineIndex] = lines[lineIndex].replace(/^([^:]+:\s*)(.+)$/, `$1[${tag}] $2`);
        } else if (type === 'audio') {
            lines[lineIndex] = lines[lineIndex] + ` {${tag}}`;
        }

        this.generatedScript = lines.join('\n');
        this.renderScript();
    }

    toggleEnhancement(type, enabled) {
        const palette = document.getElementById('enhancementPalette');
        if (!palette) return;

        if (type === 'emotion') {
            const emotionSection = palette.querySelector('.palette-section:first-child');
            emotionSection.style.opacity = enabled ? '1' : '0.5';
            emotionSection.style.pointerEvents = enabled ? 'auto' : 'none';
        } else if (type === 'audioEvents') {
            const audioSection = palette.querySelector('.palette-section:last-child');
            audioSection.style.opacity = enabled ? '1' : '0.5';
            audioSection.style.pointerEvents = enabled ? 'auto' : 'none';
        }
    }

    async regenerateScript() {
        this.showLoading('Regenerating script...', 'Creating a new version with fresh content');
        
        // Simulate regeneration delay
        setTimeout(() => {
            this.hideLoading();
            this.showSuccess('Script regenerated successfully!');
        }, 2000);
    }

    autoEnhanceScript() {
        this.showLoading('Auto-enhancing script...', 'Adding emotion tags and audio events automatically');
        
        // Simulate auto-enhancement
        setTimeout(() => {
            // Add some sample enhancements
            const lines = this.generatedScript.split('\n');
            const enhanced = lines.map(line => {
                if (line.includes('excited') || line.includes('amazing')) {
                    return line.replace(/^([^:]+:\s*)(.+)$/, '$1[excited] $2');
                } else if (line.includes('serious') || line.includes('important')) {
                    return line.replace(/^([^:]+:\s*)(.+)$/, '$1[calm] $2');
                }
                return line;
            });
            
            this.generatedScript = enhanced.join('\n');
            this.renderScript();
            this.hideLoading();
            this.showSuccess('Script enhanced with emotion tags!');
        }, 1500);
    }

    showLoading(text, details = '') {
        const overlay = document.getElementById('loadingOverlay');
        const textEl = document.getElementById('loadingText');
        const detailsEl = document.getElementById('loadingDetails');
        
        if (overlay) {
            overlay.classList.add('active');
            if (textEl) textEl.textContent = text;
            if (detailsEl) detailsEl.textContent = details;
        }
        
        this.isGenerating = true;
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
        
        this.isGenerating = false;
    }

    showError(message) {
        // Simple error display - could be enhanced with a proper modal
        alert(`Error: ${message}`);
    }

    showSuccess(message) {
        // Simple success display - could be enhanced with a proper notification
        console.log(`Success: ${message}`);
    }

    async loadVoices() {
        if (!this.selectedProvider) return;

        try {
            const response = await fetch(`/api/tts/voices?provider=${this.selectedProvider}`);
            const data = await response.json();

            if (data.success) {
                this.renderVoiceSelection(data.voices);
            } else {
                this.showError('Failed to load voices');
            }
        } catch (error) {
            console.error('Failed to load voices:', error);
            this.showError('Failed to load voices');
        }
    }

    renderVoiceSelection(voices) {
        const container = document.getElementById('speakersGrid');
        if (!container) return;

        const speakerCount = parseInt(document.getElementById('speakers')?.value || 2);
        const speakers = [];

        for (let i = 1; i <= speakerCount; i++) {
            speakers.push({
                id: `speaker${i}`,
                name: `Speaker ${i}`,
                role: i === 1 ? 'Host' : 'Guest',
                avatar: String.fromCharCode(64 + i) // A, B, C...
            });
        }

        container.innerHTML = speakers.map(speaker => `
            <div class="speaker-card" data-speaker="${speaker.id}">
                <div class="speaker-header">
                    <div class="speaker-avatar">${speaker.avatar}</div>
                    <div class="speaker-info">
                        <h3>${speaker.name}</h3>
                        <div class="speaker-role">${speaker.role}</div>
                    </div>
                </div>
                <div class="voice-options" id="voiceOptions${speaker.id}">
                    ${voices.map(voice => `
                        <div class="voice-option" data-voice="${voice.id}" data-speaker="${speaker.id}"
                             onclick="podcastStudio.selectVoice('${speaker.id}', '${voice.id}')">
                            <div class="voice-name">${voice.name}</div>
                            <div class="voice-description">${voice.description}</div>
                            <button class="voice-preview" onclick="event.stopPropagation(); podcastStudio.previewVoice('${voice.id}')">
                                <i class="fas fa-play"></i> Preview
                            </button>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');

        // Auto-select first voice for each speaker
        speakers.forEach((speaker, index) => {
            if (voices[index]) {
                this.selectVoice(speaker.id, voices[index].id);
            }
        });
    }

    selectVoice(speakerId, voiceId) {
        // Remove previous selection for this speaker
        document.querySelectorAll(`[data-speaker="${speakerId}"] .voice-option`).forEach(option => {
            option.classList.remove('selected');
        });

        // Select new voice
        const option = document.querySelector(`[data-speaker="${speakerId}"][data-voice="${voiceId}"]`);
        if (option) {
            option.classList.add('selected');
            this.selectedVoices[speakerId] = voiceId;
        }
    }

    async previewVoice(voiceId) {
        try {
            const response = await fetch('/api/tts/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    voice_id: voiceId,
                    provider: this.selectedProvider,
                    text: "Hello, this is a preview of my voice. How do I sound?"
                })
            });

            const data = await response.json();

            if (data.success && data.audio_url) {
                const audio = new Audio(data.audio_url);
                audio.play();
            } else {
                this.showError('Failed to preview voice');
            }
        } catch (error) {
            console.error('Voice preview error:', error);
            this.showError('Failed to preview voice');
        }
    }

    async generateAudio() {
        if (!this.generatedScript) {
            this.showError('No script available. Please generate a script first.');
            return;
        }

        if (Object.keys(this.selectedVoices).length === 0) {
            this.showError('Please select voices for all speakers');
            return;
        }

        this.showLoading('Generating audio...', 'Converting script to speech with enhanced TTS features');

        try {
            const response = await fetch('/api/generate_audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    script: this.generatedScript,
                    voices: this.selectedVoices,
                    provider: this.selectedProvider,
                    enhanced_features: {
                        emotion_tags: document.getElementById('emotionToggle')?.checked || false,
                        audio_events: document.getElementById('audioEventsToggle')?.checked || false
                    }
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
                this.goToStep(4); // Results panel
            } else {
                this.showError(data.error || 'Failed to generate audio');
            }
        } catch (error) {
            console.error('Audio generation error:', error);
            this.showError('Failed to generate audio. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    displayResults(data) {
        // Set audio source
        const audioPlayer = document.getElementById('finalAudio');
        if (audioPlayer && data.audio_url) {
            audioPlayer.src = data.audio_url;
        }

        // Display generation summary
        const summaryContainer = document.getElementById('generationSummary');
        if (summaryContainer && data.summary) {
            summaryContainer.innerHTML = `
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-value">${data.summary.duration || '0:00'}</div>
                        <div class="summary-label">Duration</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">${data.summary.words || 0}</div>
                        <div class="summary-label">Words</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">${data.summary.speakers || 0}</div>
                        <div class="summary-label">Speakers</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">${data.summary.provider || 'Unknown'}</div>
                        <div class="summary-label">TTS Provider</div>
                    </div>
                </div>
                <div class="generation-details">
                    <h4><i class="fas fa-info-circle"></i> Generation Details</h4>
                    <div class="detail-item">
                        <span class="detail-label">Model Used:</span>
                        <span class="detail-value">${data.summary.model || 'Standard'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Enhanced Features:</span>
                        <span class="detail-value">${data.summary.enhanced_features || 'None'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Processing Time:</span>
                        <span class="detail-value">${data.summary.processing_time || 'Unknown'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">File Size:</span>
                        <span class="detail-value">${data.summary.file_size || 'Unknown'}</span>
                    </div>
                </div>
            `;
        }
    }

    downloadAudio() {
        const audioPlayer = document.getElementById('finalAudio');
        if (audioPlayer && audioPlayer.src) {
            const link = document.createElement('a');
            link.href = audioPlayer.src;
            link.download = 'podcast.mp3';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            this.showError('No audio available to download');
        }
    }

    shareAudio() {
        const audioPlayer = document.getElementById('finalAudio');
        if (audioPlayer && audioPlayer.src) {
            if (navigator.share) {
                navigator.share({
                    title: 'AI Generated Podcast',
                    text: 'Check out this AI-generated podcast!',
                    url: audioPlayer.src
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(audioPlayer.src).then(() => {
                    this.showSuccess('Audio URL copied to clipboard!');
                });
            }
        } else {
            this.showError('No audio available to share');
        }
    }

    resetToStart() {
        this.currentStep = 1;
        this.selectedProvider = null;
        this.generatedScript = '';
        this.selectedVoices = {};

        // Reset form
        const topicInput = document.getElementById('topic');
        if (topicInput) topicInput.value = '';

        // Clear selections
        document.querySelectorAll('.tts-provider-card').forEach(card => {
            card.classList.remove('selected');
        });

        this.goToStep(1);
        this.loadTTSProviders();
    }

    // Override goToStep to handle voice loading
    goToStep(step) {
        if (step < 1 || step > this.maxSteps + 1) return; // +1 for results panel

        // Hide all panels
        document.querySelectorAll('.step-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        // Show target panel
        let targetPanel;
        if (step <= this.maxSteps) {
            targetPanel = document.getElementById(`step${step}Panel`);
        } else {
            targetPanel = document.getElementById('resultsPanel');
        }

        if (targetPanel) {
            targetPanel.classList.add('active');
        }

        // Load voices when entering step 3
        if (step === 3) {
            this.loadVoices();
        }

        this.currentStep = step;
        this.updateWorkflowProgress();
    }
}

// Initialize the application
let podcastStudio;
document.addEventListener('DOMContentLoaded', () => {
    podcastStudio = new EnhancedPodcastStudio();
});
