// Enhanced AI Podcast Studio JavaScript
class EnhancedPodcastStudio {
    constructor() {
        this.currentStep = 1;
        this.maxSteps = 3;
        this.ttsProviders = [];
        this.selectedProvider = null;
        this.generatedScript = '';
        this.selectedVoices = {};
        this.isGenerating = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadTTSProviders();
        this.updateWorkflowProgress();
    }

    bindEvents() {
        // Step navigation
        document.getElementById('generateScriptBtn')?.addEventListener('click', () => this.generateScript());
        document.getElementById('backToConfigBtn')?.addEventListener('click', () => this.goToStep(1));
        document.getElementById('proceedToVoicesBtn')?.addEventListener('click', () => this.goToStep(3));
        document.getElementById('backToScriptBtn')?.addEventListener('click', () => this.goToStep(2));
        document.getElementById('generateAudioBtn')?.addEventListener('click', () => this.generateAudio());
        document.getElementById('createNewBtn')?.addEventListener('click', () => this.resetToStart());

        // Advanced settings
        document.getElementById('advancedToggle')?.addEventListener('click', () => this.toggleAdvancedSettings());
        document.getElementById('enableMusic')?.addEventListener('change', (e) => this.toggleBackgroundMusic(e.target.checked));

        // Template library
        document.getElementById('loadTemplateBtn')?.addEventListener('click', () => this.showTemplateSelector());
        document.getElementById('saveConfigBtn')?.addEventListener('click', () => this.saveConfiguration());

        // Script enhancement
        document.getElementById('regenerateBtn')?.addEventListener('click', () => this.regenerateScript());
        document.getElementById('enhanceBtn')?.addEventListener('click', () => this.autoEnhanceScript());

        // Enhancement toggles
        document.getElementById('emotionToggle')?.addEventListener('change', (e) => this.toggleEnhancement('emotion', e.target.checked));
        document.getElementById('audioEventsToggle')?.addEventListener('change', (e) => this.toggleEnhancement('audioEvents', e.target.checked));

        // Results panel actions
        document.getElementById('editScriptBtn')?.addEventListener('click', () => this.goToStep(2));
        document.getElementById('regenerateAudioBtn')?.addEventListener('click', () => this.regenerateAudio());

        // Audio player controls
        document.getElementById('loopBtn')?.addEventListener('click', () => this.toggleLoop());
        document.getElementById('speedBtn')?.addEventListener('click', () => this.cyclePlaybackSpeed());

        // Collaboration
        document.getElementById('inviteBtn')?.addEventListener('click', () => this.showInviteDialog());

        // Export and sharing
        this.bindExportButtons();
        this.bindSharingButtons();

        // Template selection
        this.bindTemplateCards();

        // Enhancement palette
        this.bindEnhancementPalette();
    }

    bindEnhancementPalette() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('emotion-tag') || e.target.classList.contains('audio-tag')) {
                this.insertEnhancementTag(e.target.dataset.tag, e.target.classList.contains('emotion-tag') ? 'emotion' : 'audio');
            }
        });
    }

    async loadTTSProviders() {
        try {
            const response = await fetch('/api/tts/providers');
            const data = await response.json();
            this.ttsProviders = data.providers || [];
            this.renderTTSProviders();
            this.updateTTSStatus();
        } catch (error) {
            console.error('Failed to load TTS providers:', error);
            this.showError('Failed to load TTS providers');
        }
    }

    renderTTSProviders() {
        const container = document.getElementById('ttsProviders');
        if (!container) return;

        container.innerHTML = this.ttsProviders.map(provider => `
            <div class="tts-provider-card ${provider.available ? 'available' : 'unavailable'}" 
                 data-provider="${provider.id}" 
                 onclick="podcastStudio.selectProvider('${provider.id}')">
                <div class="provider-header">
                    <div class="provider-name">${provider.name}</div>
                    <div class="provider-status ${provider.available ? 'available' : 'unavailable'}">
                        ${provider.available ? 'Available' : 'Unavailable'}
                    </div>
                </div>
                <div class="provider-description">${provider.description}</div>
                <div class="provider-features">
                    ${provider.features.map(feature => `
                        <span class="feature-badge ${feature.enhanced ? 'enhanced' : ''}">${feature.name}</span>
                    `).join('')}
                </div>
            </div>
        `).join('');

        // Auto-select first available provider
        const firstAvailable = this.ttsProviders.find(p => p.available);
        if (firstAvailable && !this.selectedProvider) {
            this.selectProvider(firstAvailable.id);
        }
    }

    selectProvider(providerId) {
        // Remove previous selection
        document.querySelectorAll('.tts-provider-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Select new provider
        const card = document.querySelector(`[data-provider="${providerId}"]`);
        if (card) {
            card.classList.add('selected');
            this.selectedProvider = providerId;
            this.updateFeaturesPreview();
            this.updateTTSStatus();
        }
    }

    updateFeaturesPreview() {
        const provider = this.ttsProviders.find(p => p.id === this.selectedProvider);
        if (!provider) return;

        const container = document.getElementById('featureTags');
        if (!container) return;

        const enhancedFeatures = provider.features.filter(f => f.enhanced);
        container.innerHTML = enhancedFeatures.map(feature => `
            <div class="feature-tag">
                <i class="${feature.icon || 'fas fa-star'}"></i>
                ${feature.name}
            </div>
        `).join('');
    }

    updateTTSStatus() {
        const indicator = document.getElementById('ttsStatusIndicator');
        const activeProviderEl = document.getElementById('activeProvider');
        const activeModelEl = document.getElementById('activeModel');
        const enhancedFeaturesEl = document.getElementById('enhancedFeatures');

        if (indicator) {
            const dot = indicator.querySelector('.status-dot');
            const availableCount = this.ttsProviders.filter(p => p.available).length;
            
            if (availableCount > 0) {
                dot.style.color = 'var(--success-color)';
                indicator.title = `${availableCount} TTS provider(s) available`;
            } else {
                dot.style.color = 'var(--error-color)';
                indicator.title = 'No TTS providers available';
            }
        }

        if (this.selectedProvider) {
            const provider = this.ttsProviders.find(p => p.id === this.selectedProvider);
            if (provider) {
                if (activeProviderEl) activeProviderEl.textContent = provider.name;
                if (activeModelEl) activeModelEl.textContent = provider.model || 'Standard';
                if (enhancedFeaturesEl) {
                    const enhancedCount = provider.features.filter(f => f.enhanced).length;
                    enhancedFeaturesEl.textContent = enhancedCount > 0 ? `${enhancedCount} Active` : 'None';
                }
            }
        }
    }

    goToStep(step) {
        if (step < 1 || step > this.maxSteps) return;
        
        // Hide all panels
        document.querySelectorAll('.step-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        // Show target panel
        const targetPanel = document.getElementById(`step${step}Panel`);
        if (targetPanel) {
            targetPanel.classList.add('active');
        }

        this.currentStep = step;
        this.updateWorkflowProgress();
    }

    updateWorkflowProgress() {
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
            }
        });

        // Update connectors
        document.querySelectorAll('.progress-connector').forEach((connector, index) => {
            connector.classList.remove('completed');
            if (index + 1 < this.currentStep) {
                connector.classList.add('completed');
            }
        });
    }

    async generateScript() {
        const topic = document.getElementById('topic')?.value;
        const language = document.getElementById('language')?.value;
        const duration = document.getElementById('duration')?.value;
        const style = document.getElementById('style')?.value;
        const speakers = document.getElementById('speakers')?.value;

        if (!topic) {
            this.showError('Please enter a podcast topic');
            return;
        }

        if (!this.selectedProvider) {
            this.showError('Please select a TTS provider');
            return;
        }

        this.showLoading('Generating podcast script...', 'AI is researching your topic and creating engaging dialogue');

        try {
            const response = await fetch('/api/generate_script', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    topic,
                    language,
                    duration: parseInt(duration),
                    style,
                    speakers: parseInt(speakers),
                    provider: this.selectedProvider
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.generatedScript = data.script;
                this.renderScript();
                this.goToStep(2);
            } else {
                this.showError(data.error || 'Failed to generate script');
            }
        } catch (error) {
            console.error('Script generation error:', error);
            this.showError('Failed to generate script. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    renderScript() {
        const editor = document.getElementById('scriptEditor');
        if (!editor || !this.generatedScript) return;

        // Parse script and render with syntax highlighting
        const lines = this.generatedScript.split('\n');
        const renderedLines = lines.map((line, index) => {
            if (line.trim() === '') return '<div class="script-line empty-line">&nbsp;</div>';
            
            // Detect speaker labels
            const speakerMatch = line.match(/^([^:]+):\s*(.+)$/);
            if (speakerMatch) {
                const [, speaker, content] = speakerMatch;
                const enhancedContent = this.highlightEnhancements(content);
                return `
                    <div class="script-line" data-line="${index}">
                        <span class="speaker-label">${speaker}:</span>
                        <span class="line-content">${enhancedContent}</span>
                    </div>
                `;
            }
            
            return `<div class="script-line" data-line="${index}">${this.highlightEnhancements(line)}</div>`;
        }).join('');

        editor.innerHTML = renderedLines;
        this.bindScriptEditor();
    }

    highlightEnhancements(text) {
        // Highlight emotion tags [emotion]
        text = text.replace(/\[([^\]]+)\]/g, '<span class="emotion-tag-inline">[$1]</span>');
        
        // Highlight audio events {event}
        text = text.replace(/\{([^}]+)\}/g, '<span class="audio-tag-inline">{$1}</span>');
        
        // Highlight pause markers <#0.3#>
        text = text.replace(/<#([^#]+)#>/g, '<span class="pause-marker">&lt;#$1#&gt;</span>');
        
        return text;
    }

    bindScriptEditor() {
        document.querySelectorAll('.script-line').forEach(line => {
            line.addEventListener('click', () => {
                document.querySelectorAll('.script-line').forEach(l => l.classList.remove('selected'));
                line.classList.add('selected');
            });
        });
    }

    insertEnhancementTag(tag, type) {
        const selectedLine = document.querySelector('.script-line.selected');
        if (!selectedLine) {
            this.showError('Please select a line in the script first');
            return;
        }

        const lineIndex = parseInt(selectedLine.dataset.line);
        const lines = this.generatedScript.split('\n');
        
        if (type === 'emotion') {
            lines[lineIndex] = lines[lineIndex].replace(/^([^:]+:\s*)(.+)$/, `$1[${tag}] $2`);
        } else if (type === 'audio') {
            lines[lineIndex] = lines[lineIndex] + ` {${tag}}`;
        }

        this.generatedScript = lines.join('\n');
        this.renderScript();
    }

    toggleEnhancement(type, enabled) {
        const palette = document.getElementById('enhancementPalette');
        if (!palette) return;

        if (type === 'emotion') {
            const emotionSection = palette.querySelector('.palette-section:first-child');
            emotionSection.style.opacity = enabled ? '1' : '0.5';
            emotionSection.style.pointerEvents = enabled ? 'auto' : 'none';
        } else if (type === 'audioEvents') {
            const audioSection = palette.querySelector('.palette-section:last-child');
            audioSection.style.opacity = enabled ? '1' : '0.5';
            audioSection.style.pointerEvents = enabled ? 'auto' : 'none';
        }
    }

    async regenerateScript() {
        this.showLoading('Regenerating script...', 'Creating a new version with fresh content');
        
        // Simulate regeneration delay
        setTimeout(() => {
            this.hideLoading();
            this.showSuccess('Script regenerated successfully!');
        }, 2000);
    }

    autoEnhanceScript() {
        this.showLoading('Auto-enhancing script...', 'Adding emotion tags and audio events automatically');
        
        // Simulate auto-enhancement
        setTimeout(() => {
            // Add some sample enhancements
            const lines = this.generatedScript.split('\n');
            const enhanced = lines.map(line => {
                if (line.includes('excited') || line.includes('amazing')) {
                    return line.replace(/^([^:]+:\s*)(.+)$/, '$1[excited] $2');
                } else if (line.includes('serious') || line.includes('important')) {
                    return line.replace(/^([^:]+:\s*)(.+)$/, '$1[calm] $2');
                }
                return line;
            });
            
            this.generatedScript = enhanced.join('\n');
            this.renderScript();
            this.hideLoading();
            this.showSuccess('Script enhanced with emotion tags!');
        }, 1500);
    }

    showLoading(text, details = '') {
        const overlay = document.getElementById('loadingOverlay');
        const textEl = document.getElementById('loadingText');
        const detailsEl = document.getElementById('loadingDetails');
        
        if (overlay) {
            overlay.classList.add('active');
            if (textEl) textEl.textContent = text;
            if (detailsEl) detailsEl.textContent = details;
        }
        
        this.isGenerating = true;
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
        
        this.isGenerating = false;
    }

    showError(message) {
        // Simple error display - could be enhanced with a proper modal
        alert(`Error: ${message}`);
    }

    showSuccess(message) {
        // Simple success display - could be enhanced with a proper notification
        console.log(`Success: ${message}`);
    }

    async loadVoices() {
        if (!this.selectedProvider) return;

        try {
            const response = await fetch(`/api/tts/voices?provider=${this.selectedProvider}`);
            const data = await response.json();

            if (data.success) {
                this.renderVoiceSelection(data.voices);
            } else {
                this.showError('Failed to load voices');
            }
        } catch (error) {
            console.error('Failed to load voices:', error);
            this.showError('Failed to load voices');
        }
    }

    renderVoiceSelection(voices) {
        const container = document.getElementById('speakersGrid');
        if (!container) return;

        const speakerCount = parseInt(document.getElementById('speakers')?.value || 2);
        const speakers = [];

        for (let i = 1; i <= speakerCount; i++) {
            speakers.push({
                id: `speaker${i}`,
                name: `Speaker ${i}`,
                role: i === 1 ? 'Host' : 'Guest',
                avatar: String.fromCharCode(64 + i) // A, B, C...
            });
        }

        container.innerHTML = speakers.map(speaker => `
            <div class="speaker-card" data-speaker="${speaker.id}">
                <div class="speaker-header">
                    <div class="speaker-avatar">${speaker.avatar}</div>
                    <div class="speaker-info">
                        <h3>${speaker.name}</h3>
                        <div class="speaker-role">${speaker.role}</div>
                    </div>
                </div>
                <div class="voice-options" id="voiceOptions${speaker.id}">
                    ${voices.map(voice => `
                        <div class="voice-option" data-voice="${voice.id}" data-speaker="${speaker.id}"
                             onclick="podcastStudio.selectVoice('${speaker.id}', '${voice.id}')">
                            <div class="voice-name">${voice.name}</div>
                            <div class="voice-description">${voice.description}</div>
                            <button class="voice-preview" onclick="event.stopPropagation(); podcastStudio.previewVoice('${voice.id}')">
                                <i class="fas fa-play"></i> Preview
                            </button>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');

        // Auto-select first voice for each speaker
        speakers.forEach((speaker, index) => {
            if (voices[index]) {
                this.selectVoice(speaker.id, voices[index].id);
            }
        });
    }

    selectVoice(speakerId, voiceId) {
        // Remove previous selection for this speaker
        document.querySelectorAll(`[data-speaker="${speakerId}"] .voice-option`).forEach(option => {
            option.classList.remove('selected');
        });

        // Select new voice
        const option = document.querySelector(`[data-speaker="${speakerId}"][data-voice="${voiceId}"]`);
        if (option) {
            option.classList.add('selected');
            this.selectedVoices[speakerId] = voiceId;
        }
    }

    async previewVoice(voiceId) {
        try {
            const response = await fetch('/api/tts/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    voice_id: voiceId,
                    provider: this.selectedProvider,
                    text: "Hello, this is a preview of my voice. How do I sound?"
                })
            });

            const data = await response.json();

            if (data.success && data.audio_url) {
                const audio = new Audio(data.audio_url);
                audio.play();
            } else {
                this.showError('Failed to preview voice');
            }
        } catch (error) {
            console.error('Voice preview error:', error);
            this.showError('Failed to preview voice');
        }
    }

    async generateAudio() {
        if (!this.generatedScript) {
            this.showError('No script available. Please generate a script first.');
            return;
        }

        if (Object.keys(this.selectedVoices).length === 0) {
            this.showError('Please select voices for all speakers');
            return;
        }

        this.showLoading('Generating audio...', 'Converting script to speech with enhanced TTS features');

        try {
            const response = await fetch('/api/generate_audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    script: this.generatedScript,
                    voices: this.selectedVoices,
                    provider: this.selectedProvider,
                    enhanced_features: {
                        emotion_tags: document.getElementById('emotionToggle')?.checked || false,
                        audio_events: document.getElementById('audioEventsToggle')?.checked || false
                    }
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
                this.goToStep(4); // Results panel
            } else {
                this.showError(data.error || 'Failed to generate audio');
            }
        } catch (error) {
            console.error('Audio generation error:', error);
            this.showError('Failed to generate audio. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    displayResults(data) {
        // Set audio source
        const audioPlayer = document.getElementById('finalAudio');
        if (audioPlayer && data.audio_url) {
            audioPlayer.src = data.audio_url;
        }

        // Display generation summary
        const summaryContainer = document.getElementById('generationSummary');
        if (summaryContainer && data.summary) {
            summaryContainer.innerHTML = `
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-value">${data.summary.duration || '0:00'}</div>
                        <div class="summary-label">Duration</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">${data.summary.words || 0}</div>
                        <div class="summary-label">Words</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">${data.summary.speakers || 0}</div>
                        <div class="summary-label">Speakers</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">${data.summary.provider || 'Unknown'}</div>
                        <div class="summary-label">TTS Provider</div>
                    </div>
                </div>
                <div class="generation-details">
                    <h4><i class="fas fa-info-circle"></i> Generation Details</h4>
                    <div class="detail-item">
                        <span class="detail-label">Model Used:</span>
                        <span class="detail-value">${data.summary.model || 'Standard'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Enhanced Features:</span>
                        <span class="detail-value">${data.summary.enhanced_features || 'None'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Processing Time:</span>
                        <span class="detail-value">${data.summary.processing_time || 'Unknown'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">File Size:</span>
                        <span class="detail-value">${data.summary.file_size || 'Unknown'}</span>
                    </div>
                </div>
            `;
        }
    }

    downloadAudio() {
        const audioPlayer = document.getElementById('finalAudio');
        if (audioPlayer && audioPlayer.src) {
            const link = document.createElement('a');
            link.href = audioPlayer.src;
            link.download = 'podcast.mp3';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            this.showError('No audio available to download');
        }
    }

    shareAudio() {
        const audioPlayer = document.getElementById('finalAudio');
        if (audioPlayer && audioPlayer.src) {
            if (navigator.share) {
                navigator.share({
                    title: 'AI Generated Podcast',
                    text: 'Check out this AI-generated podcast!',
                    url: audioPlayer.src
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(audioPlayer.src).then(() => {
                    this.showSuccess('Audio URL copied to clipboard!');
                });
            }
        } else {
            this.showError('No audio available to share');
        }
    }

    resetToStart() {
        this.currentStep = 1;
        this.selectedProvider = null;
        this.generatedScript = '';
        this.selectedVoices = {};

        // Reset form
        const topicInput = document.getElementById('topic');
        if (topicInput) topicInput.value = '';

        // Clear selections
        document.querySelectorAll('.tts-provider-card').forEach(card => {
            card.classList.remove('selected');
        });

        this.goToStep(1);
        this.loadTTSProviders();
    }

    // Advanced Settings Management
    toggleAdvancedSettings() {
        const toggle = document.getElementById('advancedToggle');
        const settings = document.getElementById('advancedSettings');
        const icon = toggle?.querySelector('.toggle-icon');

        if (settings && toggle) {
            const isExpanded = settings.classList.contains('expanded');

            if (isExpanded) {
                settings.classList.remove('expanded');
                toggle.classList.remove('expanded');
                settings.style.display = 'none';
            } else {
                settings.classList.add('expanded');
                toggle.classList.add('expanded');
                settings.style.display = 'block';
            }
        }
    }

    toggleBackgroundMusic(enabled) {
        const musicSelect = document.getElementById('musicStyle');
        if (musicSelect) {
            musicSelect.disabled = !enabled;
            if (enabled) {
                musicSelect.style.opacity = '1';
            } else {
                musicSelect.style.opacity = '0.5';
            }
        }
    }

    // Template Management
    bindTemplateCards() {
        document.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', () => {
                // Remove previous selection
                document.querySelectorAll('.template-card').forEach(c => c.classList.remove('selected'));

                // Select current template
                card.classList.add('selected');

                // Load template data
                const templateId = card.dataset.template;
                this.loadTemplate(templateId);
            });
        });
    }

    async loadTemplate(templateId) {
        const templates = {
            'tech-interview': {
                style: 'interview',
                speakers: 2,
                duration: 600,
                targetAudience: 'professional',
                complexity: 'advanced',
                mood: 'serious',
                pacing: 'normal'
            },
            'educational': {
                style: 'educational',
                speakers: 1,
                duration: 900,
                targetAudience: 'general',
                complexity: 'moderate',
                mood: 'neutral',
                pacing: 'normal'
            },
            'news-report': {
                style: 'news',
                speakers: 2,
                duration: 300,
                targetAudience: 'general',
                complexity: 'simple',
                mood: 'serious',
                pacing: 'fast'
            },
            'storytelling': {
                style: 'storytelling',
                speakers: 3,
                duration: 1200,
                targetAudience: 'general',
                complexity: 'moderate',
                mood: 'dramatic',
                pacing: 'variable'
            }
        };

        const template = templates[templateId];
        if (template) {
            // Apply template settings
            Object.keys(template).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = template[key];
                }
            });

            this.showSuccess(`Template "${templateId}" loaded successfully!`);
        }
    }

    showTemplateSelector() {
        const selectedTemplate = document.querySelector('.template-card.selected');
        if (selectedTemplate) {
            const templateId = selectedTemplate.dataset.template;
            this.loadTemplate(templateId);
        } else {
            this.showError('Please select a template first');
        }
    }

    async saveConfiguration() {
        const config = {
            topic: document.getElementById('topic')?.value,
            language: document.getElementById('language')?.value,
            duration: document.getElementById('duration')?.value,
            style: document.getElementById('style')?.value,
            speakers: document.getElementById('speakers')?.value,
            targetAudience: document.getElementById('targetAudience')?.value,
            complexity: document.getElementById('complexity')?.value,
            mood: document.getElementById('mood')?.value,
            pacing: document.getElementById('pacing')?.value,
            enableMusic: document.getElementById('enableMusic')?.checked,
            musicStyle: document.getElementById('musicStyle')?.value,
            customInstructions: document.getElementById('customInstructions')?.value,
            provider: this.selectedProvider
        };

        try {
            const response = await fetch('/api/save_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            });

            const data = await response.json();
            if (data.success) {
                this.showSuccess('Configuration saved successfully!');
            } else {
                this.showError('Failed to save configuration');
            }
        } catch (error) {
            console.error('Save config error:', error);
            this.showError('Failed to save configuration');
        }
    }

    // Audio Player Enhancements
    toggleLoop() {
        const audio = document.getElementById('finalAudio');
        const loopBtn = document.getElementById('loopBtn');

        if (audio && loopBtn) {
            audio.loop = !audio.loop;
            loopBtn.classList.toggle('active', audio.loop);
        }
    }

    cyclePlaybackSpeed() {
        const audio = document.getElementById('finalAudio');
        const speedBtn = document.getElementById('speedBtn');
        const speedIndicator = speedBtn?.querySelector('.speed-indicator');

        if (audio && speedIndicator) {
            const speeds = [0.5, 0.75, 1, 1.25, 1.5, 2];
            const currentSpeed = audio.playbackRate;
            const currentIndex = speeds.indexOf(currentSpeed);
            const nextIndex = (currentIndex + 1) % speeds.length;

            audio.playbackRate = speeds[nextIndex];
            speedIndicator.textContent = `${speeds[nextIndex]}x`;
        }
    }

    // Export Functions
    bindExportButtons() {
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('.export-card');
                const format = card?.dataset.format;
                if (format) {
                    this.exportContent(format);
                }
            });
        });
    }

    async exportContent(format) {
        const audioPlayer = document.getElementById('finalAudio');

        try {
            switch (format) {
                case 'mp3':
                    if (audioPlayer?.src) {
                        this.downloadFile(audioPlayer.src, 'podcast.mp3');
                    }
                    break;

                case 'wav':
                    // Convert to WAV format
                    const response = await fetch('/api/convert_audio', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            audio_url: audioPlayer?.src,
                            format: 'wav'
                        })
                    });
                    const data = await response.json();
                    if (data.success) {
                        this.downloadFile(data.download_url, 'podcast.wav');
                    }
                    break;

                case 'script':
                    this.downloadTextFile(this.generatedScript, 'podcast_script.txt');
                    break;

                case 'metadata':
                    const metadata = this.generateMetadata();
                    this.downloadTextFile(JSON.stringify(metadata, null, 2), 'podcast_metadata.json');
                    break;
            }
        } catch (error) {
            console.error('Export error:', error);
            this.showError(`Failed to export ${format.toUpperCase()}`);
        }
    }

    downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    downloadTextFile(content, filename) {
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        this.downloadFile(url, filename);
        URL.revokeObjectURL(url);
    }

    generateMetadata() {
        return {
            title: document.getElementById('topic')?.value || 'AI Generated Podcast',
            duration: document.getElementById('totalTime')?.textContent || '0:00',
            speakers: parseInt(document.getElementById('speakers')?.value || 2),
            language: document.getElementById('language')?.value || 'en',
            style: document.getElementById('style')?.value || 'conversational',
            provider: this.selectedProvider,
            enhanced_features: {
                emotion_tags: document.getElementById('emotionToggle')?.checked || false,
                audio_events: document.getElementById('audioEventsToggle')?.checked || false
            },
            generated_at: new Date().toISOString(),
            script_length: this.generatedScript?.length || 0
        };
    }

    // Override goToStep to handle voice loading
    goToStep(step) {
        if (step < 1 || step > this.maxSteps + 1) return; // +1 for results panel

        // Hide all panels
        document.querySelectorAll('.step-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        // Show target panel
        let targetPanel;
        if (step <= this.maxSteps) {
            targetPanel = document.getElementById(`step${step}Panel`);
        } else {
            targetPanel = document.getElementById('resultsPanel');
        }

        if (targetPanel) {
            targetPanel.classList.add('active');
        }

        // Load voices when entering step 3
        if (step === 3) {
            this.loadVoices();
        }

        this.currentStep = step;
        this.updateWorkflowProgress();
    }

    // Social Sharing Functions
    bindSharingButtons() {
        document.querySelectorAll('.share-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const platform = btn.dataset.platform;
                this.shareOnPlatform(platform);
            });
        });
    }

    shareOnPlatform(platform) {
        const audioPlayer = document.getElementById('finalAudio');
        const title = document.getElementById('topic')?.value || 'AI Generated Podcast';
        const description = 'Check out this AI-generated podcast created with Enhanced ElevenLabs TTS!';
        const url = audioPlayer?.src || window.location.href;

        switch (platform) {
            case 'twitter':
                const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title + ' - ' + description)}&url=${encodeURIComponent(url)}`;
                window.open(twitterUrl, '_blank');
                break;

            case 'linkedin':
                const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
                window.open(linkedinUrl, '_blank');
                break;

            case 'facebook':
                const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                window.open(facebookUrl, '_blank');
                break;

            case 'copy':
                navigator.clipboard.writeText(url).then(() => {
                    this.showSuccess('Link copied to clipboard!');
                }).catch(() => {
                    this.showError('Failed to copy link');
                });
                break;
        }
    }

    // Collaboration Functions
    showInviteDialog() {
        // Create a simple invite dialog
        const dialog = document.createElement('div');
        dialog.className = 'invite-dialog-overlay';
        dialog.innerHTML = `
            <div class="invite-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-user-plus"></i> Invite Collaborators</h3>
                    <button class="close-btn" onclick="this.closest('.invite-dialog-overlay').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-content">
                    <div class="form-group">
                        <label>Email Address</label>
                        <input type="email" id="inviteEmail" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>Permission Level</label>
                        <select id="invitePermission">
                            <option value="view">View Only</option>
                            <option value="comment">Can Comment</option>
                            <option value="edit">Can Edit</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Message (Optional)</label>
                        <textarea id="inviteMessage" placeholder="Let's collaborate on this podcast project..."></textarea>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="btn btn-secondary" onclick="this.closest('.invite-dialog-overlay').remove()">
                        Cancel
                    </button>
                    <button class="btn btn-primary" onclick="podcastStudio.sendInvite()">
                        <i class="fas fa-paper-plane"></i> Send Invite
                    </button>
                </div>
            </div>
        `;

        // Add dialog styles
        const style = document.createElement('style');
        style.textContent = `
            .invite-dialog-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            .invite-dialog {
                background: var(--glass-bg);
                backdrop-filter: blur(20px);
                border: 1px solid var(--glass-border);
                border-radius: var(--radius-2xl);
                padding: var(--spacing-2xl);
                max-width: 500px;
                width: 90%;
                color: white;
            }
            .dialog-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: var(--spacing-lg);
            }
            .dialog-header h3 {
                margin: 0;
                display: flex;
                align-items: center;
                gap: var(--spacing-sm);
            }
            .close-btn {
                background: none;
                border: none;
                color: white;
                font-size: var(--font-size-lg);
                cursor: pointer;
                padding: var(--spacing-sm);
                border-radius: 50%;
                transition: background 0.3s ease;
            }
            .close-btn:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            .dialog-actions {
                display: flex;
                gap: var(--spacing-md);
                justify-content: flex-end;
                margin-top: var(--spacing-lg);
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(dialog);
    }

    async sendInvite() {
        const email = document.getElementById('inviteEmail')?.value;
        const permission = document.getElementById('invitePermission')?.value;
        const message = document.getElementById('inviteMessage')?.value;

        if (!email) {
            this.showError('Please enter an email address');
            return;
        }

        try {
            const response = await fetch('/api/invite_collaborator', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email,
                    permission,
                    message,
                    project_id: 'current_podcast' // This would be dynamic in a real app
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showSuccess('Invitation sent successfully!');
                document.querySelector('.invite-dialog-overlay')?.remove();
                this.updateCollaboratorsList();
            } else {
                this.showError(data.error || 'Failed to send invitation');
            }
        } catch (error) {
            console.error('Invite error:', error);
            this.showError('Failed to send invitation');
        }
    }

    async updateCollaboratorsList() {
        try {
            const response = await fetch('/api/collaborators');
            const data = await response.json();

            if (data.success && data.collaborators) {
                const container = document.querySelector('.collaborator-list');
                if (container) {
                    container.innerHTML = data.collaborators.map(collaborator => `
                        <div class="collaborator-item">
                            <div class="collaborator-avatar">
                                <img src="${collaborator.avatar || '/static/images/avatar-placeholder.png'}"
                                     alt="${collaborator.name}"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="avatar-fallback" style="display:none;">
                                    <i class="fas fa-user"></i>
                                </div>
                            </div>
                            <div class="collaborator-info">
                                <div class="collaborator-name">${collaborator.name}</div>
                                <div class="collaborator-role">${collaborator.role}</div>
                            </div>
                            <div class="collaborator-status ${collaborator.status}"></div>
                        </div>
                    `).join('');
                }
            }
        } catch (error) {
            console.error('Failed to update collaborators list:', error);
        }
    }

    // Enhanced Audio Generation with Quality Metrics
    async regenerateAudio() {
        this.showLoading('Regenerating audio...', 'Applying latest enhancements and optimizations');

        try {
            const response = await fetch('/api/regenerate_audio', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    script: this.generatedScript,
                    voices: this.selectedVoices,
                    provider: this.selectedProvider,
                    enhanced_features: {
                        emotion_tags: document.getElementById('emotionToggle')?.checked || false,
                        audio_events: document.getElementById('audioEventsToggle')?.checked || false
                    }
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
                this.updateQualityMetrics(data.quality_metrics);
                this.showSuccess('Audio regenerated successfully!');
            } else {
                this.showError(data.error || 'Failed to regenerate audio');
            }
        } catch (error) {
            console.error('Audio regeneration error:', error);
            this.showError('Failed to regenerate audio');
        } finally {
            this.hideLoading();
        }
    }

    updateQualityMetrics(metrics) {
        if (!metrics) return;

        const elements = {
            audioQuality: document.getElementById('audioQuality'),
            aiScore: document.getElementById('aiScore'),
            processingSpeed: document.getElementById('processingSpeed'),
            enhancementScore: document.getElementById('enhancementScore')
        };

        if (elements.audioQuality) elements.audioQuality.textContent = metrics.audio_quality || 'High';
        if (elements.aiScore) elements.aiScore.textContent = `${metrics.ai_naturalness || 95}%`;
        if (elements.processingSpeed) elements.processingSpeed.textContent = metrics.processing_speed || 'Fast';
        if (elements.enhancementScore) elements.enhancementScore.textContent = `${metrics.enhancement_score || 8}/10`;
    }

    // Enhanced Success/Error Messages
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add notification styles if not already added
        if (!document.querySelector('#notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: var(--glass-bg);
                    backdrop-filter: blur(20px);
                    border: 1px solid var(--glass-border);
                    border-radius: var(--radius-lg);
                    padding: var(--spacing-lg);
                    color: white;
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-md);
                    z-index: 1000;
                    animation: slideInFromRight 0.3s ease;
                    max-width: 400px;
                }
                .notification-success {
                    border-left: 4px solid var(--success-color);
                }
                .notification-error {
                    border-left: 4px solid var(--error-color);
                }
                .notification-content {
                    display: flex;
                    align-items: center;
                    gap: var(--spacing-sm);
                    flex: 1;
                }
                .notification-close {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: var(--spacing-xs);
                    border-radius: 50%;
                    transition: background 0.3s ease;
                }
                .notification-close:hover {
                    background: rgba(255, 255, 255, 0.1);
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize the application
let podcastStudio;
document.addEventListener('DOMContentLoaded', () => {
    podcastStudio = new EnhancedPodcastStudio();
});
