// Global variables for Podcast AI
let currentSessionId = null;
let currentScript = null;
let sessionStartTime = null;

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 Podcast AI - Initializing Readdy.ai inspired interface...');
    initializeApp();
});

// Initialize the application
function initializeApp() {
    console.log('🚀 Initializing Podcast AI...');
    
    // Initialize form handling
    initializeFormHandling();
    
    // Initialize status tracking
    initializeStatusTracking();
    
    // Initialize interactive elements
    initializeInteractiveElements();
    
    // Update initial status
    updateStatus('Ready', '0%', '--');
}

// Initialize interactive elements
function initializeInteractiveElements() {
    // Hero CTA buttons
    const tryFreeBtn = document.getElementById('try-free-btn');
    const watchDemoBtn = document.getElementById('watch-demo-btn');
    
    if (tryFreeBtn) {
        tryFreeBtn.addEventListener('click', () => {
            document.querySelector('.design-preview').scrollIntoView({ 
                behavior: 'smooth' 
            });
        });
    }
    
    if (watchDemoBtn) {
        watchDemoBtn.addEventListener('click', () => {
            // Simulate demo modal or action
            alert('Demo coming soon! 🎬');
        });
    }
    
    // Preview tabs
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all tabs
            tabBtns.forEach(tab => tab.classList.remove('active'));
            // Add active class to clicked tab
            btn.classList.add('active');
            
            // Here you could add logic to switch between desktop/mobile views
            console.log(`Switched to ${btn.dataset.tab} view`);
        });
    });
    
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Initialize form handling
function initializeFormHandling() {
    const form = document.getElementById('podcast-form');
    
    if (form) {
        form.addEventListener('submit', handleFormSubmission);
    }
}

// Initialize status tracking
function initializeStatusTracking() {
    // Set initial values
    updateStatus('Ready', '0%', '--');
}

// Update status display
function updateStatus(status, progress, time) {
    const statusValue = document.getElementById('status-value');
    const progressValue = document.getElementById('progress-value');
    const timeValue = document.getElementById('time-value');
    
    if (statusValue) statusValue.textContent = status;
    if (progressValue) progressValue.textContent = progress;
    if (timeValue) timeValue.textContent = time;
}

// Handle form submission
async function handleFormSubmission(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const generateBtn = document.getElementById('generate-btn');
    const startTime = Date.now();
    
    // Show loading state
    setButtonLoading(generateBtn, true);
    updateStatus('Generating', '10%', '0s');
    
    try {
        // Simulate progress updates
        const progressInterval = setInterval(() => {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const progress = Math.min(Math.floor(elapsed / 3), 90);
            updateStatus('Generating', `${progress}%`, `${elapsed}s`);
        }, 1000);
        
        const response = await fetch('/generate_podcast', {
            method: 'POST',
            body: formData
        });
        
        clearInterval(progressInterval);
        
        if (response.ok) {
            const result = await response.json();
            console.log('Podcast generated successfully:', result);
            const totalTime = Math.floor((Date.now() - startTime) / 1000);
            updateStatus('Complete', '100%', `${totalTime}s`);
            
            // Store session data
            currentSessionId = result.session_id;
            currentScript = result.script;
        } else {
            console.error('Failed to generate podcast');
            updateStatus('Error', '0%', '--');
        }
    } catch (error) {
        console.error('Error generating podcast:', error);
        updateStatus('Error', '0%', '--');
    } finally {
        setButtonLoading(generateBtn, false);
    }
}

// Set button loading state
function setButtonLoading(button, isLoading) {
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    
    if (isLoading) {
        btnText.classList.add('hidden');
        btnLoading.classList.remove('hidden');
        button.disabled = true;
    } else {
        btnText.classList.remove('hidden');
        btnLoading.classList.add('hidden');
        button.disabled = false;
    }
}

// Utility function to format time
function formatTime(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    } else {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}m ${remainingSeconds}s`;
    }
}