// Global variables
let currentSessionId = null;
let currentScript = null;
let availableVoices = [];
let selectedVoices = {};
let ttsProviders = [];
let currentTTSProvider = 'auto';
let providerVoices = {}; // Store voices for each provider
let currentWorkflowStep = 1;

// Dashboard state
let sessionStartTime = null;
let sessionTimer = null;
let dashboardMetrics = {
    generationSpeed: 0,
    researchSources: 0,
    scriptLength: 0,
    sessionEvents: []
};

// Animation and interaction helpers
const ANIMATION_DURATION = 300;
const WORKFLOW_STEPS = {
    INPUT: 1,
    REVIEW: 2,
    AUDIO: 3
};

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 PodcastMaster AI - Initializing minimal interface...');
    initializeApp();
    initializeMinimalInterface();
});

// Initialize minimal interface
function initializeMinimalInterface() {
    console.log('🎛️ Initializing minimal interface...');
    
    // Initialize navigation tabs
    initializeNavigationTabs();
    
    // Initialize form handling
    initializeFormHandling();
    
    // Initialize progress tracking
    initializeProgressTracking();
}

// Initialize navigation tabs
function initializeNavigationTabs() {
    const navTabs = document.querySelectorAll('.nav-tab');
    
    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            navTabs.forEach(t => t.classList.remove('active'));
            // Add active class to clicked tab
            this.classList.add('active');
        });
    });
}

// Initialize form handling
function initializeFormHandling() {
    const form = document.getElementById('podcast-form');
    
    if (form) {
        form.addEventListener('submit', handleFormSubmission);
    }
}

// Initialize progress tracking
function initializeProgressTracking() {
    // Update progress steps based on current state
    updateProgressStep(1);
}

// Update progress step
function updateProgressStep(stepNumber) {
    const progressSteps = document.querySelectorAll('.progress-step');
    
    progressSteps.forEach((step, index) => {
        const stepNum = index + 1;
        
        if (stepNum <= stepNumber) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
}

// Handle form submission
async function handleFormSubmission(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const generateBtn = document.getElementById('generate-btn');
    
    // Show loading state
    setButtonLoading(generateBtn, true);
    updateProgressStep(2);
    
    try {
        const response = await fetch('/generate_podcast', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('Podcast generated successfully:', result);
            updateProgressStep(3);
        } else {
            console.error('Failed to generate podcast');
            updateProgressStep(1);
        }
    } catch (error) {
        console.error('Error generating podcast:', error);
        updateProgressStep(1);
    } finally {
        setButtonLoading(generateBtn, false);
    }
}

// Initialize workflow progress visualization
function initializeWorkflowProgress() {
    updateWorkflowStep(WORKFLOW_STEPS.INPUT);
}

// Initialize button loading states
function initializeButtonStates() {
    // Add click handlers for all buttons with loading states
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function() {
            if (this.type === 'submit' || this.classList.contains('btn-async')) {
                setButtonLoading(this, true);
            }
        });
    });
}

// Set button loading state
function setButtonLoading(button, isLoading) {
    const btnText = button.querySelector('.btn-text');
    const btnLoading = button.querySelector('.btn-loading');
    
    if (isLoading) {
        button.disabled = true;
        button.classList.add('loading');
        if (btnText) btnText.style.opacity = '0';
        if (btnLoading) btnLoading.classList.remove('hidden');
    } else {
        button.disabled = false;
        button.classList.remove('loading');
        if (btnText) btnText.style.opacity = '1';
        if (btnLoading) btnLoading.classList.add('hidden');
    }
}

// Enhanced section transitions
function showSectionWithAnimation(section, delay = 0) {
    return new Promise(resolve => {
        setTimeout(() => {
            section.classList.remove('hidden');
            section.classList.add('active');
            
            // Trigger animation
            requestAnimationFrame(() => {
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            });
            
            setTimeout(resolve, ANIMATION_DURATION);
        }, delay);
    });
}

function hideSectionWithAnimation(section) {
    return new Promise(resolve => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            section.classList.add('hidden');
            section.classList.remove('active');
            resolve();
        }, ANIMATION_DURATION);
    });
}

// Update workflow step visualization
function updateWorkflowStep(step) {
    currentWorkflowStep = step;
    const workflowSteps = document.querySelectorAll('.workflow-step');
    
    workflowSteps.forEach((stepElement, index) => {
        const stepNumber = index + 1;
        stepElement.classList.remove('active', 'completed');
        
        if (stepNumber < step) {
            stepElement.classList.add('completed');
        } else if (stepNumber === step) {
            stepElement.classList.add('active');
        }
    });
}

function initializeApp() {
    // DOM Elements
    const form = document.getElementById('podcast-form');
    const progressSection = document.getElementById('progress-section');
    const reportSection = document.getElementById('report-section');
    const scriptSection = document.getElementById('script-section');
    const reviewSection = document.getElementById('review-section');
    const audioSection = document.getElementById('audio-section');
    const errorSection = document.getElementById('error-section');
    const scriptEditorModal = document.getElementById('script-editor-modal');

    console.log('✅ Form element found:', !!form);
    console.log('✅ Progress section found:', !!progressSection);

    // Load TTS provider information
    loadTTSProviders();

    // Initialize button loading states
    initializeButtonStates();

    // Form submission handler
    if (form) {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            console.log('Form submitted, preventDefault called');
            
            // Get generate button and set loading state
            const generateBtn = document.getElementById('generate-btn');
            setButtonLoading(generateBtn, true);
            
            // Get form data
            const formData = new FormData(form);
            currentTTSProvider = formData.get('tts_provider') || 'auto';
            const requestData = {
                topic: formData.get('topic'),
                script_style: formData.get('script_style'),
                num_speakers: parseInt(formData.get('num_speakers')),
                duration_target: parseInt(formData.get('duration_target')),
                language: formData.get('language'),
                tts_provider: currentTTSProvider
            };
            
            // Debug: Log the request data
            console.log('🚀 Generating podcast:', requestData);
            
            // Update workflow and show progress
            updateWorkflowStep(WORKFLOW_STEPS.INPUT);
            updateWorkflowStepEnhanced(WORKFLOW_STEPS.REVIEW);
            await hideAllSections();
            await showSectionWithAnimation(progressSection, 300);
            updateEnhancedProgress('searching', 0);
            updateMiniProgress(5, 'Starting generation...');
            
            try {
                // Load voices for current TTS provider
                await loadVoicesForProvider(currentTTSProvider);
                
                // Make API request for script generation (Phase 1)
                const response = await fetch('/api/generate-script', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (data.status === 'error') {
                    throw new Error(data.error);
                }
                
                currentSessionId = data.request_id;
                
                // Update dashboard
                updateSessionIdDisplay(currentSessionId);
                updateDashboardStatus('Generating Script');
                updateMiniProgress(10, 'Processing request...');
                addTimelineEvent('REQUEST', 'Request Submitted', `Session ID: ${currentSessionId.substring(0, 8)}...`, 'completed');
                
                // If already completed (sync mode), display results immediately
                if (data.status === 'script_ready' || data.status === 'completed') {
                    currentScript = data.script;
                    displayScriptForReview(data);
                } else {
                    // Start polling for status updates
                    setTimeout(pollStatusPhase1, 1000);
                }
                
            } catch (error) {
                setButtonLoading(generateBtn, false);
                showEnhancedError(error.message);
            } finally {
                // Reset button state after a delay if still loading
                setTimeout(() => {
                    if (generateBtn.classList.contains('loading')) {
                        setButtonLoading(generateBtn, false);
                    }
                }, 5000);
            }
        });
    } else {
        console.error('Form element not found!');
    }

    // Script editor functionality
    const editScriptBtn = document.getElementById('edit-script-btn');
    const closeBtn = document.querySelector('.modal-close');
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    const saveScriptBtn = document.getElementById('save-script-btn');
    const approveScriptBtn = document.getElementById('approve-script-btn');
    const editBeforeApproveBtn = document.getElementById('edit-before-approve-btn');
    const retryBtn = document.getElementById('retry-btn');

    if (editScriptBtn) {
        editScriptBtn.addEventListener('click', () => {
            if (!currentScript) return;
            
            const editor = document.getElementById('script-editor');
            editor.value = JSON.stringify(currentScript, null, 2);
            scriptEditorModal.classList.remove('hidden');
            scriptEditorModal.setAttribute('aria-hidden', 'false');
            editor.focus();
        });
    }

    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            scriptEditorModal.classList.add('hidden');
            scriptEditorModal.setAttribute('aria-hidden', 'true');
        });
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            scriptEditorModal.classList.add('hidden');
            scriptEditorModal.setAttribute('aria-hidden', 'true');
        });
    }

    if (saveScriptBtn) {
        saveScriptBtn.addEventListener('click', async () => {
            const editor = document.getElementById('script-editor');
            
            try {
                // Validate JSON
                const updatedScript = JSON.parse(editor.value);
                
                // Send update request
                const formData = new FormData();
                formData.append('session_id', currentSessionId);
                formData.append('updated_script', editor.value);
                
                const response = await fetch('/api/update-script', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentScript = data.script;
                    displayScript(data.script);
                    displayAudio(data.audio_url);
                    scriptEditorModal.classList.add('hidden');
                    scriptEditorModal.setAttribute('aria-hidden', 'true');
                } else {
                    throw new Error('Failed to update script');
                }
                
            } catch (error) {
                alert('Error updating script: ' + error.message);
            }
        });
    }

    // Approve script and start audio generation
    if (approveScriptBtn) {
        approveScriptBtn.addEventListener('click', async () => {
            // Validate that all speakers have voices selected
            const speakers = [...new Set(currentScript.dialogue.map(line => line.speaker_name))];
            const missingVoices = speakers.filter(speaker => !selectedVoices[speaker] || !selectedVoices[speaker].voice_id);
            
            if (missingVoices.length > 0) {
                alert(`Please select voices for: ${missingVoices.join(', ')}`);
                return;
            }
            
            try {
                // Show TTS status
                updateTTSStatus(getTTSProviderName(currentTTSProvider), 'Starting audio generation...');

                // Prepare form data
                const formData = new FormData();
                formData.append('session_id', currentSessionId);
                formData.append('approved_script', JSON.stringify(currentScript));
                formData.append('voice_selections', JSON.stringify(selectedVoices));
                formData.append('tts_provider', currentTTSProvider);

                // Submit to new audio generation endpoint
                const response = await fetch('/api/generate-audio', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // Update TTS status with results
                    const providerUsed = data.provider_used || currentTTSProvider;
                    const fallbackInfo = data.fallback_used ? ' (fallback used)' : '';
                    updateTTSStatus(getTTSProviderName(providerUsed), `Completed in ${data.generation_time?.toFixed(1)}s${fallbackInfo}`);

                    // Update dashboard for completion
                    updateWorkflowStepEnhanced(WORKFLOW_STEPS.AUDIO);
                    updateDashboardStatus('Complete');
                    updateMiniProgress(100, 'Podcast ready!');
                    
                    // Update generation speed metric
                    if (data.generation_time) {
                        updateMetrics('generationSpeed', Math.round(data.generation_time), 'Fast');
                    }
                    
                    addTimelineEvent('SUCCESS', 'Audio Generated', `Podcast created with ${getTTSProviderName(providerUsed)}`, 'completed');

                    // Show audio player
                    displayAudioPlayer(data.audio_url);

                } else {
                    // Handle audio generation failure
                    const errorMsg = data.error || 'Audio generation failed';
                    updateTTSStatus(getTTSProviderName(currentTTSProvider), `Failed: ${errorMsg}`);

                    // Try fallback or show error
                    if (data.fallback_used) {
                        throw new Error(`Audio generation failed even with fallback: ${errorMsg}`);
                    } else {
                        // Start polling for potential fallback
                        setTimeout(pollStatusPhase2, 1000);
                    }
                }
                
            } catch (error) {
                showError('Error approving script: ' + error.message);
            }
        });
    }

    // Edit script before approval
    if (editBeforeApproveBtn) {
        editBeforeApproveBtn.addEventListener('click', () => {
            if (!currentScript) return;
            
            const editor = document.getElementById('script-editor');
            editor.value = JSON.stringify(currentScript, null, 2);
            scriptEditorModal.classList.remove('hidden');
            scriptEditorModal.setAttribute('aria-hidden', 'false');
            editor.focus();
        });
    }

    if (retryBtn) {
        retryBtn.addEventListener('click', () => {
            hideAllSections();
            showSection(document.querySelector('#input-section'));
        });
    }

    // Create new podcast button
    const createNewBtn = document.getElementById('create-new-btn');
    if (createNewBtn) {
        createNewBtn.addEventListener('click', () => {
            // Reset state
            currentSessionId = null;
            currentScript = null;
            selectedVoices = {};
            
            // Reset form
            const form = document.getElementById('podcast-form');
            if (form) form.reset();
            
            // Go back to input section
            hideAllSections();
            showSection(document.querySelector('#input-section'));
            updateWorkflowStep(WORKFLOW_STEPS.INPUT);
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === scriptEditorModal) {
            scriptEditorModal.classList.add('hidden');
            scriptEditorModal.setAttribute('aria-hidden', 'true');
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && !scriptEditorModal.classList.contains('hidden')) {
            scriptEditorModal.classList.add('hidden');
            scriptEditorModal.setAttribute('aria-hidden', 'true');
        }
    });

    // Utility functions
    function hideAllSections() {
        const sections = [progressSection, reportSection, scriptSection, reviewSection, audioSection, errorSection];
        sections.forEach(section => section.classList.add('hidden'));
    }

    function showSection(section) {
        section.classList.remove('hidden');
    }

    // Error handling
    function showError(message) {
        hideAllSections();
        document.getElementById('error-message').textContent = message;
        showSection(errorSection);
    }
}

// Poll for script generation status (Phase 1)
async function pollStatusPhase1() {
    if (!currentSessionId) return;
    
    try {
        const response = await fetch(`/api/status/${currentSessionId}`);
        const data = await response.json();
        
        updateProgress(data.status, getProgressPercentage(data.status));
        
        if (data.status === 'script_ready') {
            // Script is ready for review, get the results
            const resultResponse = await fetch(`/api/session/${currentSessionId}`);
            const result = await resultResponse.json();
            
            if (result.error) {
                showError(result.error);
            } else {
                currentScript = result.script;
                displayScriptForReview(result);
            }
            
        } else if (data.status === 'error') {
            showError(data.error || 'Script generation failed');
        } else {
            // Continue polling
            setTimeout(pollStatusPhase1, 1000);
        }
    } catch (error) {
        showError(error.message);
    }
}

// Poll for audio generation status (Phase 2)
async function pollStatusPhase2() {
    if (!currentSessionId) return;
    
    try {
        const response = await fetch(`/api/status/${currentSessionId}`);
        const data = await response.json();
        
        updateProgress(data.status, getProgressPercentage(data.status));
        
        if (data.status === 'completed') {
            // Audio generation complete, get the full results
            const resultResponse = await fetch(`/api/session/${currentSessionId}`);
            const result = await resultResponse.json();
            
            if (result.error) {
                showError(result.error);
            } else {
                displayFinalResults(result);
            }
            
        } else if (data.status === 'error') {
            showError(data.error || 'Audio generation failed');
        } else {
            // Continue polling
            setTimeout(pollStatusPhase2, 1000);
        }
    } catch (error) {
        showError(error.message);
    }
}

// Enhanced progress update with modern animations
function updateEnhancedProgress(status, percentage) {
    const progressFill = document.querySelector('.progress-fill');
    const progressPercentage = document.querySelector('.progress-percentage');
    const stages = document.querySelectorAll('.stage');
    
    // Update progress bar with animation
    if (progressFill) {
        progressFill.style.width = `${percentage}%`;
    }
    
    // Update percentage with counter animation
    if (progressPercentage) {
        animateCounter(progressPercentage, parseInt(progressPercentage.textContent) || 0, percentage);
    }
    
    // Update dashboard mini progress
    const statusTexts = {
        'searching': 'Searching web...',
        'generating_report': 'Creating report...',
        'generating_script': 'Writing script...',
        'script_ready': 'Script complete!',
        'synthesizing_audio': 'Generating audio...',
        'completed': 'Podcast ready!'
    };
    
    updateMiniProgress(percentage, statusTexts[status] || 'Processing...');
    updateDashboardStatus(statusTexts[status] || 'Processing...');
    
    // Add timeline events for major milestones
    const eventMapping = {
        'searching': { icon: 'SEARCH', title: 'Web Research Started', desc: 'Gathering information from web sources' },
        'generating_report': { icon: 'ANALYSIS', title: 'Report Generation', desc: 'Creating research summary' },
        'generating_script': { icon: 'SCRIPT', title: 'Script Writing', desc: 'Crafting podcast dialogue' },
        'script_ready': { icon: 'COMPLETE', title: 'Script Complete', desc: 'Podcast script ready for review' },
        'synthesizing_audio': { icon: 'AUDIO', title: 'Audio Synthesis', desc: 'Converting script to speech' },
        'completed': { icon: 'SUCCESS', title: 'Generation Complete', desc: 'Podcast ready for download' }
    };
    
    if (eventMapping[status]) {
        const event = eventMapping[status];
        addTimelineEvent(event.icon, event.title, event.desc, status === 'completed' ? 'completed' : 'active');
    }
    
    // Update stage indicators
    const stageMapping = {
        'searching': 0,
        'generating_report': 1,
        'generating_script': 2,
        'script_ready': 3,
        'synthesizing_audio': 3
    };
    
    const currentStageIndex = stageMapping[status] || 0;
    
    stages.forEach((stage, index) => {
        stage.classList.remove('active', 'completed');
        if (index < currentStageIndex) {
            stage.classList.add('completed');
        } else if (index === currentStageIndex) {
            stage.classList.add('active');
        }
    });
}

// Animate counter numbers
function animateCounter(element, start, end, duration = 1000) {
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;
    
    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
            current = end;
            clearInterval(timer);
        }
        element.textContent = Math.round(current) + '%';
    }, 16);
}

// Enhanced error display
function showEnhancedError(message) {
    const errorSection = document.getElementById('error-section');
    const errorMessage = document.getElementById('error-message');
    
    if (errorMessage) {
        errorMessage.textContent = message;
    }
    
    hideAllSections().then(() => {
        showSectionWithAnimation(errorSection, 200);
    });
    
    // Add shake animation to error section
    if (errorSection) {
        errorSection.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            errorSection.style.animation = '';
        }, 500);
    }
}

// Enhanced section hiding with promise
function hideAllSections() {
    const sections = [
        'input-section', 'progress-section', 'report-section', 
        'script-section', 'review-section', 'audio-section', 'error-section'
    ];
    
    const promises = sections.map(id => {
        const section = document.getElementById(id);
        if (section && !section.classList.contains('hidden')) {
            return hideSectionWithAnimation(section);
        }
        return Promise.resolve();
    });
    
    return Promise.all(promises);
}

// Legacy compatibility functions
function updateProgress(status, percentage) {
    updateEnhancedProgress(status, percentage);
}

function showError(message) {
    showEnhancedError(message);
}

function showSection(section) {
    if (section) {
        showSectionWithAnimation(section);
    }
}

// Update progress display (legacy)
function updateProgressLegacy(status, percentage) {
    const progressFill = document.querySelector('.progress-fill');
    const progressText = document.querySelector('.progress-text');
    const steps = document.querySelectorAll('.step');
    
    progressFill.style.width = `${percentage}%`;
    
    const statusTexts = {
        'searching': 'Searching the web for information...',
        'generating_report': 'Generating research report...',
        'generating_script': 'Writing podcast script...',
        'script_ready': 'Script ready for review!',
        'synthesizing_audio': 'Synthesizing audio with multiple voices...',
        'completed': 'Podcast generation complete!'
    };
    
    progressText.textContent = statusTexts[status] || 'Processing...';
    
    // Update step indicators
    const stepOrder = ['searching', 'generating_report', 'generating_script', 'script_ready', 'synthesizing_audio'];
    const currentIndex = stepOrder.indexOf(status);
    
    steps.forEach((step, index) => {
        step.classList.remove('active', 'completed');
        if (index < currentIndex) {
            step.classList.add('completed');
        } else if (index === currentIndex) {
            step.classList.add('active');
        }
    });
}

// Get progress percentage based on status
function getProgressPercentage(status) {
    const percentages = {
        'searching': 20,
        'generating_report': 40,
        'generating_script': 60,
        'script_ready': 65,
        'synthesizing_audio': 85,
        'completed': 100
    };
    return percentages[status] || 0;
}

// Display generation results
function displayResults(data) {
    const progressSection = document.getElementById('progress-section');
    const reportSection = document.getElementById('report-section');
    const scriptSection = document.getElementById('script-section');
    const audioSection = document.getElementById('audio-section');
    
    hideAllSections();
    
    // Display report
    if (data.report) {
        displayReport(data.report);
        showSection(reportSection);
    }
    
    // Display script
    if (data.script) {
        displayScript(data.script);
        showSection(scriptSection);
    }
    
    // Display audio player
    if (data.audio_url) {
        displayAudio(data.audio_url);
        showSection(audioSection);
    }
}

// Display research report
function displayReport(report) {
    const reportContent = document.getElementById('report-content');
    
    let html = `
        <h3>Summary</h3>
        <div class="summary">${marked.parse(report.summary)}</div>
        
        <h3>Key Findings</h3>
        <ul>
            ${report.key_findings.map(finding => `<li>${finding}</li>`).join('')}
        </ul>
        
        <h3>Sources</h3>
        <ul class="sources">
            ${report.sources.map(source => `
                <li>
                    <strong>${source.title}</strong><br>
                    ${source.snippet}<br>
                    <small>Relevance: ${Math.round(source.relevance_score * 100)}%</small>
                </li>
            `).join('')}
        </ul>
    `;
    
    reportContent.innerHTML = html;
}

// Display podcast script
function displayScript(script) {
    document.getElementById('script-title').textContent = script.title;
    document.getElementById('script-description').textContent = script.description;
    
    const dialogueContainer = document.getElementById('script-dialogue');
    
    let html = script.dialogue.map(line => `
        <div class="dialogue-line">
            <div class="speaker">${line.speaker_name} (${line.role}) 
                ${line.emotion ? `<span class="emotion-tag">${getEmotionEmoji(line.emotion)} ${line.emotion}</span>` : ''}
            </div>
            <div class="text">${line.text}</div>
        </div>
    `).join('');
    
    dialogueContainer.innerHTML = html;
}

// Display audio player
function displayAudio(audioUrl) {
    const audioElement = document.getElementById('podcast-audio');
    const downloadBtn = document.getElementById('download-audio-btn');
    
    audioElement.src = audioUrl;
    downloadBtn.href = audioUrl;
    
    // Extract filename from URL for proper download
    const urlPath = audioUrl.split('/');
    const filename = urlPath[urlPath.length - 1];
    downloadBtn.download = filename || 'podcast.mp3';
}

// Get MiniMax voices
function getMinimaxVoices() {
    return [
        // Working Male Voices (confirmed working with MiniMax API)
        { voice_id: "audiobook_male_1", voice_name: "男性有声书1", gender: "male", category: "audiobook" },
        { voice_id: "audiobook_male_2", voice_name: "男性有声书2", gender: "male", category: "audiobook" },
        { voice_id: "presenter_male", voice_name: "男性主持人", gender: "male", category: "professional" },

        // Working Female Voices (confirmed working with MiniMax API)
        { voice_id: "audiobook_female_1", voice_name: "女性有声书1", gender: "female", category: "audiobook" },
        { voice_id: "audiobook_female_2", voice_name: "女性有声书2", gender: "female", category: "audiobook" },
        { voice_id: "presenter_female", voice_name: "女性主持人", gender: "female", category: "professional" },

        // Legacy voices that might work
        { voice_id: "Chinese (Mandarin)_Reliable_Executive", voice_name: "可靠的主管", gender: "male", category: "legacy" },
        { voice_id: "Chinese (Mandarin)_Wise_Women", voice_name: "智慧女性", gender: "female", category: "legacy" },
        { voice_id: "Chinese (Mandarin)_Narrator_Male", voice_name: "男性旁白", gender: "male", category: "legacy" },
        { voice_id: "Chinese (Mandarin)_Narrator_Female", voice_name: "女性旁白", gender: "female", category: "legacy" },

        // Try the Chinese voice IDs from your list
        { voice_id: "male-qn-qingse", voice_name: "青涩青年音色", gender: "male", category: "youth" },
        { voice_id: "male-qn-jingying", voice_name: "精英青年音色", gender: "male", category: "youth" },
        { voice_id: "male-qn-badao", voice_name: "霸道青年音色", gender: "male", category: "youth" },
        { voice_id: "male-qn-daxuesheng", voice_name: "青年大学生音色", gender: "male", category: "youth" },
        { voice_id: "female-shaonv", voice_name: "少女音色", gender: "female", category: "youth" },
        { voice_id: "female-yujie", voice_name: "御姐音色", gender: "female", category: "mature" },
        { voice_id: "female-chengshu", voice_name: "成熟女性音色", gender: "female", category: "mature" },
        { voice_id: "female-tianmei", voice_name: "甜美女性音色", gender: "female", category: "sweet" },

        // Character and roleplay voices
        { voice_id: "clever_boy", voice_name: "聪明男童", gender: "male", category: "child" },
        { voice_id: "cute_boy", voice_name: "可爱男童", gender: "male", category: "child" },
        { voice_id: "lovely_girl", voice_name: "萌萌女童", gender: "female", category: "child" },
        { voice_id: "bingjiao_didi", voice_name: "病娇弟弟", gender: "male", category: "roleplay" },
        { voice_id: "junlang_nanyou", voice_name: "俊朗男友", gender: "male", category: "roleplay" },
        { voice_id: "tianxin_xiaoling", voice_name: "甜心小玲", gender: "female", category: "roleplay" },
        { voice_id: "qiaopi_mengmei", voice_name: "俏皮萌妹", gender: "female", category: "roleplay" }
    ];
}

// Initialize fixed voice list with working MiniMax voice IDs
function initializeFixedVoices() {
    availableVoices = getMinimaxVoices();
    console.log('Initialized fixed voice list with', availableVoices.length, 'voices');
}

// Update existing voice selections when voices change
function updateVoiceSelections() {
    // Update all voice selector dropdowns if they exist
    document.querySelectorAll('.voice-selector').forEach(select => {
        const currentValue = select.value;
        const speaker = select.dataset.speaker;

        // Regenerate options
        select.innerHTML = `
            <option value="">Choose a voice...</option>
            ${generateVoiceOptionsGrouped()}
        `;

        // Try to restore previous selection if the voice still exists
        if (currentValue && availableVoices.find(v => v.voice_id === currentValue)) {
            select.value = currentValue;
        } else {
            // Clear the selection if the voice is no longer available
            select.value = "";
            if (selectedVoices[speaker]) {
                delete selectedVoices[speaker];
            }
        }
    });
}

// Generate grouped voice options for select dropdown
function generateVoiceOptionsGrouped() {
    const categories = {
        'professional': '专业主播',
        'audiobook': '有声书',
        'youth': '青年音色',
        'mature': '成熟音色',
        'sweet': '甜美音色',
        'beta': 'Beta版本',
        'child': '童声',
        'roleplay': '角色扮演',
        'cartoon': '卡通角色',
        'english': 'English Voices',
        'elevenlabs': 'ElevenLabs V2 Voices',
        'v3_premium': 'V3 Premium',
        'v3_multilingual': 'V3 Multilingual',
        'v3_conversational': 'V3 Conversational',
        'v3_narration': 'V3 Narration',
        'v3_standard': 'V3 Standard',
        'legacy': '经典音色'
    };
    
    let html = '';
    
    Object.keys(categories).forEach(category => {
        const categoryVoices = availableVoices.filter(voice => voice.category === category);
        if (categoryVoices.length > 0) {
            html += `<optgroup label="${categories[category]}">`;
            categoryVoices.forEach(voice => {
                html += `<option value="${voice.voice_id}">${voice.voice_name} (${voice.gender})</option>`;
            });
            html += `</optgroup>`;
        }
    });
    
    return html;
}

// Get emotion emoji for display
function getEmotionEmoji(emotion) {
    const emotionEmojis = {
        'happy': '😊',
        'sad': '😢', 
        'angry': '😠',
        'fearful': '😨',
        'disgusted': '🤢',
        'surprised': '😲',
        'neutral': '😐'
    };
    return emotionEmojis[emotion] || '😐';
}

// Display script for review (Phase 1 complete)
function displayScriptForReview(data) {
    const progressSection = document.getElementById('progress-section');
    const reportSection = document.getElementById('report-section');
    const scriptSection = document.getElementById('script-section');
    const reviewSection = document.getElementById('review-section');
    
    // Update dashboard for review phase
    updateWorkflowStepEnhanced(WORKFLOW_STEPS.REVIEW);
    updateDashboardStatus('Review Script');
    updateMiniProgress(75, 'Ready for review');
    
    // Update metrics
    if (data.script) {
        const wordCount = data.script.dialogue
            .map(line => line.text.split(' ').length)
            .reduce((a, b) => a + b, 0);
        updateMetrics('scriptLength', wordCount, 'Generated');
    }
    
    if (data.report && data.report.sources) {
        updateMetrics('researchSources', data.report.sources.length, 'Found');
    }
    
    addTimelineEvent('REVIEW', 'Review Phase', 'Script ready for voice selection and editing', 'active');
    
    hideAllSections();
    
    // Display report
    if (data.report) {
        displayReport(data.report);
        showSection(reportSection);
    }
    
    // Display script
    if (data.script) {
        displayScript(data.script);
        showSection(scriptSection);
        
        // Show voice selection interface
        displayVoiceSelection(data.script);
        showSection(reviewSection);
    }
}

// Enhanced voice selection interface with modern design
function displayVoiceSelection(script) {
    const container = document.getElementById('voice-selection-container');
    
    // Get unique speakers from script
    const speakers = [...new Set(script.dialogue.map(line => line.speaker_name))];
    
    let html = '';
    speakers.forEach((speaker, index) => {
        const speakerLines = script.dialogue.filter(line => line.speaker_name === speaker);
        const firstLine = speakerLines[0];
        const roleIcon = getRoleIcon(firstLine.role);
        const genderSuggestion = getGenderSuggestion(speaker, firstLine.role);
        
        html += `
            <div class="voice-selection-item" style="animation-delay: ${index * 0.1}s">
                <h4>${roleIcon} ${speaker} <span class="role-badge">${firstLine.role}</span></h4>
                <div class="sample-text">
                    <div class="sample-quote">"${firstLine.text.substring(0, 120)}..."</div>
                    <div class="sample-meta">Sample from dialogue line ${speakerLines.length > 1 ? `(${speakerLines.length} lines total)` : ''}</div>
                </div>
                
                <div class="voice-controls">
                    <div class="voice-select-wrapper">
                        <label for="voice-${speaker}">Choose Voice:</label>
                        <select id="voice-${speaker}" class="voice-selector modern-select" data-speaker="${speaker}">
                            <option value="">Select a voice...</option>
                            ${generateVoiceOptionsGrouped()}
                        </select>
                        <div class="voice-suggestion">💡 Suggested: ${genderSuggestion}</div>
                    </div>
                    
                    <button class="btn preview-voice-btn btn-async" data-speaker="${speaker}">
                        <span class="btn-icon">🔊</span>
                        <span class="btn-text">Preview</span>
                        <span class="btn-loading hidden">
                            <span class="spinner"></span>
                            Playing...
                        </span>
                    </button>
                </div>
                
                <div class="voice-preview-player hidden" id="preview-${speaker}">
                    <audio controls class="mini-audio-player"></audio>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // Add event listeners for enhanced voice selection
    setupEnhancedVoiceSelectionListeners();
    
    // Update workflow step
    updateWorkflowStep(WORKFLOW_STEPS.REVIEW);
}

// Get role icon for display
function getRoleIcon(role) {
    const icons = {
        'host': '🎙️',
        'expert': '🎓',
        'interviewer': '🎤',
        'guest': '👤'
    };
    return icons[role.toLowerCase()] || '🗣️';
}

// Get gender suggestion based on name and role
function getGenderSuggestion(name, role) {
    // Simple heuristic - in production, this could be more sophisticated
    const femaleIndicators = ['女', 'Female', 'ella', 'ana', 'ina', 'iya'];
    const maleIndicators = ['男', 'Male', 'Mr', 'Dr'];
    
    const nameContainsFemale = femaleIndicators.some(indicator => name.includes(indicator));
    const nameContainsMale = maleIndicators.some(indicator => name.includes(indicator));
    
    if (nameContainsFemale) return 'Female voice';
    if (nameContainsMale) return 'Male voice';
    
    // Default suggestions based on role
    const roleSuggestions = {
        'host': 'Warm, engaging voice',
        'expert': 'Professional, authoritative voice',
        'interviewer': 'Clear, curious voice',
        'guest': 'Natural, conversational voice'
    };
    
    return roleSuggestions[role.toLowerCase()] || 'Natural voice';
}

// Enhanced voice selection listeners
function setupEnhancedVoiceSelectionListeners() {
    // Voice selector change events with enhanced feedback
    document.querySelectorAll('.voice-selector').forEach(select => {
        select.addEventListener('change', (e) => {
            const speaker = e.target.dataset.speaker;
            const voiceId = e.target.value;
            
            // Add selection feedback
            const selectionItem = e.target.closest('.voice-selection-item');
            if (voiceId) {
                selectionItem.classList.add('voice-selected');
                selectedVoices[speaker] = {
                    voice_id: voiceId,
                    emotion: "auto"
                };
                
                // Show success feedback
                showVoiceSelectionFeedback(selectionItem, 'Voice selected!', 'success');
            } else {
                selectionItem.classList.remove('voice-selected');
                delete selectedVoices[speaker];
            }
            
            // Update approval button state
            updateApprovalButtonState();
        });
    });
    
    // Enhanced preview functionality
    document.querySelectorAll('.preview-voice-btn').forEach(btn => {
        btn.addEventListener('click', async (e) => {
            e.preventDefault();
            const speaker = e.target.closest('.preview-voice-btn').dataset.speaker;
            await previewVoiceEnhanced(speaker, btn);
        });
    });
}

// Enhanced voice preview with better UX
async function previewVoiceEnhanced(speaker, button) {
    const voiceSelect = document.getElementById(`voice-${speaker}`);
    const previewPlayer = document.getElementById(`preview-${speaker}`);
    
    if (!voiceSelect.value) {
        showVoiceSelectionFeedback(
            button.closest('.voice-selection-item'), 
            'Please select a voice first', 
            'warning'
        );
        return;
    }
    
    setButtonLoading(button, true);
    
    try {
        const sampleText = currentScript.dialogue
            .find(line => line.speaker_name === speaker)?.text.substring(0, 80) || "Hello, this is a voice preview.";
        
        const response = await fetch('/api/test-tts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `text=${encodeURIComponent(sampleText)}&voice_id=${encodeURIComponent(voiceSelect.value)}`
        });
        
        const data = await response.json();
        
        if (data.success && data.audio_path) {
            // Show and play audio
            const audio = previewPlayer.querySelector('audio');
            audio.src = data.audio_path;
            previewPlayer.classList.remove('hidden');
            
            showVoiceSelectionFeedback(
                button.closest('.voice-selection-item'),
                'Preview ready!',
                'success'
            );
            
            // Auto-play preview
            try {
                await audio.play();
            } catch (playError) {
                console.log('Auto-play prevented, user can manually play');
            }
        } else {
            throw new Error('Preview generation failed');
        }
    } catch (error) {
        showVoiceSelectionFeedback(
            button.closest('.voice-selection-item'),
            'Preview unavailable',
            'error'
        );
        console.error('Voice preview failed:', error);
    } finally {
        setButtonLoading(button, false);
    }
}

// Show feedback for voice selection actions
function showVoiceSelectionFeedback(container, message, type = 'info') {
    // Remove any existing feedback
    const existingFeedback = container.querySelector('.voice-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    // Create new feedback element
    const feedback = document.createElement('div');
    feedback.className = `voice-feedback voice-feedback-${type}`;
    feedback.textContent = message;
    
    // Add to container
    container.appendChild(feedback);
    
    // Animate in
    requestAnimationFrame(() => {
        feedback.style.opacity = '1';
        feedback.style.transform = 'translateY(0)';
    });
    
    // Remove after delay
    setTimeout(() => {
        feedback.style.opacity = '0';
        feedback.style.transform = 'translateY(-10px)';
        setTimeout(() => feedback.remove(), 300);
    }, 3000);
}

// Update approval button state based on voice selections
function updateApprovalButtonState() {
    const approveBtn = document.getElementById('approve-script-btn');
    if (!approveBtn || !currentScript) return;
    
    const speakers = [...new Set(currentScript.dialogue.map(line => line.speaker_name))];
    const selectedSpeakers = Object.keys(selectedVoices);
    const allVoicesSelected = speakers.every(speaker => 
        selectedVoices[speaker] && selectedVoices[speaker].voice_id
    );
    
    if (allVoicesSelected) {
        approveBtn.disabled = false;
        approveBtn.classList.remove('btn-disabled');
        approveBtn.querySelector('.btn-text').textContent = `Generate Audio (${speakers.length} voices ready)`;
    } else {
        approveBtn.disabled = true;
        approveBtn.classList.add('btn-disabled');
        const remaining = speakers.length - selectedSpeakers.length;
        approveBtn.querySelector('.btn-text').textContent = `Select ${remaining} more voice${remaining !== 1 ? 's' : ''}`;
    }
}

// Setup event listeners for voice selection
function setupVoiceSelectionListeners() {
    // Voice selector change events
    document.querySelectorAll('.voice-selector').forEach(select => {
        select.addEventListener('change', (e) => {
            const speaker = e.target.dataset.speaker;
            const voiceId = e.target.value;
            
            selectedVoices[speaker] = {
                voice_id: voiceId,
                emotion: "neutral"  // Default emotion, will be overridden by LLM analysis
            };
        });
    });
    
    // Preview voice buttons
    document.querySelectorAll('.preview-voice-btn').forEach(btn => {
        btn.addEventListener('click', async (e) => {
            const speaker = e.target.dataset.speaker;
            await previewVoice(speaker);
        });
    });
}

// Preview voice function
async function previewVoice(speaker) {
    const voiceSelect = document.getElementById(`voice-${speaker}`);
    
    if (!voiceSelect.value) {
        alert('Please select a voice first');
        return;
    }
    
    const sampleText = currentScript.dialogue.find(line => line.speaker_name === speaker)?.text.substring(0, 50) || "Hello, this is a voice preview.";
    
    try {
        const response = await fetch('/api/test-tts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `text=${encodeURIComponent(sampleText)}`
        });
        
        const data = await response.json();
        if (data.success && data.audio_path) {
            // Play preview audio
            const audio = new Audio(data.audio_path);
            audio.play();
        } else {
            console.log('Voice preview not available');
        }
    } catch (error) {
        console.error('Preview failed:', error);
    }
}

// Display final results (Phase 2 complete)
function displayFinalResults(data) {
    const progressSection = document.getElementById('progress-section');
    const reportSection = document.getElementById('report-section');
    const scriptSection = document.getElementById('script-section');
    const audioSection = document.getElementById('audio-section');
    
    hideAllSections();
    
    // Display report
    if (data.report) {
        displayReport(data.report);
        showSection(reportSection);
    }
    
    // Display script
    if (data.script) {
        displayScript(data.script);
        showSection(scriptSection);
    }
    
    // Display audio player
    if (data.audio_url) {
        displayAudio(data.audio_url);
        showSection(audioSection);
    }
}

// Utility functions for sections
function hideAllSections() {
    const sections = ['progress-section', 'report-section', 'script-section', 'review-section', 'audio-section', 'error-section'];
    sections.forEach(id => {
        const section = document.getElementById(id);
        if (section) section.classList.add('hidden');
    });
}

function showSection(section) {
    if (section) section.classList.remove('hidden');
}

function showError(message) {
    hideAllSections();
    const errorSection = document.getElementById('error-section');
    const errorMessage = document.getElementById('error-message');
    if (errorMessage) errorMessage.textContent = message;
    showSection(errorSection);
}

// TTS Provider Functions
async function loadTTSProviders() {
    try {
        const response = await fetch('/api/tts-providers');
        const data = await response.json();

        if (data.error) {
            console.error('Failed to load TTS providers:', data.error);
            return;
        }

        ttsProviders = data.providers;
        updateTTSProviderUI(data);

        // Load voices for the default provider
        await loadVoicesForProvider(currentTTSProvider);

    } catch (error) {
        console.error('Error loading TTS providers:', error);
    }
}

// Load voices for a specific TTS provider
async function loadVoicesForProvider(provider) {
    try {
        // Check if we already have voices for this provider
        if (providerVoices[provider]) {
            availableVoices = providerVoices[provider];
            updateVoiceSelections();
            return;
        }

        let voices = [];

        if (provider === 'minimax') {
            // Use fixed MiniMax voices
            voices = getMinimaxVoices();
        } else if (provider === 'elevenlabs') {
            // Load ElevenLabs V2 voices from API
            const response = await fetch(`/api/voices/elevenlabs`);
            if (response.ok) {
                const data = await response.json();
                voices = data.voices.map(voice => ({
                    voice_id: voice.voice_id,
                    voice_name: voice.name,
                    gender: voice.gender,
                    category: 'elevenlabs',
                    language: voice.language,
                    description: voice.description
                }));
            } else {
                console.error('Failed to load ElevenLabs V2 voices');
                voices = getMinimaxVoices(); // Fallback to MiniMax
            }
        } else if (provider === 'elevenlabs_v3') {
            // Load ElevenLabs V3 voices from API
            const response = await fetch(`/api/voices/elevenlabs_v3`);
            if (response.ok) {
                const data = await response.json();
                voices = data.voices.map(voice => ({
                    voice_id: voice.voice_id,
                    voice_name: voice.name,
                    gender: voice.gender,
                    category: voice.category || 'v3_standard',
                    language: voice.language,
                    description: voice.description,
                    features: voice.features || []
                }));
            } else {
                console.error('Failed to load ElevenLabs V3 voices');
                // Fallback to V2 voices
                try {
                    const fallbackResponse = await fetch(`/api/voices/elevenlabs`);
                    if (fallbackResponse.ok) {
                        const fallbackData = await fallbackResponse.json();
                        voices = fallbackData.voices.map(voice => ({
                            voice_id: voice.voice_id,
                            voice_name: `${voice.name} (V2 Fallback)`,
                            gender: voice.gender,
                            category: 'elevenlabs',
                            language: voice.language,
                            description: voice.description
                        }));
                    } else {
                        voices = getMinimaxVoices(); // Final fallback
                    }
                } catch (error) {
                    voices = getMinimaxVoices(); // Final fallback
                }
            }
        } else if (provider === 'auto') {
            // For auto selection, combine all available voices
            voices = getMinimaxVoices();

            // Try to load V3 voices first
            try {
                const v3Response = await fetch(`/api/voices/elevenlabs_v3`);
                if (v3Response.ok) {
                    const v3Data = await v3Response.json();
                    const v3Voices = v3Data.voices.map(voice => ({
                        voice_id: voice.voice_id,
                        voice_name: `${voice.name} (V3)`,
                        gender: voice.gender,
                        category: voice.category || 'v3_standard',
                        language: voice.language,
                        description: voice.description,
                        features: voice.features || []
                    }));
                    voices = voices.concat(v3Voices);
                }
            } catch (error) {
                console.warn('Could not load ElevenLabs V3 voices for auto mode');
            }

            // Also load V2 voices as fallback
            try {
                const v2Response = await fetch(`/api/voices/elevenlabs`);
                if (v2Response.ok) {
                    const v2Data = await v2Response.json();
                    const v2Voices = v2Data.voices.map(voice => ({
                        voice_id: voice.voice_id,
                        voice_name: `${voice.name} (V2)`,
                        gender: voice.gender,
                        category: 'elevenlabs',
                        language: voice.language,
                        description: voice.description
                    }));
                    voices = voices.concat(v2Voices);
                }
            } catch (error) {
                console.warn('Could not load ElevenLabs V2 voices for auto mode');
            }
        }

        // Cache the voices for this provider
        providerVoices[provider] = voices;
        availableVoices = voices;

        // Update any existing voice selections
        updateVoiceSelections();

        console.log(`Loaded ${voices.length} voices for ${provider}`);

    } catch (error) {
        console.error(`Error loading voices for ${provider}:`, error);
        // Fallback to MiniMax voices
        availableVoices = getMinimaxVoices();
        updateVoiceSelections();
    }
}

function updateTTSProviderUI(data) {
    const ttsSelect = document.getElementById('tts-provider');
    const ttsHelp = document.getElementById('tts-help');

    if (!ttsSelect || !ttsHelp) return;

    // Update help text based on provider availability
    const availableProviders = data.providers.filter(p => p.available);
    const providerNames = availableProviders.map(p => p.name).join(', ');
    ttsHelp.textContent = `Available: ${providerNames}`;

    // Update select options based on availability
    const options = ttsSelect.querySelectorAll('option');
    options.forEach(option => {
        const provider = data.providers.find(p => p.provider === option.value);
        if (provider && !provider.available) {
            option.disabled = true;
            option.textContent += ' (Unavailable)';
        }
    });

    // Add TTS provider change handler
    ttsSelect.addEventListener('change', function() {
        currentTTSProvider = this.value;
        updateTTSProviderHelp(this.value, data);
        // Load voices for the selected provider
        loadVoicesForProvider(this.value);
    });

    // Set initial help text
    updateTTSProviderHelp(ttsSelect.value, data);
}

function updateTTSProviderHelp(selectedProvider, data) {
    const ttsHelp = document.getElementById('tts-help');
    if (!ttsHelp) return;

    const provider = data.providers.find(p => p.provider === selectedProvider);
    if (provider) {
        const features = provider.features.slice(0, 2).join(', ');
        const languages = provider.languages.join(', ');
        ttsHelp.innerHTML = `
            <strong>${provider.name}:</strong> ${provider.description}<br>
            <small>Languages: ${languages} | Features: ${features}</small>
        `;
    }
}



function updateTTSStatus(provider, status) {
    const ttsStatus = document.getElementById('tts-status');
    const currentProvider = document.getElementById('current-tts-provider');
    const generationStatus = document.getElementById('tts-generation-status');

    if (ttsStatus && currentProvider && generationStatus) {
        ttsStatus.classList.remove('hidden');
        currentProvider.textContent = provider || 'Unknown';
        generationStatus.textContent = status || 'Processing...';
    }
}

function getTTSProviderName(provider) {
    const providerNames = {
        'minimax': 'MiniMax TTS',
        'elevenlabs': 'ElevenLabs TTS (V2)',
        'elevenlabs_v3': 'ElevenLabs V3 (Latest)',
        'auto': 'Smart Selection'
    };
    return providerNames[provider] || provider;
}

function displayAudioPlayer(audioUrl) {
    hideAllSections();
    const audioSection = document.getElementById('audio-section');
    const audioPlayer = document.getElementById('podcast-audio');
    const downloadBtn = document.getElementById('download-audio-btn');

    if (audioSection && audioPlayer && downloadBtn) {
        audioPlayer.src = audioUrl;
        downloadBtn.href = audioUrl;
        showSection(audioSection);
    }
}

// Dashboard Functions
function initializeDashboard() {
    console.log('🎛️ Initializing dashboard...');
    
    // Initialize session timer
    sessionStartTime = new Date();
    startSessionTimer();
    
    // Initialize dashboard displays
    updateDashboardStatus('Ready');
    updateSessionIdDisplay('None');
    updateActiveTTSDisplay('Smart Selection');
    updateMiniProgress(0, 'Ready to start');
    
    // Add dashboard event handlers
    setupDashboardEventHandlers();
    
    // Initialize workflow enhanced display
    updateWorkflowStepEnhanced(WORKFLOW_STEPS.INPUT);
    
    // Add initial session event
    addTimelineEvent('INIT', 'Session Started', 'Podcast generation workflow initiated', 'completed');
    
    console.log('✅ Dashboard initialized successfully');
}

function setupDashboardEventHandlers() {
    // Dashboard refresh button
    const dashboardRefresh = document.getElementById('dashboard-refresh');
    if (dashboardRefresh) {
        dashboardRefresh.addEventListener('click', () => {
            refreshDashboard();
        });
    }
    
    // Workflow reset button
    const workflowReset = document.getElementById('workflow-reset');
    if (workflowReset) {
        workflowReset.addEventListener('click', () => {
            resetWorkflow();
        });
    }
    
    // Timeline expand button
    const timelineExpand = document.getElementById('timeline-expand');
    if (timelineExpand) {
        timelineExpand.addEventListener('click', () => {
            toggleTimelineDetails();
        });
    }
    
    // Metrics refresh button
    const metricsRefresh = document.getElementById('metrics-refresh');
    if (metricsRefresh) {
        metricsRefresh.addEventListener('click', () => {
            refreshMetrics();
        });
    }
    
    // Step card click handlers for expanding details
    document.querySelectorAll('.workflow-step-card').forEach(card => {
        card.addEventListener('click', () => {
            toggleStepDetails(card);
        });
    });
}

function startSessionTimer() {
    sessionTimer = setInterval(() => {
        if (sessionStartTime) {
            const elapsed = new Date() - sessionStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            updateSessionTimer(timeString);
        }
    }, 1000);
}

function updateDashboardStatus(status) {
    const statusDisplay = document.getElementById('workflow-status-display');
    if (statusDisplay) {
        statusDisplay.textContent = status;
    }
}

function updateSessionIdDisplay(sessionId) {
    const sessionDisplay = document.getElementById('session-id-display');
    if (sessionDisplay) {
        sessionDisplay.textContent = sessionId || 'None';
    }
}

function updateActiveTTSDisplay(provider) {
    const ttsDisplay = document.getElementById('active-tts-display');
    if (ttsDisplay) {
        ttsDisplay.textContent = getTTSProviderName(provider) || 'Smart Selection';
    }
}

function updateSessionTimer(timeString) {
    const timerDisplay = document.getElementById('session-timer-display');
    if (timerDisplay) {
        timerDisplay.textContent = timeString;
    }
}

function updateMiniProgress(percentage, text) {
    const progressFill = document.getElementById('mini-progress-fill');
    const progressText = document.getElementById('mini-progress-text');
    
    if (progressFill) {
        progressFill.style.width = `${percentage}%`;
    }
    
    if (progressText) {
        progressText.textContent = text;
    }
}

function updateWorkflowStepEnhanced(step) {
    currentWorkflowStep = step;
    
    // Update step counter
    const stepCounter = document.getElementById('workflow-step-counter');
    if (stepCounter) {
        stepCounter.textContent = `Step ${step} of 3`;
    }
    
    // Update progress percentage
    const progressPercent = document.getElementById('workflow-progress-percent');
    const percentage = Math.round(((step - 1) / 2) * 100);
    if (progressPercent) {
        progressPercent.textContent = `${percentage}%`;
    }
    
    // Update step cards
    document.querySelectorAll('.workflow-step-card').forEach((card, index) => {
        const stepNumber = index + 1;
        card.classList.remove('active', 'completed');
        
        if (stepNumber < step) {
            card.classList.add('completed');
        } else if (stepNumber === step) {
            card.classList.add('active');
        }
    });
    
    // Update connector progress
    document.querySelectorAll('.connector-progress').forEach((connector, index) => {
        const connectorStep = index + 1;
        if (connectorStep < step) {
            connector.style.height = '100%';
        } else {
            connector.style.height = '0%';
        }
    });
    
    // Update timeline progress
    const timelineProgress = document.getElementById('timeline-progress');
    if (timelineProgress) {
        timelineProgress.style.width = `${percentage}%`;
    }
}

function addTimelineEvent(icon, title, description, status = 'completed') {
    const timelineEvents = document.querySelector('.timeline-events');
    if (!timelineEvents) return;
    
    const elapsed = sessionStartTime ? new Date() - sessionStartTime : 0;
    const minutes = Math.floor(elapsed / 60000);
    const seconds = Math.floor((elapsed % 60000) / 1000);
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    const eventElement = document.createElement('div');
    eventElement.className = 'timeline-event';
    eventElement.setAttribute('data-status', status);
    
    const iconMapping = {
        'INIT': '<circle cx="12" cy="12" r="10"/><polyline points="10 8 16 12 10 16"/>',
        'REQUEST': '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14 2 14 8 20 8"/>',
        'SEARCH': '<circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/>',
        'ANALYSIS': '<rect x="3" y="3" width="18" height="18" rx="2"/><rect x="7" y="7" width="3" height="9"/><rect x="14" y="7" width="3" height="5"/>',
        'SCRIPT': '<path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><polyline points="14 2 14 8 20 8"/><line x1="16" y1="13" x2="8" y2="13"/>',
        'COMPLETE': '<polyline points="20 6 9 17 4 12"/>',
        'REVIEW': '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/><circle cx="12" cy="12" r="3"/>',
        'AUDIO': '<path d="M3 18v-6a9 9 0 0 1 18 0v6"/><path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"/>',
        'SUCCESS': '<polyline points="20 6 9 17 4 12"/>',
        'REFRESH': '<polyline points="23 4 23 10 17 10"/><polyline points="1 20 1 14 7 14"/><path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>',
        'RESET': '<polyline points="1 4 1 10 7 10"/><path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"/>',
        'METRICS': '<rect x="3" y="3" width="18" height="18" rx="2"/><rect x="7" y="7" width="3" height="9"/><rect x="14" y="7" width="3" height="5"/>'
    };
    
    const iconSvg = iconMapping[icon] || iconMapping['COMPLETE'];
    
    eventElement.innerHTML = `
        <div class="event-time">${timeString}</div>
        <div class="event-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                ${iconSvg}
            </svg>
        </div>
        <div class="event-content">
            <div class="event-title">${title}</div>
            <div class="event-description">${description}</div>
        </div>
    `;
    
    timelineEvents.appendChild(eventElement);
    
    // Store in metrics
    dashboardMetrics.sessionEvents.push({
        time: timeString,
        icon,
        title,
        description,
        status,
        timestamp: new Date()
    });
    
    // Auto-scroll to latest event
    eventElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

function updateMetrics(type, value, trend) {
    dashboardMetrics[type] = value;
    
    const displays = {
        generationSpeed: 'generation-speed',
        researchSources: 'research-sources',
        scriptLength: 'script-length'
    };
    
    const elementId = displays[type];
    if (elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
        
        // Update trend if provided
        if (trend) {
            const trendElement = element?.closest('.metric-card')?.querySelector('.trend-text');
            if (trendElement) {
                trendElement.textContent = trend;
            }
        }
    }
}

function toggleStepDetails(stepCard) {
    const details = stepCard.querySelector('.step-details');
    if (details) {
        details.classList.toggle('hidden');
        
        // Add expand animation
        if (!details.classList.contains('hidden')) {
            details.style.maxHeight = '0px';
            details.style.overflow = 'hidden';
            
            requestAnimationFrame(() => {
                details.style.transition = 'max-height 0.3s ease-out';
                details.style.maxHeight = details.scrollHeight + 'px';
            });
        }
    }
}

function refreshDashboard() {
    console.log('🔄 Refreshing dashboard...');
    
    // Show refresh animation
    const refreshBtn = document.getElementById('dashboard-refresh');
    if (refreshBtn) {
        refreshBtn.style.animation = 'spin 1s linear infinite';
        setTimeout(() => {
            refreshBtn.style.animation = '';
        }, 1000);
    }
    
    // Update displays
    updateActiveTTSDisplay(currentTTSProvider);
    updateSessionIdDisplay(currentSessionId);
    
    // Add refresh event
    addTimelineEvent('REFRESH', 'Dashboard Refreshed', 'All metrics and status updated', 'completed');
}

function resetWorkflow() {
    console.log('🔄 Resetting workflow...');
    
    // Reset state
    currentSessionId = null;
    currentScript = null;
    selectedVoices = {};
    dashboardMetrics.sessionEvents = [];
    
    // Reset displays
    updateWorkflowStepEnhanced(WORKFLOW_STEPS.INPUT);
    updateDashboardStatus('Ready');
    updateSessionIdDisplay('None');
    updateMiniProgress(0, 'Ready to start');
    
    // Reset form
    const form = document.getElementById('podcast-form');
    if (form) form.reset();
    
    // Clear timeline events
    const timelineEvents = document.querySelector('.timeline-events');
    if (timelineEvents) {
        timelineEvents.innerHTML = '';
    }
    
    // Restart session timer
    sessionStartTime = new Date();
    
    // Hide all sections except input
    hideAllSections();
    const inputSection = document.getElementById('input-section');
    if (inputSection) {
        showSection(inputSection);
    }
    
    // Add reset event
    addTimelineEvent('RESET', 'Workflow Reset', 'Session restarted from beginning', 'completed');
    
    console.log('✅ Workflow reset complete');
}

function toggleTimelineDetails() {
    const metricsPanel = document.getElementById('metrics-panel');
    if (metricsPanel) {
        metricsPanel.classList.toggle('hidden');
        
        const expandBtn = document.getElementById('timeline-expand');
        if (expandBtn) {
            const isVisible = !metricsPanel.classList.contains('hidden');
            expandBtn.textContent = isVisible ? '📉 Hide Details' : '📈 Details';
        }
    }
}

function refreshMetrics() {
    console.log('📊 Refreshing metrics...');
    
    // Simulate metric updates with current data
    if (currentScript) {
        const wordCount = currentScript.dialogue
            .map(line => line.text.split(' ').length)
            .reduce((a, b) => a + b, 0);
        updateMetrics('scriptLength', wordCount, 'Generated');
    }
    
    // Add metrics refresh event
    addTimelineEvent('METRICS', 'Metrics Updated', 'Session analytics refreshed', 'completed');
}