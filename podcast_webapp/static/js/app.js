// Global variables
let currentSessionId = null;
let currentScript = null;
let availableVoices = [];
let selectedVoices = {};
let ttsProviders = [];
let currentTTSProvider = 'minimax';
let providerVoices = {}; // Store voices for each provider

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing app...');
    initializeApp();
});

function initializeApp() {
    // DOM Elements
    const form = document.getElementById('podcast-form');
    const progressSection = document.getElementById('progress-section');
    const reportSection = document.getElementById('report-section');
    const scriptSection = document.getElementById('script-section');
    const reviewSection = document.getElementById('review-section');
    const audioSection = document.getElementById('audio-section');
    const errorSection = document.getElementById('error-section');
    const scriptEditorModal = document.getElementById('script-editor-modal');

    console.log('Form element found:', !!form);
    console.log('Progress section found:', !!progressSection);

    // Load TTS provider information
    loadTTSProviders();



    // Form submission handler
    if (form) {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            console.log('Form submitted, preventDefault called');
            
            // Get form data
            const formData = new FormData(form);
            currentTTSProvider = formData.get('tts_provider') || 'minimax';
            const requestData = {
                topic: formData.get('topic'),
                script_style: formData.get('script_style'),
                num_speakers: parseInt(formData.get('num_speakers')),
                duration_target: parseInt(formData.get('duration_target')),
                language: formData.get('language'),
                tts_provider: currentTTSProvider
            };
            
            // Debug: Log the request data
            console.log('DEBUG: Frontend sending request data:', requestData);
            
            // Reset UI
            hideAllSections();
            showSection(progressSection);
            updateProgress('searching', 0);
            
            try {
                // Load voices for current TTS provider
                await loadVoicesForProvider(currentTTSProvider);
                
                // Make API request for script generation (Phase 1)
                const response = await fetch('/api/generate-script', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (data.status === 'error') {
                    throw new Error(data.error);
                }
                
                currentSessionId = data.request_id;
                
                // If already completed (sync mode), display results immediately
                if (data.status === 'script_ready' || data.status === 'completed') {
                    currentScript = data.script;
                    displayScriptForReview(data);
                } else {
                    // Start polling for status updates
                    setTimeout(pollStatusPhase1, 1000);
                }
                
            } catch (error) {
                showError(error.message);
            }
        });
    } else {
        console.error('Form element not found!');
    }

    // Script editor functionality
    const editScriptBtn = document.getElementById('edit-script-btn');
    const closeBtn = document.querySelector('.close');
    const cancelEditBtn = document.getElementById('cancel-edit-btn');
    const saveScriptBtn = document.getElementById('save-script-btn');
    const approveScriptBtn = document.getElementById('approve-script-btn');
    const editBeforeApproveBtn = document.getElementById('edit-before-approve-btn');
    const retryBtn = document.getElementById('retry-btn');

    if (editScriptBtn) {
        editScriptBtn.addEventListener('click', () => {
            if (!currentScript) return;
            
            const editor = document.getElementById('script-editor');
            editor.value = JSON.stringify(currentScript, null, 2);
            scriptEditorModal.classList.remove('hidden');
        });
    }

    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            scriptEditorModal.classList.add('hidden');
        });
    }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            scriptEditorModal.classList.add('hidden');
        });
    }

    if (saveScriptBtn) {
        saveScriptBtn.addEventListener('click', async () => {
            const editor = document.getElementById('script-editor');
            
            try {
                // Validate JSON
                const updatedScript = JSON.parse(editor.value);
                
                // Send update request
                const formData = new FormData();
                formData.append('session_id', currentSessionId);
                formData.append('updated_script', editor.value);
                
                const response = await fetch('/api/update-script', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentScript = data.script;
                    displayScript(data.script);
                    displayAudio(data.audio_url);
                    scriptEditorModal.classList.add('hidden');
                } else {
                    throw new Error('Failed to update script');
                }
                
            } catch (error) {
                alert('Error updating script: ' + error.message);
            }
        });
    }

    // Approve script and start audio generation
    if (approveScriptBtn) {
        approveScriptBtn.addEventListener('click', async () => {
            // Validate that all speakers have voices selected
            const speakers = [...new Set(currentScript.dialogue.map(line => line.speaker_name))];
            const missingVoices = speakers.filter(speaker => !selectedVoices[speaker] || !selectedVoices[speaker].voice_id);
            
            if (missingVoices.length > 0) {
                alert(`Please select voices for: ${missingVoices.join(', ')}`);
                return;
            }
            
            try {
                // Show TTS status
                updateTTSStatus(getTTSProviderName(currentTTSProvider), 'Starting audio generation...');

                // Prepare form data
                const formData = new FormData();
                formData.append('session_id', currentSessionId);
                formData.append('approved_script', JSON.stringify(currentScript));
                formData.append('voice_selections', JSON.stringify(selectedVoices));
                formData.append('tts_provider', currentTTSProvider);

                // Submit to new audio generation endpoint
                const response = await fetch('/api/generate-audio', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // Update TTS status with results
                    const providerUsed = data.provider_used || currentTTSProvider;
                    const fallbackInfo = data.fallback_used ? ' (fallback used)' : '';
                    updateTTSStatus(getTTSProviderName(providerUsed), `Completed in ${data.generation_time?.toFixed(1)}s${fallbackInfo}`);

                    // Show audio player
                    displayAudioPlayer(data.audio_url);

                } else {
                    // Handle audio generation failure
                    const errorMsg = data.error || 'Audio generation failed';
                    updateTTSStatus(getTTSProviderName(currentTTSProvider), `Failed: ${errorMsg}`);

                    // Try fallback or show error
                    if (data.fallback_used) {
                        throw new Error(`Audio generation failed even with fallback: ${errorMsg}`);
                    } else {
                        // Start polling for potential fallback
                        setTimeout(pollStatusPhase2, 1000);
                    }
                }
                
            } catch (error) {
                showError('Error approving script: ' + error.message);
            }
        });
    }

    // Edit script before approval
    if (editBeforeApproveBtn) {
        editBeforeApproveBtn.addEventListener('click', () => {
            if (!currentScript) return;
            
            const editor = document.getElementById('script-editor');
            editor.value = JSON.stringify(currentScript, null, 2);
            scriptEditorModal.classList.remove('hidden');
        });
    }

    if (retryBtn) {
        retryBtn.addEventListener('click', () => {
            hideAllSections();
            showSection(document.querySelector('#input-section'));
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === scriptEditorModal) {
            scriptEditorModal.classList.add('hidden');
        }
    });

    // Utility functions
    function hideAllSections() {
        const sections = [progressSection, reportSection, scriptSection, reviewSection, audioSection, errorSection];
        sections.forEach(section => section.classList.add('hidden'));
    }

    function showSection(section) {
        section.classList.remove('hidden');
    }

    // Error handling
    function showError(message) {
        hideAllSections();
        document.getElementById('error-message').textContent = message;
        showSection(errorSection);
    }
}

// Poll for script generation status (Phase 1)
async function pollStatusPhase1() {
    if (!currentSessionId) return;
    
    try {
        const response = await fetch(`/api/status/${currentSessionId}`);
        const data = await response.json();
        
        updateProgress(data.status, getProgressPercentage(data.status));
        
        if (data.status === 'script_ready') {
            // Script is ready for review, get the results
            const resultResponse = await fetch(`/api/session/${currentSessionId}`);
            const result = await resultResponse.json();
            
            if (result.error) {
                showError(result.error);
            } else {
                currentScript = result.script;
                displayScriptForReview(result);
            }
            
        } else if (data.status === 'error') {
            showError(data.error || 'Script generation failed');
        } else {
            // Continue polling
            setTimeout(pollStatusPhase1, 1000);
        }
    } catch (error) {
        showError(error.message);
    }
}

// Poll for audio generation status (Phase 2)
async function pollStatusPhase2() {
    if (!currentSessionId) return;
    
    try {
        const response = await fetch(`/api/status/${currentSessionId}`);
        const data = await response.json();
        
        updateProgress(data.status, getProgressPercentage(data.status));
        
        if (data.status === 'completed') {
            // Audio generation complete, get the full results
            const resultResponse = await fetch(`/api/session/${currentSessionId}`);
            const result = await resultResponse.json();
            
            if (result.error) {
                showError(result.error);
            } else {
                displayFinalResults(result);
            }
            
        } else if (data.status === 'error') {
            showError(data.error || 'Audio generation failed');
        } else {
            // Continue polling
            setTimeout(pollStatusPhase2, 1000);
        }
    } catch (error) {
        showError(error.message);
    }
}

// Update progress display
function updateProgress(status, percentage) {
    const progressFill = document.querySelector('.progress-fill');
    const progressText = document.querySelector('.progress-text');
    const steps = document.querySelectorAll('.step');
    
    progressFill.style.width = `${percentage}%`;
    
    const statusTexts = {
        'searching': 'Searching the web for information...',
        'generating_report': 'Generating research report...',
        'generating_script': 'Writing podcast script...',
        'script_ready': 'Script ready for review!',
        'synthesizing_audio': 'Synthesizing audio with multiple voices...',
        'completed': 'Podcast generation complete!'
    };
    
    progressText.textContent = statusTexts[status] || 'Processing...';
    
    // Update step indicators
    const stepOrder = ['searching', 'generating_report', 'generating_script', 'script_ready', 'synthesizing_audio'];
    const currentIndex = stepOrder.indexOf(status);
    
    steps.forEach((step, index) => {
        step.classList.remove('active', 'completed');
        if (index < currentIndex) {
            step.classList.add('completed');
        } else if (index === currentIndex) {
            step.classList.add('active');
        }
    });
}

// Get progress percentage based on status
function getProgressPercentage(status) {
    const percentages = {
        'searching': 20,
        'generating_report': 40,
        'generating_script': 60,
        'script_ready': 65,
        'synthesizing_audio': 85,
        'completed': 100
    };
    return percentages[status] || 0;
}

// Display generation results
function displayResults(data) {
    const progressSection = document.getElementById('progress-section');
    const reportSection = document.getElementById('report-section');
    const scriptSection = document.getElementById('script-section');
    const audioSection = document.getElementById('audio-section');
    
    hideAllSections();
    
    // Display report
    if (data.report) {
        displayReport(data.report);
        showSection(reportSection);
    }
    
    // Display script
    if (data.script) {
        displayScript(data.script);
        showSection(scriptSection);
    }
    
    // Display audio player
    if (data.audio_url) {
        displayAudio(data.audio_url);
        showSection(audioSection);
    }
}

// Display research report
function displayReport(report) {
    const reportContent = document.getElementById('report-content');
    
    let html = `
        <h3>Summary</h3>
        <div class="summary">${marked.parse(report.summary)}</div>
        
        <h3>Key Findings</h3>
        <ul>
            ${report.key_findings.map(finding => `<li>${finding}</li>`).join('')}
        </ul>
        
        <h3>Sources</h3>
        <ul class="sources">
            ${report.sources.map(source => `
                <li>
                    <strong>${source.title}</strong><br>
                    ${source.snippet}<br>
                    <small>Relevance: ${Math.round(source.relevance_score * 100)}%</small>
                </li>
            `).join('')}
        </ul>
    `;
    
    reportContent.innerHTML = html;
}

// Display podcast script
function displayScript(script) {
    document.getElementById('script-title').textContent = script.title;
    document.getElementById('script-description').textContent = script.description;
    
    const dialogueContainer = document.getElementById('script-dialogue');
    
    let html = script.dialogue.map(line => `
        <div class="dialogue-line">
            <div class="speaker">${line.speaker_name} (${line.role}) 
                ${line.emotion ? `<span class="emotion-tag">${getEmotionEmoji(line.emotion)} ${line.emotion}</span>` : ''}
            </div>
            <div class="text">${line.text}</div>
        </div>
    `).join('');
    
    dialogueContainer.innerHTML = html;
}

// Display audio player
function displayAudio(audioUrl) {
    const audioElement = document.getElementById('podcast-audio');
    const downloadBtn = document.getElementById('download-audio-btn');
    
    audioElement.src = audioUrl;
    downloadBtn.href = audioUrl;
    
    // Extract filename from URL for proper download
    const urlPath = audioUrl.split('/');
    const filename = urlPath[urlPath.length - 1];
    downloadBtn.download = filename || 'podcast.mp3';
}

// Get MiniMax voices
function getMinimaxVoices() {
    return [
        // Working Male Voices (confirmed working with MiniMax API)
        { voice_id: "audiobook_male_1", voice_name: "男性有声书1", gender: "male", category: "audiobook" },
        { voice_id: "audiobook_male_2", voice_name: "男性有声书2", gender: "male", category: "audiobook" },
        { voice_id: "presenter_male", voice_name: "男性主持人", gender: "male", category: "professional" },

        // Working Female Voices (confirmed working with MiniMax API)
        { voice_id: "audiobook_female_1", voice_name: "女性有声书1", gender: "female", category: "audiobook" },
        { voice_id: "audiobook_female_2", voice_name: "女性有声书2", gender: "female", category: "audiobook" },
        { voice_id: "presenter_female", voice_name: "女性主持人", gender: "female", category: "professional" },

        // Legacy voices that might work
        { voice_id: "Chinese (Mandarin)_Reliable_Executive", voice_name: "可靠的主管", gender: "male", category: "legacy" },
        { voice_id: "Chinese (Mandarin)_Wise_Women", voice_name: "智慧女性", gender: "female", category: "legacy" },
        { voice_id: "Chinese (Mandarin)_Narrator_Male", voice_name: "男性旁白", gender: "male", category: "legacy" },
        { voice_id: "Chinese (Mandarin)_Narrator_Female", voice_name: "女性旁白", gender: "female", category: "legacy" },

        // Try the Chinese voice IDs from your list
        { voice_id: "male-qn-qingse", voice_name: "青涩青年音色", gender: "male", category: "youth" },
        { voice_id: "male-qn-jingying", voice_name: "精英青年音色", gender: "male", category: "youth" },
        { voice_id: "male-qn-badao", voice_name: "霸道青年音色", gender: "male", category: "youth" },
        { voice_id: "male-qn-daxuesheng", voice_name: "青年大学生音色", gender: "male", category: "youth" },
        { voice_id: "female-shaonv", voice_name: "少女音色", gender: "female", category: "youth" },
        { voice_id: "female-yujie", voice_name: "御姐音色", gender: "female", category: "mature" },
        { voice_id: "female-chengshu", voice_name: "成熟女性音色", gender: "female", category: "mature" },
        { voice_id: "female-tianmei", voice_name: "甜美女性音色", gender: "female", category: "sweet" },

        // Character and roleplay voices
        { voice_id: "clever_boy", voice_name: "聪明男童", gender: "male", category: "child" },
        { voice_id: "cute_boy", voice_name: "可爱男童", gender: "male", category: "child" },
        { voice_id: "lovely_girl", voice_name: "萌萌女童", gender: "female", category: "child" },
        { voice_id: "bingjiao_didi", voice_name: "病娇弟弟", gender: "male", category: "roleplay" },
        { voice_id: "junlang_nanyou", voice_name: "俊朗男友", gender: "male", category: "roleplay" },
        { voice_id: "tianxin_xiaoling", voice_name: "甜心小玲", gender: "female", category: "roleplay" },
        { voice_id: "qiaopi_mengmei", voice_name: "俏皮萌妹", gender: "female", category: "roleplay" }
    ];
}

// Initialize fixed voice list with working MiniMax voice IDs
function initializeFixedVoices() {
    availableVoices = getMinimaxVoices();
    console.log('Initialized fixed voice list with', availableVoices.length, 'voices');
}

// Update existing voice selections when voices change
function updateVoiceSelections() {
    // Update all voice selector dropdowns if they exist
    document.querySelectorAll('.voice-selector').forEach(select => {
        const currentValue = select.value;
        const speaker = select.dataset.speaker;

        // Regenerate options
        select.innerHTML = `
            <option value="">Choose a voice...</option>
            ${generateVoiceOptionsGrouped()}
        `;

        // Try to restore previous selection if the voice still exists
        if (currentValue && availableVoices.find(v => v.voice_id === currentValue)) {
            select.value = currentValue;
        } else {
            // Clear the selection if the voice is no longer available
            select.value = "";
            if (selectedVoices[speaker]) {
                delete selectedVoices[speaker];
            }
        }
    });
}

// Generate grouped voice options for select dropdown
function generateVoiceOptionsGrouped() {
    const categories = {
        'professional': '专业主播',
        'audiobook': '有声书',
        'youth': '青年音色',
        'mature': '成熟音色',
        'sweet': '甜美音色',
        'beta': 'Beta版本',
        'child': '童声',
        'roleplay': '角色扮演',
        'cartoon': '卡通角色',
        'english': 'English Voices',
        'elevenlabs': 'ElevenLabs Voices',
        'legacy': '经典音色'
    };
    
    let html = '';
    
    Object.keys(categories).forEach(category => {
        const categoryVoices = availableVoices.filter(voice => voice.category === category);
        if (categoryVoices.length > 0) {
            html += `<optgroup label="${categories[category]}">`;
            categoryVoices.forEach(voice => {
                html += `<option value="${voice.voice_id}">${voice.voice_name} (${voice.gender})</option>`;
            });
            html += `</optgroup>`;
        }
    });
    
    return html;
}

// Get emotion emoji for display
function getEmotionEmoji(emotion) {
    const emotionEmojis = {
        'happy': '😊',
        'sad': '😢', 
        'angry': '😠',
        'fearful': '😨',
        'disgusted': '🤢',
        'surprised': '😲',
        'neutral': '😐'
    };
    return emotionEmojis[emotion] || '😐';
}

// Display script for review (Phase 1 complete)
function displayScriptForReview(data) {
    const progressSection = document.getElementById('progress-section');
    const reportSection = document.getElementById('report-section');
    const scriptSection = document.getElementById('script-section');
    const reviewSection = document.getElementById('review-section');
    
    hideAllSections();
    
    // Display report
    if (data.report) {
        displayReport(data.report);
        showSection(reportSection);
    }
    
    // Display script
    if (data.script) {
        displayScript(data.script);
        showSection(scriptSection);
        
        // Show voice selection interface
        displayVoiceSelection(data.script);
        showSection(reviewSection);
    }
}

// Display voice selection interface
function displayVoiceSelection(script) {
    const container = document.getElementById('voice-selection-container');
    
    // Get unique speakers from script
    const speakers = [...new Set(script.dialogue.map(line => line.speaker_name))];
    
    let html = '';
    speakers.forEach(speaker => {
        const speakerLines = script.dialogue.filter(line => line.speaker_name === speaker);
        const firstLine = speakerLines[0];
        
        html += `
            <div class="voice-selection-item">
                <h4>🎭 ${speaker} (${firstLine.role})</h4>
                <p class="sample-text">"${firstLine.text.substring(0, 100)}..."</p>
                
                <div class="voice-controls">
                    <label for="voice-${speaker}">Select Voice:</label>
                    <select id="voice-${speaker}" class="voice-selector" data-speaker="${speaker}">
                        <option value="">Choose a voice...</option>
                        ${generateVoiceOptionsGrouped()}
                    </select>
                    
                    <button class="btn btn-small preview-voice-btn" data-speaker="${speaker}">
                        🔊 Preview
                    </button>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
    
    // Add event listeners for voice selection
    setupVoiceSelectionListeners();
}

// Setup event listeners for voice selection
function setupVoiceSelectionListeners() {
    // Voice selector change events
    document.querySelectorAll('.voice-selector').forEach(select => {
        select.addEventListener('change', (e) => {
            const speaker = e.target.dataset.speaker;
            const voiceId = e.target.value;
            
            selectedVoices[speaker] = {
                voice_id: voiceId,
                emotion: "neutral"  // Default emotion, will be overridden by LLM analysis
            };
        });
    });
    
    // Preview voice buttons
    document.querySelectorAll('.preview-voice-btn').forEach(btn => {
        btn.addEventListener('click', async (e) => {
            const speaker = e.target.dataset.speaker;
            await previewVoice(speaker);
        });
    });
}

// Preview voice function
async function previewVoice(speaker) {
    const voiceSelect = document.getElementById(`voice-${speaker}`);
    
    if (!voiceSelect.value) {
        alert('Please select a voice first');
        return;
    }
    
    const sampleText = currentScript.dialogue.find(line => line.speaker_name === speaker)?.text.substring(0, 50) || "Hello, this is a voice preview.";
    
    try {
        const response = await fetch('/api/test-tts', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `text=${encodeURIComponent(sampleText)}`
        });
        
        const data = await response.json();
        if (data.success && data.audio_path) {
            // Play preview audio
            const audio = new Audio(data.audio_path);
            audio.play();
        } else {
            console.log('Voice preview not available');
        }
    } catch (error) {
        console.error('Preview failed:', error);
    }
}

// Display final results (Phase 2 complete)
function displayFinalResults(data) {
    const progressSection = document.getElementById('progress-section');
    const reportSection = document.getElementById('report-section');
    const scriptSection = document.getElementById('script-section');
    const audioSection = document.getElementById('audio-section');
    
    hideAllSections();
    
    // Display report
    if (data.report) {
        displayReport(data.report);
        showSection(reportSection);
    }
    
    // Display script
    if (data.script) {
        displayScript(data.script);
        showSection(scriptSection);
    }
    
    // Display audio player
    if (data.audio_url) {
        displayAudio(data.audio_url);
        showSection(audioSection);
    }
}

// Utility functions for sections
function hideAllSections() {
    const sections = ['progress-section', 'report-section', 'script-section', 'review-section', 'audio-section', 'error-section'];
    sections.forEach(id => {
        const section = document.getElementById(id);
        if (section) section.classList.add('hidden');
    });
}

function showSection(section) {
    if (section) section.classList.remove('hidden');
}

function showError(message) {
    hideAllSections();
    const errorSection = document.getElementById('error-section');
    const errorMessage = document.getElementById('error-message');
    if (errorMessage) errorMessage.textContent = message;
    showSection(errorSection);
}

// TTS Provider Functions
async function loadTTSProviders() {
    try {
        const response = await fetch('/api/tts-providers');
        const data = await response.json();

        if (data.error) {
            console.error('Failed to load TTS providers:', data.error);
            return;
        }

        ttsProviders = data.providers;
        updateTTSProviderUI(data);

        // Load voices for the default provider
        await loadVoicesForProvider(currentTTSProvider);

    } catch (error) {
        console.error('Error loading TTS providers:', error);
    }
}

// Load voices for a specific TTS provider
async function loadVoicesForProvider(provider) {
    try {
        // Check if we already have voices for this provider
        if (providerVoices[provider]) {
            availableVoices = providerVoices[provider];
            updateVoiceSelections();
            return;
        }

        let voices = [];

        if (provider === 'minimax') {
            // Use fixed MiniMax voices
            voices = getMinimaxVoices();
        } else if (provider === 'elevenlabs') {
            // Load ElevenLabs voices from API
            const response = await fetch(`/api/voices/elevenlabs`);
            if (response.ok) {
                const data = await response.json();
                voices = data.voices.map(voice => ({
                    voice_id: voice.voice_id,
                    voice_name: voice.name,
                    gender: voice.gender,
                    category: 'elevenlabs',
                    language: voice.language,
                    description: voice.description
                }));
            } else {
                console.error('Failed to load ElevenLabs voices');
                voices = getMinimaxVoices(); // Fallback to MiniMax
            }
        } else if (provider === 'auto') {
            // For auto selection, combine all available voices
            voices = getMinimaxVoices();
            try {
                const response = await fetch(`/api/voices/elevenlabs`);
                if (response.ok) {
                    const data = await response.json();
                    const elevenLabsVoices = data.voices.map(voice => ({
                        voice_id: voice.voice_id,
                        voice_name: voice.name,
                        gender: voice.gender,
                        category: 'elevenlabs',
                        language: voice.language,
                        description: voice.description
                    }));
                    voices = voices.concat(elevenLabsVoices);
                }
            } catch (error) {
                console.warn('Could not load ElevenLabs voices for auto mode');
            }
        }

        // Cache the voices for this provider
        providerVoices[provider] = voices;
        availableVoices = voices;

        // Update any existing voice selections
        updateVoiceSelections();

        console.log(`Loaded ${voices.length} voices for ${provider}`);

    } catch (error) {
        console.error(`Error loading voices for ${provider}:`, error);
        // Fallback to MiniMax voices
        availableVoices = getMinimaxVoices();
        updateVoiceSelections();
    }
}

function updateTTSProviderUI(data) {
    const ttsSelect = document.getElementById('tts-provider');
    const ttsHelp = document.getElementById('tts-help');

    if (!ttsSelect || !ttsHelp) return;

    // Update help text based on provider availability
    const availableProviders = data.providers.filter(p => p.available);
    const providerNames = availableProviders.map(p => p.name).join(', ');
    ttsHelp.textContent = `Available: ${providerNames}`;

    // Update select options based on availability
    const options = ttsSelect.querySelectorAll('option');
    options.forEach(option => {
        const provider = data.providers.find(p => p.provider === option.value);
        if (provider && !provider.available) {
            option.disabled = true;
            option.textContent += ' (Unavailable)';
        }
    });

    // Add TTS provider change handler
    ttsSelect.addEventListener('change', function() {
        currentTTSProvider = this.value;
        updateTTSProviderHelp(this.value, data);
        // Load voices for the selected provider
        loadVoicesForProvider(this.value);
    });

    // Set initial help text
    updateTTSProviderHelp(ttsSelect.value, data);
}

function updateTTSProviderHelp(selectedProvider, data) {
    const ttsHelp = document.getElementById('tts-help');
    if (!ttsHelp) return;

    const provider = data.providers.find(p => p.provider === selectedProvider);
    if (provider) {
        const features = provider.features.slice(0, 2).join(', ');
        const languages = provider.languages.join(', ');
        ttsHelp.innerHTML = `
            <strong>${provider.name}:</strong> ${provider.description}<br>
            <small>Languages: ${languages} | Features: ${features}</small>
        `;
    }
}



function updateTTSStatus(provider, status) {
    const ttsStatus = document.getElementById('tts-status');
    const currentProvider = document.getElementById('current-tts-provider');
    const generationStatus = document.getElementById('tts-generation-status');

    if (ttsStatus && currentProvider && generationStatus) {
        ttsStatus.classList.remove('hidden');
        currentProvider.textContent = provider || 'Unknown';
        generationStatus.textContent = status || 'Processing...';
    }
}

function getTTSProviderName(provider) {
    const providerNames = {
        'minimax': 'MiniMax TTS',
        'elevenlabs': 'ElevenLabs TTS',
        'auto': 'Smart Selection'
    };
    return providerNames[provider] || provider;
}

function displayAudioPlayer(audioUrl) {
    hideAllSections();
    const audioSection = document.getElementById('audio-section');
    const audioPlayer = document.getElementById('podcast-audio');
    const downloadBtn = document.getElementById('download-audio-btn');

    if (audioSection && audioPlayer && downloadBtn) {
        audioPlayer.src = audioUrl;
        downloadBtn.href = audioUrl;
        showSection(audioSection);
    }
}