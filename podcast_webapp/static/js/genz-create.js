/**
 * TrendCast AI - Content Creation Interface
 * Interactive creation workflow for Gen Z users
 */

class ContentCreator {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.projectData = {
            topic: '',
            content: '',
            research: [],
            script: '',
            audio: null
        };
        this.isProcessing = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeStep();
        this.setupAutoSave();
        this.handleURLParams();
    }

    handleURLParams() {
        // Check if topic was passed from analysis
        const urlParams = new URLSearchParams(window.location.search);
        const topic = urlParams.get('topic');

        if (topic) {
            // Pre-fill the topic input
            const topicInput = document.querySelector('#topic-input');
            if (topicInput) {
                topicInput.value = decodeURIComponent(topic);
                this.projectData.topic = decodeURIComponent(topic);

                // Show notification
                this.showNotification(`🎯 已为您预设话题: ${decodeURIComponent(topic)}`, 'success');

                // Auto-trigger research if topic is provided
                setTimeout(() => {
                    this.startResearch();
                }, 1000);
            }
        }
    }

    bindEvents() {
        // Tab switching
        this.bindTabSwitching();
        
        // Input handling
        this.bindInputHandling();
        
        // Suggestion interactions
        this.bindSuggestionActions();
        
        // Navigation
        this.bindNavigation();
        
        // Quick actions
        this.bindQuickActions();
        
        // Floating buttons
        this.bindFloatingButtons();
    }

    bindTabSwitching() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                
                // Remove active class from all tabs
                tabButtons.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab
                btn.classList.add('active');
                document.getElementById(`${tabId}-tab`).classList.add('active');
                
                // Add animation
                btn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    btn.style.transform = 'scale(1)';
                }, 150);
            });
        });
    }

    bindInputHandling() {
        // Text input
        const contentInput = document.querySelector('.content-input');
        if (contentInput) {
            contentInput.addEventListener('input', (e) => {
                this.projectData.content = e.target.value;
                this.updateProgress();
                this.autoSave();
            });

            // Add typing animation
            contentInput.addEventListener('focus', () => {
                this.addTypingIndicator();
            });

            contentInput.addEventListener('blur', () => {
                this.removeTypingIndicator();
            });
        }

        // File upload
        const fileInput = document.querySelector('.file-input');
        const uploadZone = document.querySelector('.upload-zone');
        
        if (uploadZone && fileInput) {
            uploadZone.addEventListener('click', () => {
                fileInput.click();
            });

            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.style.borderColor = 'var(--neon-pink)';
                uploadZone.style.background = 'rgba(255, 0, 110, 0.1)';
            });

            uploadZone.addEventListener('dragleave', () => {
                uploadZone.style.borderColor = 'var(--bg-tertiary)';
                uploadZone.style.background = 'transparent';
            });

            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                const files = e.dataTransfer.files;
                this.handleFileUpload(files);
            });

            fileInput.addEventListener('change', (e) => {
                this.handleFileUpload(e.target.files);
            });
        }

        // URL input
        const urlInput = document.querySelector('.url-input');
        const fetchBtn = document.querySelector('.fetch-btn');
        
        if (urlInput && fetchBtn) {
            fetchBtn.addEventListener('click', () => {
                this.fetchUrlContent(urlInput.value);
            });

            urlInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.fetchUrlContent(urlInput.value);
                }
            });
        }

        // Action buttons
        const analyzeBtn = document.querySelector('.action-btn.primary');
        const enhanceBtn = document.querySelector('.action-btn.secondary');
        
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.analyzeContent();
            });
        }

        if (enhanceBtn) {
            enhanceBtn.addEventListener('click', () => {
                this.enhanceContent();
            });
        }
    }

    bindSuggestionActions() {
        // Use suggestion buttons
        const useSuggestionBtns = document.querySelectorAll('.use-suggestion-btn');
        useSuggestionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const suggestionItem = btn.closest('.suggestion-item');
                this.useSuggestion(suggestionItem);
            });
        });

        // Suggestion item clicks
        const suggestionItems = document.querySelectorAll('.suggestion-item');
        suggestionItems.forEach(item => {
            item.addEventListener('click', () => {
                this.previewSuggestion(item);
            });
        });

        // Refresh suggestions
        const refreshBtn = document.querySelector('.refresh-suggestions');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshSuggestions();
            });
        }

        // Suggestion chips
        const chips = document.querySelectorAll('.chip');
        chips.forEach(chip => {
            chip.addEventListener('click', () => {
                this.quickFetch(chip.textContent);
            });
        });
    }

    bindNavigation() {
        const nextBtn = document.querySelector('.next-btn');
        const prevBtn = document.querySelector('.prev-btn');

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                this.nextStep();
            });
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                this.prevStep();
            });
        }

        // Save button
        const saveBtn = document.querySelector('.save-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveProject();
            });
        }
    }

    bindQuickActions() {
        const quickActionBtns = document.querySelectorAll('.quick-action-btn');
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.dataset.action;
                this.handleQuickAction(action);
            });
        });
    }

    bindFloatingButtons() {
        const helpBtn = document.querySelector('.help-btn');
        const feedbackBtn = document.querySelector('.feedback-btn');

        if (helpBtn) {
            helpBtn.addEventListener('click', () => {
                this.showHelp();
            });
        }

        if (feedbackBtn) {
            feedbackBtn.addEventListener('click', () => {
                this.showFeedback();
            });
        }
    }

    // Content handling methods
    handleFileUpload(files) {
        if (files.length === 0) return;

        this.showNotification('📁 Processing files...', 'info');
        
        Array.from(files).forEach(file => {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.projectData.content += `\n\n[File: ${file.name}]\n${e.target.result}`;
                this.updateContentDisplay();
                this.showNotification(`✅ ${file.name} uploaded successfully!`, 'success');
            };
            reader.readAsText(file);
        });

        // Reset upload zone
        const uploadZone = document.querySelector('.upload-zone');
        uploadZone.style.borderColor = 'var(--bg-tertiary)';
        uploadZone.style.background = 'transparent';
    }

    async fetchUrlContent(url) {
        if (!url) {
            this.showNotification('⚠️ Please enter a valid URL', 'warning');
            return;
        }

        const fetchBtn = document.querySelector('.fetch-btn');
        const originalText = fetchBtn.innerHTML;
        fetchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        fetchBtn.disabled = true;

        try {
            this.showNotification('🔍 Fetching content...', 'info');
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const mockContent = `Content from ${url}:\n\nThis is simulated content that would be extracted from the provided URL. In a real implementation, this would use web scraping or content extraction APIs.`;
            
            this.projectData.content += `\n\n[URL: ${url}]\n${mockContent}`;
            this.updateContentDisplay();
            this.showNotification('✅ Content fetched successfully!', 'success');
            
        } catch (error) {
            this.showNotification('❌ Failed to fetch content', 'error');
        } finally {
            fetchBtn.innerHTML = originalText;
            fetchBtn.disabled = false;
        }
    }

    async analyzeContent() {
        if (!this.projectData.content.trim()) {
            this.showNotification('⚠️ Please add some content first', 'warning');
            return;
        }

        const analyzeBtn = document.querySelector('.action-btn.primary');
        const originalText = analyzeBtn.innerHTML;
        analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';
        analyzeBtn.disabled = true;

        try {
            this.showNotification('🧠 AI is analyzing your content...', 'info');
            
            // Simulate AI analysis
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            this.showNotification('✅ Analysis complete! Moving to research phase...', 'success');
            
            // Auto-advance to next step
            setTimeout(() => {
                this.nextStep();
            }, 1500);
            
        } catch (error) {
            this.showNotification('❌ Analysis failed', 'error');
        } finally {
            analyzeBtn.innerHTML = originalText;
            analyzeBtn.disabled = false;
        }
    }

    async enhanceContent() {
        const contentInput = document.querySelector('.content-input');
        if (!contentInput.value.trim()) {
            this.showNotification('⚠️ Please add some content first', 'warning');
            return;
        }

        const enhanceBtn = document.querySelector('.action-btn.secondary');
        const originalText = enhanceBtn.innerHTML;
        enhanceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enhancing...';
        enhanceBtn.disabled = true;

        try {
            this.showNotification('✨ AI is enhancing your content...', 'info');
            
            // Simulate AI enhancement
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const enhanced = contentInput.value + '\n\n[AI Enhanced]\nThis content has been enhanced with additional context, trending keywords, and improved structure for better engagement.';
            contentInput.value = enhanced;
            this.projectData.content = enhanced;
            
            this.showNotification('✅ Content enhanced successfully!', 'success');
            
        } catch (error) {
            this.showNotification('❌ Enhancement failed', 'error');
        } finally {
            enhanceBtn.innerHTML = originalText;
            enhanceBtn.disabled = false;
        }
    }

    useSuggestion(suggestionItem) {
        const topic = suggestionItem.dataset.topic;
        const title = suggestionItem.querySelector('h4').textContent;
        const description = suggestionItem.querySelector('.suggestion-desc').textContent;

        this.projectData.topic = topic;
        this.projectData.content = `Topic: ${title}\n\nDescription: ${description}\n\nTrend Analysis: This topic is currently trending across multiple platforms with high engagement rates.`;

        this.updateContentDisplay();
        this.showNotification(`🎯 Using "${title}" as your topic!`, 'success');

        // Add visual feedback
        suggestionItem.style.transform = 'scale(0.95)';
        suggestionItem.style.background = 'var(--creation-success)';
        setTimeout(() => {
            suggestionItem.style.transform = 'scale(1)';
            suggestionItem.style.background = '';
        }, 300);
    }

    previewSuggestion(suggestionItem) {
        const title = suggestionItem.querySelector('h4').textContent;
        this.showNotification(`👀 Previewing "${title}"...`, 'info');
        
        // Add preview highlight
        suggestionItem.style.boxShadow = '0 0 30px rgba(255, 0, 110, 0.5)';
        setTimeout(() => {
            suggestionItem.style.boxShadow = '';
        }, 2000);
    }

    refreshSuggestions() {
        const refreshBtn = document.querySelector('.refresh-suggestions');
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshBtn.disabled = true;

        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
            this.showNotification('🔄 Suggestions refreshed!', 'success');
        }, 1500);
    }

    quickFetch(platform) {
        this.showNotification(`🔍 Fetching ${platform} trends...`, 'info');
        
        // Simulate platform-specific content
        const mockContent = {
            'TikTok Trends': 'Latest viral TikTok trends including dance challenges, comedy skits, and educational content...',
            'Twitter Viral': 'Trending hashtags and viral tweets from the past 24 hours...',
            'Reddit Hot': 'Top posts from popular subreddits including memes, discussions, and breaking news...',
            'YouTube Trending': 'Most popular YouTube videos and emerging creators...'
        };

        setTimeout(() => {
            const content = mockContent[platform] || 'Trending content from various platforms...';
            this.projectData.content += `\n\n[${platform}]\n${content}`;
            this.updateContentDisplay();
            this.showNotification(`✅ ${platform} content loaded!`, 'success');
        }, 1000);
    }

    // Navigation methods
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.updateStepDisplay();
            this.showNotification(`📍 Moving to step ${this.currentStep}`, 'info');
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepDisplay();
        }
    }

    updateStepDisplay() {
        // Update progress indicators
        const progressSteps = document.querySelectorAll('.progress-step');
        const connectors = document.querySelectorAll('.progress-connector');
        
        progressSteps.forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
            }
        });

        connectors.forEach((connector, index) => {
            connector.classList.remove('completed');
            if (index + 1 < this.currentStep) {
                connector.classList.add('completed');
            }
        });

        // Update step indicator
        const currentStepEl = document.querySelector('.current-step');
        if (currentStepEl) {
            currentStepEl.textContent = this.currentStep;
        }

        // Update navigation buttons
        const prevBtn = document.querySelector('.prev-btn');
        const nextBtn = document.querySelector('.next-btn');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentStep === 1;
        }
        
        if (nextBtn) {
            const stepNames = ['Research', 'Script', 'Audio', 'Publish'];
            if (this.currentStep < this.totalSteps) {
                nextBtn.querySelector('span').textContent = `Next: ${stepNames[this.currentStep - 1]}`;
            } else {
                nextBtn.querySelector('span').textContent = 'Publish';
            }
        }
    }

    // Quick actions
    handleQuickAction(action) {
        switch (action) {
            case 'random':
                this.generateRandomTopic();
                break;
            case 'templates':
                this.showTemplates();
                break;
            case 'history':
                this.showHistory();
                break;
        }
    }

    generateRandomTopic() {
        const randomTopics = [
            'The rise of AI influencers on social media',
            'Gen Z\'s impact on sustainable fashion',
            'Mental health awareness in gaming communities',
            'The future of remote work for young professionals',
            'Cryptocurrency adoption among college students'
        ];
        
        const randomTopic = randomTopics[Math.floor(Math.random() * randomTopics.length)];
        this.projectData.content = `Random Topic: ${randomTopic}\n\nThis is a trending topic that's perfect for creating engaging content. Let's explore what makes this topic interesting and how it resonates with your audience.`;
        
        this.updateContentDisplay();
        this.showNotification('🎲 Random topic generated!', 'success');
    }

    showTemplates() {
        this.showNotification('📋 Template library coming soon!', 'info');
    }

    showHistory() {
        this.showNotification('📚 Project history coming soon!', 'info');
    }

    // Utility methods
    updateContentDisplay() {
        const contentInput = document.querySelector('.content-input');
        if (contentInput) {
            contentInput.value = this.projectData.content;
        }
    }

    updateProgress() {
        // Update progress based on content length and completeness
        const hasContent = this.projectData.content.trim().length > 50;
        const nextBtn = document.querySelector('.next-btn');
        
        if (nextBtn) {
            nextBtn.disabled = !hasContent;
        }
    }

    addTypingIndicator() {
        // Add visual typing indicator
        const contentInput = document.querySelector('.content-input');
        if (contentInput) {
            contentInput.style.borderColor = 'var(--neon-pink)';
            contentInput.style.boxShadow = '0 0 20px rgba(255, 0, 110, 0.3)';
        }
    }

    removeTypingIndicator() {
        const contentInput = document.querySelector('.content-input');
        if (contentInput) {
            contentInput.style.borderColor = 'var(--bg-tertiary)';
            contentInput.style.boxShadow = 'none';
        }
    }

    setupAutoSave() {
        setInterval(() => {
            if (this.projectData.content.trim()) {
                this.autoSave();
            }
        }, 30000); // Auto-save every 30 seconds
    }

    autoSave() {
        // Simulate auto-save
        const saveBtn = document.querySelector('.save-btn');
        if (saveBtn) {
            saveBtn.style.background = 'var(--creation-success)';
            setTimeout(() => {
                saveBtn.style.background = '';
            }, 1000);
        }
    }

    saveProject() {
        this.showNotification('💾 Project saved successfully!', 'success');
        
        // Visual feedback
        const saveBtn = document.querySelector('.save-btn');
        saveBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            saveBtn.style.transform = 'scale(1)';
        }, 150);
    }

    initializeStep() {
        this.updateStepDisplay();
        this.updateProgress();
    }

    showHelp() {
        this.showNotification('💡 Help system coming soon!', 'info');
    }

    showFeedback() {
        this.showNotification('📝 Feedback form coming soon!', 'info');
    }

    async startResearch() {
        if (!this.projectData.topic) {
            this.showNotification('⚠️ 请先输入话题', 'warning');
            return;
        }

        try {
            this.showNotification('🔍 AI正在研究话题...', 'info');

            // Call the analysis API
            const response = await fetch('/api/genz/analyze-trend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    topic: this.projectData.topic,
                    platform: 'tiktok'
                })
            });

            const result = await response.json();

            if (result.success) {
                // Store research data
                this.projectData.research = result.data;

                // Show success notification
                this.showNotification('✅ 话题研究完成！', 'success');

                // Update UI with research results
                this.displayResearchResults(result.data);

                // Auto-advance to research step if not already there
                if (this.currentStep === 1) {
                    setTimeout(() => {
                        this.nextStep();
                    }, 1500);
                }
            } else {
                throw new Error(result.error || '研究失败');
            }
        } catch (error) {
            this.showNotification(`❌ 研究失败: ${error.message}`, 'error');
        }
    }

    displayResearchResults(data) {
        // Find research results container
        const resultsContainer = document.querySelector('.research-results');
        if (!resultsContainer) return;

        // Create research results HTML
        resultsContainer.innerHTML = `
            <div class="research-overview">
                <h3>📊 研究结果: ${data.topic}</h3>
                <div class="research-metrics">
                    <div class="metric">
                        <span class="metric-value">${data.engagement_score}</span>
                        <span class="metric-label">病毒指数</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value">${data.viral_potential}</span>
                        <span class="metric-label">病毒潜力</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value">${data.estimated_reach}</span>
                        <span class="metric-label">预估触达</span>
                    </div>
                </div>
            </div>

            <div class="research-insights">
                <h4>💡 关键洞察</h4>
                <ul>
                    ${data.key_insights.map(insight => `<li>${insight}</li>`).join('')}
                </ul>
            </div>

            <div class="research-suggestions">
                <h4>🎯 内容建议</h4>
                <div class="suggestions-list">
                    ${data.content_suggestions.map(suggestion => `
                        <div class="suggestion-item" onclick="window.contentCreator.applySuggestion('${suggestion}')">
                            <i class="fas fa-lightbulb"></i>
                            <span>${suggestion}</span>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="research-hashtags">
                <h4>#️⃣ 推荐标签</h4>
                <div class="hashtags">
                    ${data.hashtags.map(tag => `
                        <span class="hashtag" onclick="window.contentCreator.addHashtag('${tag}')">${tag}</span>
                    `).join('')}
                </div>
            </div>
        `;
    }

    applySuggestion(suggestion) {
        // Add suggestion to script or content
        this.showNotification(`✅ 已应用建议: ${suggestion}`, 'success');

        // You could add logic here to actually apply the suggestion to the script
    }

    addHashtag(hashtag) {
        // Add hashtag to the project
        this.showNotification(`#️⃣ 已添加标签: ${hashtag}`, 'success');

        // You could add logic here to actually add the hashtag to the project
    }

    showNotification(message, type = 'info') {
        // Use the notification system from the main app
        if (window.trendcastApp) {
            window.trendcastApp.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Initialize the content creator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.contentCreator = new ContentCreator();
});
