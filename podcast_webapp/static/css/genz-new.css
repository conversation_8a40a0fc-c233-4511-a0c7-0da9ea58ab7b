/* TrendCast AI - New Design (100% Screenshot Replication) */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #ffffff;
    overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Selection Styling */
::selection {
    background: rgba(0, 0, 0, 0.1);
    color: #000;
}

::-moz-selection {
    background: rgba(0, 0, 0, 0.1);
    color: #000;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e5e5;
    z-index: 1000;
    padding: 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
}

.brand-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-item {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-item:hover {
    color: #000;
}

.nav-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-primary {
    background: #000;
    color: #fff;
}

.btn-primary:hover {
    background: #333;
    transform: translateY(-1px);
}

.btn-secondary {
    background: transparent;
    color: #666;
    border: 1px solid #e5e5e5;
}

.btn-secondary:hover {
    background: #f8f8f8;
    color: #000;
}

.btn-primary.large, .btn-secondary.large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* Hero Section */
.hero {
    padding: 8rem 0 6rem;
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
    z-index: 0;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    color: #000;
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2.5rem;
    line-height: 1.6;
    font-weight: 400;
}

.hero-actions {
    display: flex;
    gap: 1rem;
}

/* Image Grid */
.image-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 0.75rem;
    height: 450px;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
    border-radius: 24px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
}

.image-card {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.image-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.image-card:hover::before {
    opacity: 1;
}

.image-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.image-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.image-card:hover img {
    transform: scale(1.1);
}

/* Grid Layout Pattern */
.image-card:nth-child(1) {
    grid-column: 1;
    grid-row: 1 / 3;
}

.image-card:nth-child(2) {
    grid-column: 2;
    grid-row: 1;
}

.image-card:nth-child(3) {
    grid-column: 3;
    grid-row: 1;
}

.image-card:nth-child(4) {
    grid-column: 2;
    grid-row: 2 / 4;
}

.image-card:nth-child(5) {
    grid-column: 3;
    grid-row: 2;
}

.image-card:nth-child(6) {
    grid-column: 1 / 2;
    grid-row: 3;
}

/* Features Section */
.features {
    padding: 6rem 0;
    background: #f8f9fa;
}

.features-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: #fff;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: #000;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #fff;
    font-size: 1.5rem;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #000;
    margin-bottom: 1rem;
}

.feature-description {
    color: #666;
    line-height: 1.6;
}

/* Dark Section */
.dark-section {
    background: #000;
    color: #fff;
    padding: 6rem 0;
}

.dark-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.dark-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.dark-subtitle {
    font-size: 1.1rem;
    color: #ccc;
    margin-bottom: 2rem;
}

/* Dashboard Preview */
.dashboard-preview {
    background: #1a1a1a;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dashboard-header {
    background: #2a2a2a;
    padding: 1rem;
    border-bottom: 1px solid #333;
}

.dashboard-nav {
    display: flex;
    gap: 0.5rem;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #666;
}

.nav-dot:nth-child(1) { background: #ff5f56; }
.nav-dot:nth-child(2) { background: #ffbd2e; }
.nav-dot:nth-child(3) { background: #27ca3f; }

.dashboard-content {
    display: flex;
    height: 300px;
}

.dashboard-sidebar {
    width: 200px;
    background: #1a1a1a;
    padding: 1.5rem;
    border-right: 1px solid #333;
}

.sidebar-item {
    padding: 0.75rem 0;
    color: #999;
    cursor: pointer;
    transition: color 0.2s ease;
}

.sidebar-item.active {
    color: #fff;
    font-weight: 600;
}

.dashboard-main {
    flex: 1;
    padding: 1.5rem;
}

.chart-placeholder {
    height: 150px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.stats-row {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff;
}

.stat-label {
    font-size: 0.9rem;
    color: #999;
}

/* Stats Section */
.stats {
    padding: 6rem 0;
    background: #fff;
}

.stats-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.stats-header {
    text-align: center;
    margin-bottom: 4rem;
}

.stats-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 1rem;
}

.stats-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    text-align: center;
    padding: 2rem;
}

.stat-card .stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 1rem;
    color: #666;
}

/* Gallery Section */
.gallery {
    padding: 6rem 0;
    background: #f8f9fa;
}

.gallery-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.gallery-header {
    text-align: center;
    margin-bottom: 4rem;
}

.gallery-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 1rem;
}

.gallery-subtitle {
    font-size: 1.1rem;
    color: #666;
}

.gallery-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 1rem;
    height: 500px;
}

.gallery-item {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.02);
}

.gallery-item.large {
    grid-row: span 2;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: #fff;
    padding: 2rem;
}

.gallery-overlay h3,
.gallery-overlay h4 {
    margin-bottom: 0.5rem;
}

/* CTA Section */
.cta {
    padding: 6rem 0;
    background: #fff;
}

.cta-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 1.5rem;
}

.cta-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
}

.cta-actions {
    display: flex;
    gap: 1rem;
}

.cta-visual img {
    width: 100%;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Footer */
.footer {
    background: #000;
    color: #fff;
    padding: 4rem 0 2rem;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    margin-bottom: 3rem;
}

.footer-brand h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.footer-brand p {
    color: #ccc;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.footer-column h4 {
    margin-bottom: 1rem;
    color: #fff;
}

.footer-column a {
    display: block;
    color: #ccc;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.2s ease;
}

.footer-column a:hover {
    color: #fff;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid #333;
    color: #ccc;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    color: #ccc;
    font-size: 1.2rem;
    transition: color 0.2s ease;
}

.footer-social a:hover {
    color: #fff;
}

/* Loading Animation */
.hero-title,
.hero-subtitle,
.hero-actions {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

/* Smooth Animations */
.image-card {
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Hover Effects */
.btn-primary:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-secondary:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

/* Gradient Overlays */
.gallery-overlay {
    background: linear-gradient(
        180deg,
        transparent 0%,
        rgba(0, 0, 0, 0.1) 40%,
        rgba(0, 0, 0, 0.8) 100%
    );
}

/* Enhanced Dashboard Preview */
.dashboard-preview {
    position: relative;
    overflow: hidden;
}

.dashboard-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.05) 100%
    );
    pointer-events: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-container,
    .dark-container,
    .cta-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-visual {
        order: -1;
    }

    .dashboard-content {
        flex-direction: column;
        height: auto;
    }

    .dashboard-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #333;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 1rem;
    }

    .hero {
        padding: 6rem 0 4rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .nav-menu {
        display: none;
    }

    .nav-actions {
        gap: 0.5rem;
    }

    .btn-primary.large,
    .btn-secondary.large {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-actions .btn-primary,
    .hero-actions .btn-secondary {
        width: 100%;
        max-width: 280px;
    }

    .image-grid {
        grid-template-columns: repeat(2, 1fr);
        height: 300px;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(4, 200px);
        height: auto;
    }

    .gallery-item.large {
        grid-row: span 1;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .dark-title,
    .cta-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
        line-height: 1.2;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .image-grid {
        grid-template-columns: 1fr;
        height: 400px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card .stat-number {
        font-size: 2.5rem;
    }
}
