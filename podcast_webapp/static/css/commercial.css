/* Commercial AI Podcast Studio - Professional Design System */

/* CSS Custom Properties - Design Tokens */
:root {
  /* Primary Colors (Trust & Innovation) */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Semantic Colors */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
  
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Layout */
  --header-height: 4rem;
  --sidebar-width: 16rem;
  --container-max-width: 1280px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-sans);
  background-color: var(--neutral-50);
  color: var(--neutral-700);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Main Header */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background-color: white;
  border-bottom: 1px solid var(--neutral-200);
  z-index: 50;
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--space-6);
}

/* Brand */
.header-brand {
  display: flex;
  align-items: center;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--neutral-900);
}

.logo-icon {
  width: 2rem;
  height: 2rem;
  color: var(--primary-600);
}

.brand-text {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--neutral-900);
}

.brand-badge {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Primary Navigation */
.primary-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--neutral-600);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-600);
  background-color: var(--primary-50);
}

.nav-link.active {
  color: var(--primary-600);
  background-color: var(--primary-100);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 2px;
  background: var(--primary-600);
  border-radius: 1px;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

/* Search */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 20rem;
  padding: var(--space-2) var(--space-3) var(--space-2) var(--space-10);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  background-color: var(--neutral-50);
  font-size: var(--text-sm);
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  background-color: white;
  box-shadow: 0 0 0 3px var(--primary-100);
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  color: var(--neutral-400);
  font-size: var(--text-sm);
}

.search-shortcut {
  position: absolute;
  right: var(--space-3);
  background-color: var(--neutral-200);
  color: var(--neutral-500);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-family: var(--font-mono);
}

/* Action Buttons */
.action-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--neutral-600);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--neutral-100);
  color: var(--neutral-700);
}

.notification-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 1rem;
  height: 1rem;
  background-color: var(--error-500);
  color: white;
  border-radius: 50%;
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-avatar-btn {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2);
  border: none;
  border-radius: var(--radius-lg);
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.user-avatar-btn:hover {
  background-color: var(--neutral-100);
}

.user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--neutral-700);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--space-2);
  min-width: 12rem;
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-0.5rem);
  transition: all 0.2s ease;
}

.user-menu:hover .user-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  text-decoration: none;
  color: var(--neutral-700);
  font-size: var(--text-sm);
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: var(--neutral-50);
  color: var(--neutral-900);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--neutral-200);
  margin: var(--space-2) 0;
}

/* Main Content */
.main-content {
  margin-top: var(--header-height);
  min-height: calc(100vh - var(--header-height));
}

.content-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--space-8) var(--space-6);
}

/* Page Header */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-8);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--neutral-900);
  margin-bottom: var(--space-2);
}

.page-subtitle {
  font-size: var(--text-lg);
  color: var(--neutral-600);
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: white;
  color: var(--neutral-700);
  border-color: var(--neutral-300);
}

.btn-secondary:hover {
  background-color: var(--neutral-50);
  border-color: var(--neutral-400);
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.metric-card {
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all 0.2s ease;
}

.metric-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.metric-icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
}

.metric-icon.primary {
  background-color: var(--primary-100);
  color: var(--primary-600);
}

.metric-icon.success {
  background-color: var(--success-50);
  color: var(--success-600);
}

.metric-icon.info {
  background-color: var(--info-50);
  color: var(--info-600);
}

.metric-icon.warning {
  background-color: var(--warning-50);
  color: var(--warning-600);
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.metric-trend.positive {
  color: var(--success-600);
}

.metric-trend.negative {
  color: var(--error-600);
}

.metric-trend.neutral {
  color: var(--neutral-500);
}

.metric-value {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--neutral-900);
  margin-bottom: var(--space-1);
}

.metric-label {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
  margin-bottom: var(--space-1);
}

.metric-description {
  font-size: var(--text-sm);
  color: var(--neutral-500);
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-6);
}

.dashboard-card {
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all 0.2s ease;
}

.dashboard-card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6) var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--neutral-100);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--neutral-900);
}

.card-action {
  color: var(--primary-600);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: color 0.2s ease;
}

.card-action:hover {
  color: var(--primary-700);
}

.card-content {
  padding: var(--space-6);
}

/* Recent Projects */
.recent-projects {
  grid-column: 1 / 2;
  grid-row: 1 / 3;
}

.project-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.project-item {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
  cursor: pointer;
}

.project-item:hover {
  border-color: var(--primary-300);
  background-color: var(--primary-50);
}

.project-icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
}

.project-info {
  flex: 1;
}

.project-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--neutral-900);
  margin-bottom: var(--space-1);
}

.project-meta {
  font-size: var(--text-sm);
  color: var(--neutral-500);
  margin-bottom: var(--space-2);
}

.project-progress {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.progress-bar {
  flex: 1;
  height: 0.5rem;
  background-color: var(--neutral-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-sm);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--text-xs);
  color: var(--neutral-500);
  font-weight: var(--font-medium);
  white-space: nowrap;
}

.project-status {
  margin-left: auto;
}

.status-badge {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.completed {
  background-color: var(--success-50);
  color: var(--success-600);
}

.status-badge.in-progress {
  background-color: var(--info-50);
  color: var(--info-600);
}

.status-badge.draft {
  background-color: var(--neutral-100);
  color: var(--neutral-600);
}

/* System Status */
.system-status {
  grid-column: 2 / 3;
  grid-row: 1 / 2;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.status-indicator.online {
  color: var(--success-600);
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
}

.service-name {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-900);
  margin-bottom: var(--space-1);
}

.service-description {
  font-size: var(--text-xs);
  color: var(--neutral-500);
}

.service-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.service-status.online {
  color: var(--success-600);
}

.service-status.syncing {
  color: var(--warning-600);
}

/* Quick Actions */
.quick-actions {
  grid-column: 2 / 3;
  grid-row: 2 / 3;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.action-item:hover {
  border-color: var(--primary-300);
  background-color: var(--primary-50);
  transform: translateY(-2px);
}

.action-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-base);
}

.action-title {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-900);
}

.action-description {
  font-size: var(--text-xs);
  color: var(--neutral-500);
}

/* Usage Analytics */
.usage-analytics {
  grid-column: 1 / 3;
  grid-row: 3 / 4;
}

.time-selector {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  background-color: white;
  font-size: var(--text-sm);
  color: var(--neutral-700);
}

.chart-container {
  height: 300px;
  margin-bottom: var(--space-4);
}

.usage-chart {
  width: 100%;
  height: 100%;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--space-6);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--neutral-600);
}

.legend-color {
  width: 1rem;
  height: 1rem;
  border-radius: var(--radius-sm);
}

.legend-color.primary {
  background-color: var(--primary-500);
}

.legend-color.success {
  background-color: var(--success-500);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .recent-projects {
    grid-column: 1 / 2;
    grid-row: auto;
  }

  .system-status {
    grid-column: 1 / 2;
    grid-row: auto;
  }

  .quick-actions {
    grid-column: 1 / 2;
    grid-row: auto;
  }

  .usage-analytics {
    grid-column: 1 / 2;
    grid-row: auto;
  }
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 var(--space-4);
  }

  .primary-nav {
    display: none;
  }

  .search-input {
    width: 16rem;
  }

  .content-container {
    padding: var(--space-6) var(--space-4);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-4);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .search-container {
    display: none;
  }

  .user-name {
    display: none;
  }

  .project-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .project-status {
    margin-left: 0;
    align-self: flex-end;
  }
}

/* Project Creation Wizard */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
  font-size: var(--text-sm);
}

.breadcrumb-item {
  color: var(--neutral-500);
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: var(--primary-600);
}

.breadcrumb-item.current {
  color: var(--neutral-700);
  font-weight: var(--font-medium);
}

.breadcrumb-separator {
  color: var(--neutral-400);
  font-size: var(--text-xs);
}

.creation-wizard {
  background-color: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-2xl);
  overflow: hidden;
}

/* Wizard Progress */
.wizard-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-6);
  background: linear-gradient(135deg, var(--primary-50), var(--info-50));
  border-bottom: 1px solid var(--neutral-200);
}

.progress-step {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  color: var(--neutral-500);
  transition: all 0.3s ease;
}

.progress-step.active {
  color: var(--primary-600);
}

.progress-step.completed {
  color: var(--success-600);
}

.step-number {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: var(--neutral-200);
  color: var(--neutral-600);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-semibold);
  transition: all 0.3s ease;
}

.progress-step.active .step-number {
  background-color: var(--primary-600);
  color: white;
  box-shadow: 0 0 0 4px var(--primary-100);
}

.progress-step.completed .step-number {
  background-color: var(--success-600);
  color: white;
}

.progress-step.completed .step-number::before {
  content: '✓';
  font-weight: var(--font-bold);
}

.step-info {
  text-align: left;
}

.step-title {
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
}

.step-description {
  font-size: var(--text-sm);
  opacity: 0.8;
}

.progress-connector {
  width: 4rem;
  height: 2px;
  background-color: var(--neutral-300);
  margin: 0 var(--space-6);
  position: relative;
  overflow: hidden;
}

.progress-connector::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: var(--success-600);
  width: 0%;
  transition: width 0.5s ease;
}

.progress-connector.completed::after {
  width: 100%;
}

/* Wizard Steps */
.wizard-step {
  display: none;
  padding: var(--space-8) var(--space-6);
}

.wizard-step.active {
  display: block;
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-content {
  max-width: 1000px;
  margin: 0 auto;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-8);
}

.content-section {
  margin-bottom: var(--space-8);
}

.section-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--neutral-900);
  margin-bottom: var(--space-2);
}

.section-description {
  font-size: var(--text-base);
  color: var(--neutral-600);
  margin-bottom: var(--space-6);
}

/* Template Grid */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-4);
}

.template-card {
  border: 2px solid var(--neutral-200);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: white;
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.1), transparent);
  transition: left 0.5s ease;
}

.template-card:hover::before {
  left: 100%;
}

.template-card:hover {
  border-color: var(--primary-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.template-card.selected {
  border-color: var(--primary-600);
  background: linear-gradient(135deg, var(--primary-50), white);
  box-shadow: 0 0 0 4px var(--primary-100);
}

.template-icon {
  width: 4rem;
  height: 4rem;
  border-radius: var(--radius-xl);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-2xl);
  margin-bottom: var(--space-4);
}

.template-card.selected .template-icon {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  box-shadow: var(--shadow-md);
}

.template-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--neutral-900);
  margin-bottom: var(--space-2);
}

.template-description {
  font-size: var(--text-sm);
  color: var(--neutral-600);
  margin-bottom: var(--space-4);
  line-height: 1.5;
}

.template-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.feature-tag {
  background-color: var(--neutral-100);
  color: var(--neutral-700);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.template-card.selected .feature-tag {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

/* Form Styles */
.form-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
}

.form-label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--neutral-700);
}

.form-input,
.form-textarea,
.form-select {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  color: var(--neutral-700);
  background-color: white;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.form-textarea {
  resize: vertical;
  min-height: 6rem;
}

.form-hint {
  font-size: var(--text-xs);
  color: var(--neutral-500);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
}

.checkbox-item input[type="checkbox"] {
  display: none;
}

.checkbox-mark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-mark {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
}

.checkbox-item input[type="checkbox"]:checked + .checkbox-mark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
}

.checkbox-label {
  font-size: var(--text-sm);
  color: var(--neutral-700);
}

/* Wizard Navigation */
.wizard-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-6);
  background-color: var(--neutral-50);
  border-top: 1px solid var(--neutral-200);
}

.nav-left,
.nav-right {
  display: flex;
  gap: var(--space-3);
}

/* Responsive Design for Wizard */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .template-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .wizard-progress {
    flex-direction: column;
    gap: var(--space-4);
    text-align: center;
  }

  .progress-connector {
    width: 2px;
    height: 2rem;
    margin: 0;
  }

  .progress-connector::after {
    width: 100%;
    height: 0%;
    transition: height 0.5s ease;
  }

  .progress-connector.completed::after {
    height: 100%;
  }

  .step-info {
    text-align: center;
  }

  .template-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .wizard-navigation {
    flex-direction: column;
    gap: var(--space-4);
  }

  .nav-left,
  .nav-right {
    width: 100%;
    justify-content: center;
  }
}
