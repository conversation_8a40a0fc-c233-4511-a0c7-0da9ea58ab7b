/* CSS Custom Properties for Premium Design System */
:root {
    /* Premium Color Palette - Sophisticated & Refined */
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    --secondary-gradient: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    --success-gradient: linear-gradient(135deg, #059669 0%, #047857 100%);
    --accent-gradient: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
    
    /* Refined Base Colors */
    --primary-500: #2563eb;
    --primary-600: #1d4ed8;
    --primary-700: #1e40af;
    --primary-800: #1e3a8a;
    --secondary-500: #6366f1;
    --secondary-600: #4f46e5;
    --accent-500: #0891b2;
    --accent-600: #0e7490;
    --success-500: #059669;
    --success-600: #047857;
    --warning-500: #d97706;
    --warning-600: #b45309;
    --error-500: #dc2626;
    --error-600: #b91c1c;
    
    /* Sophisticated Neutral Palette */
    --white: #ffffff;
    --gray-25: #fcfcfd;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --gray-950: #020617;
    
    /* Premium Glass Effects */
    --glass-bg: rgba(248, 250, 252, 0.8);
    --glass-bg-dark: rgba(30, 41, 59, 0.8);
    --glass-border: rgba(203, 213, 225, 0.3);
    --glass-border-dark: rgba(148, 163, 184, 0.2);
    --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --glass-shadow-sm: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    
    /* Professional Backdrop Effects */
    --backdrop-blur: blur(16px);
    --backdrop-blur-sm: blur(8px);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Premium Typography System */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
    --font-display: 'Inter', system-ui, sans-serif;
    
    /* Typography Scale */
    --text-xs: 0.75rem;    /* 12px */
    --text-sm: 0.875rem;   /* 14px */
    --text-base: 1rem;     /* 16px */
    --text-lg: 1.125rem;   /* 18px */
    --text-xl: 1.25rem;    /* 20px */
    --text-2xl: 1.5rem;    /* 24px */
    --text-3xl: 1.875rem;  /* 30px */
    --text-4xl: 2.25rem;   /* 36px */
    
    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* Line Heights */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Z-Index Stack */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(30, 30, 30, 0.25);
        --glass-border: rgba(255, 255, 255, 0.1);
    }
}

/* CSS Reset & Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
    background: linear-gradient(135deg, 
        #0f172a 0%, 
        #1e293b 25%, 
        #334155 50%, 
        #475569 75%, 
        #64748b 100%);
    background-size: 400% 400%;
    animation: gradientShift 25s ease infinite;
    color: var(--gray-100);
    line-height: var(--leading-normal);
    min-height: 100vh;
    overflow-x: hidden;
    font-weight: var(--font-normal);
    letter-spacing: -0.025em;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Top Navigation Bar - Reference Design */
.top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    height: 64px;
    display: flex;
    align-items: center;
}

.nav-container {
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-left {
    display: flex;
    align-items: center;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.brand-logo svg {
    width: 24px;
    height: 24px;
    color: var(--primary-600);
}

.brand-text {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
}

.nav-center {
    display: flex;
    align-items: center;
}

.nav-tabs {
    display: flex;
    gap: var(--space-1);
    background: var(--gray-100);
    padding: var(--space-1);
    border-radius: var(--radius-md);
}

.nav-tab {
    padding: var(--space-2) var(--space-4);
    border: none;
    background: transparent;
    color: var(--gray-600);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-tab.active {
    background: var(--white);
    color: var(--gray-900);
    box-shadow: var(--shadow-sm);
}

.nav-right {
    display: flex;
    align-items: center;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.nav-action-btn {
    padding: var(--space-2);
    border: none;
    background: transparent;
    color: var(--gray-500);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-action-btn:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.nav-action-btn svg {
    width: 20px;
    height: 20px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Main Content Layout */
.app-container {
    padding-top: 64px;
    min-height: 100vh;
    background: var(--gray-50);
}

.hero-section {
    display: none; /* Hide hero section for minimal design */
}

.main-content {
    max-width: 1440px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-6);
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-8);
}

.page-title-section h1 {
    font-size: var(--text-3xl);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.page-subtitle {
    font-size: var(--text-base);
    color: var(--gray-600);
}

.page-actions {
    display: flex;
    gap: var(--space-3);
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: var(--space-6);
}

@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
}

/* Cards */
.main-card,
.sidebar-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.card-header-minimal {
    padding: var(--space-6) var(--space-6) 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.card-title-minimal {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
}

.step-indicator {
    font-size: var(--text-sm);
    color: var(--gray-500);
    background: var(--gray-100);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
}

.card-content-minimal {
    padding: 0 var(--space-6) var(--space-6);
}

/* Form Styles */
.form-section {
    margin-bottom: var(--space-6);
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

.required {
    color: var(--error-500);
}

.form-input {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    color: var(--gray-900);
    background: var(--white);
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-help {
    font-size: var(--text-sm);
    color: var(--gray-500);
    margin-top: var(--space-1);
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.config-item {
    display: flex;
    flex-direction: column;
}

.form-select {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    color: var(--gray-900);
    background: var(--white);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-actions-minimal {
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
    padding-top: var(--space-6);
    border-top: 1px solid var(--gray-200);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    transition: all var(--transition-fast);
    cursor: pointer;
    border: none;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-600);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-700);
}

.btn-outline {
    background: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover {
    background: var(--gray-50);
}

.btn-outline-small,
.btn-primary-small {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
}

.btn-outline-small {
    background: transparent;
    color: var(--gray-600);
    border: 1px solid var(--gray-300);
}

.btn-primary-small {
    background: var(--primary-600);
    color: var(--white);
}

/* Sidebar Progress */
.progress-steps {
    margin-bottom: var(--space-6);
}

.progress-step {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) 0;
    border-bottom: 1px solid var(--gray-100);
}

.progress-step:last-child {
    border-bottom: none;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    background: var(--gray-100);
    color: var(--gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
}

.progress-step.active .step-number {
    background: var(--primary-600);
    color: var(--white);
}

.step-info {
    flex: 1;
}

.step-title {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--gray-900);
}

.step-desc {
    font-size: var(--text-xs);
    color: var(--gray-500);
}

.metrics-summary {
    padding-top: var(--space-4);
    border-top: 1px solid var(--gray-200);
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
}

.metric-label {
    font-size: var(--text-sm);
    color: var(--gray-600);
}

.metric-value {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--gray-900);
}

/* Hide legacy sections for minimal design */
.metrics-dashboard,
.content-card {
    display: none;
}

/* Legacy styles cleanup - remove duplicates */

.brand-logo {
    font-size: 2rem;
    filter: drop-shadow(0 0 8px rgba(102, 126, 234, 0.5));
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.demo-badge,
.live-badge {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.demo-badge {
    background: rgba(251, 191, 36, 0.2);
    border: 1px solid rgba(251, 191, 36, 0.3);
    color: var(--warning-500);
}

.live-badge {
    background: rgba(67, 233, 123, 0.2);
    border: 1px solid rgba(67, 233, 123, 0.3);
    color: var(--accent-500);
}

/* App Container */
.app-container {
    padding-top: 80px;
    min-height: 100vh;
}

/* Hero Section */
.hero-section {
    text-align: center;
    padding: var(--space-20) var(--space-6) var(--space-16);
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-6);
    background: linear-gradient(135deg, var(--white) 0%, rgba(255, 255, 255, 0.8) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3)); }
    to { filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.6)); }
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-8);
    font-weight: 400;
}

.hero-features {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.feature-tag {
    display: inline-flex;
    align-items: center;
    padding: var(--space-2) var(--space-4);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    backdrop-filter: blur(10px);
    color: var(--white);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-base);
    cursor: default;
}

.feature-tag:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6) var(--space-20);
}

/* Workflow Progress */
.workflow-progress {
    display: grid;
    grid-template-columns: 1fr auto 1fr auto 1fr;
    align-items: center;
    margin-bottom: var(--space-16);
    padding: var(--space-6);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
}

.workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--space-4);
    border-radius: var(--radius-xl);
    transition: all var(--transition-base);
    cursor: default;
}

.workflow-step.active {
    background: rgba(102, 126, 234, 0.2);
    border: 1px solid rgba(102, 126, 234, 0.3);
    transform: scale(1.05);
}

.workflow-step.completed {
    background: rgba(79, 172, 254, 0.2);
    border: 1px solid rgba(79, 172, 254, 0.3);
}

.step-icon {
    font-size: 2rem;
    margin-bottom: var(--space-2);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.step-title {
    font-weight: 600;
    color: var(--white);
    margin-bottom: var(--space-1);
}

.step-subtitle {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
}

.workflow-connector {
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    position: relative;
}

.workflow-connector::after {
    content: '';
    position: absolute;
    right: -4px;
    top: -2px;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
}

/* Content Cards */
.content-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
    margin-bottom: var(--space-8);
    overflow: hidden;
    transition: all var(--transition-base);
    opacity: 0;
    transform: translateY(20px);
}

.content-card.active,
.content-card:not(.hidden) {
    opacity: 1;
    transform: translateY(0);
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

.card-header {
    padding: var(--space-8) var(--space-8) var(--space-6);
    text-align: center;
    position: relative;
}

.card-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--space-3);
}

.card-icon {
    font-size: 2.5rem;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
}

.card-subtitle {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
}

/* Form Styles */
.podcast-form {
    padding: 0 var(--space-8) var(--space-8);
}

.input-group {
    margin-bottom: var(--space-6);
}

.input-group.featured {
    margin-bottom: var(--space-8);
}

.input-label {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    margin-bottom: var(--space-3);
    font-weight: 500;
    color: var(--white);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

.label-required {
    color: var(--error-500);
    font-weight: 600;
}

.input-wrapper {
    position: relative;
}

.topic-input,
.modern-select,
.script-editor {
    width: 100%;
    padding: var(--space-4) var(--space-6);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    color: var(--white);
    font-size: 1rem;
    font-weight: 400;
    backdrop-filter: blur(10px);
    transition: all var(--transition-base);
    outline: none;
}

.topic-input {
    font-size: 1.125rem;
    padding: var(--space-5) var(--space-6);
    padding-right: var(--space-12);
}

.topic-input::placeholder,
.modern-select option,
.script-editor::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.topic-input:focus,
.modern-select:focus,
.script-editor:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2), var(--shadow-lg);
    transform: translateY(-1px);
}

.input-icon {
    position: absolute;
    right: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.25rem;
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.input-hint {
    margin-top: var(--space-2);
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
}

.input-help {
    margin-top: var(--space-2);
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.4;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-xl);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-base);
    outline: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-outline {
    background: transparent;
    color: var(--white);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.btn-large {
    padding: var(--space-4) var(--space-8);
    font-size: 1rem;
    border-radius: var(--radius-2xl);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-loading .btn-text {
    opacity: 0;
}

.btn-loading .btn-loading {
    display: flex !important;
    position: absolute;
    align-items: center;
    gap: var(--space-2);
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.form-actions {
    text-align: center;
    margin-top: var(--space-8);
}

/* Progress Styles */
.progress-container {
    text-align: center;
    margin-bottom: var(--space-8);
}

.progress-track {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--space-4);
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--accent-gradient);
    width: 0%;
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.progress-stages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin-top: var(--space-8);
}

.stage {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    transition: all var(--transition-base);
}

.stage.active {
    background: rgba(102, 126, 234, 0.3);
    border-color: var(--primary-500);
    transform: scale(1.02);
}

.stage.completed {
    background: rgba(79, 172, 254, 0.3);
    border-color: var(--success-500);
}

.stage-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stage-content {
    flex: 1;
}

.stage-title {
    font-weight: 600;
    color: var(--white);
    margin-bottom: var(--space-1);
}

.stage-description {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
}

.stage-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    flex-shrink: 0;
    transition: all var(--transition-base);
}

.stage.active .stage-status {
    background: var(--primary-500);
    box-shadow: 0 0 10px var(--primary-500);
}

.stage.completed .stage-status {
    background: var(--success-500);
    box-shadow: 0 0 10px var(--success-500);
}

/* TTS Info Panel */
.tts-info-panel {
    margin-top: var(--space-8);
    padding: var(--space-6);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
}

.tts-info-header h4 {
    color: var(--white);
    margin-bottom: var(--space-4);
    font-size: 1.125rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-2);
}

.info-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.info-value {
    color: var(--white);
    font-weight: 600;
}

/* Report Content */
.report-content {
    padding: var(--space-8);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    margin: var(--space-6);
    color: var(--white);
}

.report-content h3 {
    color: var(--white);
    margin-bottom: var(--space-4);
    font-size: 1.25rem;
}

.report-content .summary {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-6);
    line-height: 1.7;
}

.report-content ul {
    list-style: none;
    padding: 0;
}

.report-content li {
    padding: var(--space-3) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    padding-left: var(--space-6);
}

.report-content li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--accent-500);
    font-weight: bold;
    font-size: 1.2em;
}

.report-content .sources li {
    background: rgba(255, 255, 255, 0.05);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-3);
    border: none;
    padding-left: var(--space-4);
}

.report-content .sources li::before {
    display: none;
}

/* Script Styles */
.script-container {
    margin: var(--space-6);
}

.script-meta {
    padding: var(--space-6);
    text-align: center;
    margin-bottom: var(--space-6);
}

.script-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--space-2);
}

.script-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

.dialogue-view {
    max-height: 600px;
    overflow-y: auto;
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    scroll-behavior: smooth;
}

.dialogue-view::-webkit-scrollbar {
    width: 8px;
}

.dialogue-view::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
}

.dialogue-view::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
}

.dialogue-line {
    margin-bottom: var(--space-4);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    border-left: 4px solid var(--primary-500);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    transition: all var(--transition-base);
}

.dialogue-line:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(4px);
}

.dialogue-line .speaker {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: 600;
    color: var(--primary-500);
    margin-bottom: var(--space-2);
    font-size: 0.875rem;
}

.dialogue-line .text {
    color: var(--white);
    line-height: 1.6;
    font-style: italic;
}

.emotion-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1) var(--space-2);
    background: rgba(102, 126, 234, 0.2);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
    font-style: normal;
}

.script-actions {
    padding: var(--space-6);
    text-align: center;
}

/* Voice Selection */
.voice-selection-grid {
    display: grid;
    gap: var(--space-6);
    padding: var(--space-6);
}

.voice-selection-item {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    backdrop-filter: blur(10px);
    transition: all var(--transition-base);
}

.voice-selection-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.voice-selection-item h4 {
    color: var(--white);
    margin-bottom: var(--space-3);
    font-size: 1.125rem;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.sample-text {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-4);
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
    border-left: 3px solid var(--accent-500);
}

.voice-controls {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--space-4);
    align-items: center;
}

.voice-controls label {
    color: var(--white);
    font-weight: 500;
    font-size: 0.875rem;
}

.voice-selector {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    color: var(--white);
    backdrop-filter: blur(10px);
    outline: none;
    transition: all var(--transition-base);
}

.voice-selector:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.preview-voice-btn {
    background: var(--accent-gradient) !important;
    border: none !important;
    padding: var(--space-2) var(--space-4) !important;
    border-radius: var(--radius-lg) !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: var(--white) !important;
}

.approval-panel {
    padding: var(--space-8) var(--space-6);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: var(--space-6);
}

.approval-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

/* Audio Player */
.audio-player-container {
    padding: var(--space-8);
}

.audio-player-wrapper {
    margin-bottom: var(--space-6);
}

.modern-audio-player {
    width: 100%;
    height: 60px;
    border-radius: var(--radius-xl);
    outline: none;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.audio-meta {
    display: flex;
    justify-content: center;
    gap: var(--space-6);
    margin-bottom: var(--space-6);
}

.audio-info {
    display: flex;
    gap: var(--space-6);
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
}

.download-section {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

/* Error Card */
.error-card {
    border-color: var(--error-500) !important;
    background: rgba(239, 68, 68, 0.1) !important;
}

.error-content {
    padding: var(--space-8);
    text-align: center;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: var(--space-4);
}

.error-title {
    color: var(--error-500);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--space-4);
}

.error-message {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-6);
    line-height: 1.6;
}

.error-actions {
    display: flex;
    justify-content: center;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.modal-overlay:not(.hidden) {
    opacity: 1;
}

.modal-container {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-base);
}

.modal-overlay:not(.hidden) .modal-container {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-title {
    color: var(--white);
    font-size: 1.25rem;
    font-weight: 700;
}

.modal-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

.modal-body {
    padding: var(--space-6) var(--space-8);
    max-height: 60vh;
    overflow-y: auto;
}

.editor-container {
    position: relative;
}

.script-editor {
    min-height: 400px;
    font-family: var(--font-mono);
    font-size: 0.875rem;
    line-height: 1.6;
    resize: vertical;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
    padding: var(--space-6) var(--space-8);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* Footer */
.app-footer {
    text-align: center;
    padding: var(--space-8);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.footer-content {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.footer-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.footer-version {
    padding: var(--space-1) var(--space-3);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .settings-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .workflow-progress {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .workflow-connector {
        width: 2px;
        height: 20px;
        background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    }
    
    .workflow-connector::after {
        top: auto;
        bottom: -4px;
        left: -2px;
        right: auto;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .nav-container {
        padding: var(--space-3) var(--space-4);
    }
    
    .brand-name {
        font-size: 1.25rem;
    }
    
    .main-content {
        padding: 0 var(--space-4) var(--space-16);
    }
    
    .card-header {
        padding: var(--space-6) var(--space-6) var(--space-4);
    }
    
    .podcast-form {
        padding: 0 var(--space-6) var(--space-6);
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .progress-stages {
        grid-template-columns: 1fr;
    }
    
    .voice-controls {
        grid-template-columns: 1fr;
        gap: var(--space-3);
        text-align: left;
    }
    
    .approval-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .download-section {
        flex-direction: column;
        align-items: center;
    }
    
    .modal-container {
        margin: var(--space-4);
        max-height: 85vh;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--space-4) var(--space-6);
    }
}

@media (max-width: 480px) {
    .hero-features {
        flex-direction: column;
        align-items: center;
    }
    
    .feature-tag {
        width: 100%;
        justify-content: center;
    }
    
    .card-title {
        flex-direction: column;
        gap: var(--space-2);
    }
    
    .btn-large {
        width: 100%;
    }
}

/* Animation Enhancements */
@media (prefers-reduced-motion: no-preference) {
    .content-card {
        animation: slideIn 0.6s ease-out forwards;
    }
    
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .dialogue-line {
        animation: fadeInUp 0.4s ease-out forwards;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* Focus Styles for Accessibility */
*:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Enhanced Voice Selection Styles */
.role-badge {
    background: rgba(102, 126, 234, 0.2);
    color: var(--primary-500);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: var(--space-2);
}

.sample-quote {
    font-style: italic;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: var(--space-2);
}

.sample-meta {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.voice-select-wrapper {
    flex: 1;
    margin-right: var(--space-4);
}

.voice-suggestion {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: var(--space-1);
}

.voice-selection-item.voice-selected {
    border-color: var(--success-500);
    background: rgba(79, 172, 254, 0.1);
}

.voice-preview-player {
    margin-top: var(--space-4);
    padding: var(--space-3);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
}

.mini-audio-player {
    width: 100%;
    height: 40px;
    border-radius: var(--radius-lg);
}

.voice-feedback {
    position: absolute;
    top: -40px;
    right: 0;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0;
    transform: translateY(-10px);
    transition: all var(--transition-base);
    z-index: 10;
}

.voice-feedback-success {
    background: var(--success-gradient);
    color: var(--white);
}

.voice-feedback-warning {
    background: var(--warning-500);
    color: var(--white);
}

.voice-feedback-error {
    background: var(--error-500);
    color: var(--white);
}

.btn-disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    pointer-events: none;
}

/* Shake animation for errors */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Enhanced audio player */
.audio-duration #audio-duration {
    font-weight: 600;
    color: var(--accent-500);
}

/* Loading button states */
.btn.loading {
    position: relative;
    pointer-events: none;
}

.btn.loading .btn-text {
    opacity: 0;
    transition: opacity var(--transition-base);
}

.btn.loading .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex !important;
    align-items: center;
    gap: var(--space-2);
}

/* Enhanced modal animations */
.modal-overlay {
    animation: modalFadeIn var(--transition-base) ease-out;
}

.modal-overlay.hidden {
    animation: modalFadeOut var(--transition-base) ease-in;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(10px);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        backdrop-filter: blur(10px);
    }
    to {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
}

/* Enhanced scroll animations */
.animate-in {
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Voice selection grid responsive enhancements */
@media (max-width: 768px) {
    .voice-controls {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }
    
    .voice-select-wrapper {
        margin-right: 0;
        margin-bottom: var(--space-3);
    }
    
    .voice-feedback {
        position: relative;
        top: auto;
        right: auto;
        margin-top: var(--space-2);
    }
}

/* Enhanced progress stages for mobile */
@media (max-width: 640px) {
    .progress-stages {
        grid-template-columns: 1fr;
        gap: var(--space-2);
    }
    
    .stage {
        padding: var(--space-3);
    }
    
    .stage-icon {
        font-size: 1.25rem;
    }
}

/* Dark theme enhancements */
@media (prefers-color-scheme: dark) {
    .voice-selection-item.voice-selected {
        background: rgba(79, 172, 254, 0.2);
        border-color: var(--success-500);
    }
    
    .role-badge {
        background: rgba(102, 126, 234, 0.3);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.9);
        --glass-border: rgba(0, 0, 0, 0.3);
    }
    
    .content-card {
        border-width: 2px;
    }
    
    .voice-selection-item {
        border-width: 2px;
    }
    
    .btn {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .hero-section::before {
        animation: none;
    }
    
    .progress-fill::after {
        animation: none;
    }
}

/* Dashboard Styles */
/* Dashboard Header */
.dashboard-header {
    backdrop-filter: blur(20px);
    background: var(--glass-bg);
    border-bottom: 1px solid var(--glass-border);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: var(--shadow-lg);
    padding: var(--space-4) 0;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.dashboard-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.brand-section {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.brand-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: var(--radius-xl);
    box-shadow: var(--glass-shadow-sm);
}

.brand-icon {
    width: 24px;
    height: 24px;
    stroke-width: 2;
    color: var(--white);
}

.brand-info h1.brand-name {
    color: var(--white);
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    margin: 0;
    letter-spacing: -0.05em;
}

.brand-tagline {
    color: var(--gray-400);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-top: var(--space-1);
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.system-status .status-badge {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-5);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: var(--backdrop-blur-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-500);
}

.status-indicator.active {
    background: var(--success-500);
    box-shadow: 0 0 8px rgba(5, 150, 105, 0.4);
}

.status-badge.demo-mode {
    color: var(--warning-600);
}

.status-badge.demo-mode .status-indicator {
    background: var(--warning-500);
    box-shadow: 0 0 8px rgba(217, 119, 6, 0.4);
}

.status-badge.live-mode {
    color: var(--gray-200);
}

.dashboard-actions {
    display: flex;
    gap: var(--space-2);
}

.action-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--gray-300);
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    backdrop-filter: var(--backdrop-blur-sm);
}

.action-btn:hover {
    background: var(--glass-bg-dark);
    color: var(--white);
    border-color: var(--primary-500);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow-sm);
}

.action-icon {
    width: 18px;
    height: 18px;
    stroke-width: 2;
}

/* Dashboard Status Bar */
.dashboard-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-6);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: var(--space-6);
    flex: 1;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-3);
    background: rgba(248, 250, 252, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(203, 213, 225, 0.1);
}

.metric-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2;
    color: var(--gray-400);
}

.metric-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.metric-label {
    color: var(--gray-400);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.metric-value {
    color: var(--gray-100);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    letter-spacing: -0.025em;
}

.workflow-mini-progress {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--space-2);
    min-width: 200px;
}

.mini-progress-track {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.mini-progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    width: 0%;
    transition: width var(--transition-base);
}

.mini-progress-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.75rem;
    font-weight: 500;
}

/* Dashboard Workflow Panel */
.dashboard-workflow-panel {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
    margin-bottom: var(--space-8);
    overflow: hidden;
}

.workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.workflow-title {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    color: var(--gray-100);
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin: 0;
    letter-spacing: -0.025em;
}

.workflow-icon {
    width: 24px;
    height: 24px;
    stroke-width: 2;
    color: var(--primary-500);
}

.workflow-controls {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.workflow-stats {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.step-counter {
    color: var(--gray-400);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    letter-spacing: 0.025em;
}

.progress-percent {
    color: var(--primary-400);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    letter-spacing: -0.025em;
}

.workflow-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
}

.workflow-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Enhanced Workflow Progress */
.workflow-progress-enhanced {
    display: flex;
    flex-direction: column;
    padding: var(--space-8);
    gap: var(--space-4);
}

.workflow-step-card {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    padding: var(--space-8);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    transition: all var(--transition-base);
    position: relative;
    backdrop-filter: var(--backdrop-blur);
    cursor: pointer;
}

.workflow-step-card:hover {
    background: var(--glass-bg-dark);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow-sm);
    border-color: var(--gray-600);
}

.workflow-step-card.active {
    border-color: var(--primary-500);
    background: rgba(37, 99, 235, 0.1);
    box-shadow: 0 0 0 1px rgba(37, 99, 235, 0.2), var(--glass-shadow-sm);
}

.workflow-step-card.completed {
    border-color: var(--success-500);
    background: rgba(5, 150, 105, 0.1);
    box-shadow: 0 0 0 1px rgba(5, 150, 105, 0.2), var(--glass-shadow-sm);
}

.step-status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: all var(--transition-base);
}

.workflow-step-card.active .step-status-indicator {
    background: var(--primary-500);
    animation: pulse 2s infinite;
}

.workflow-step-card.completed .step-status-indicator {
    background: var(--success-500);
}

.step-content {
    flex: 1;
}

.step-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.step-icon {
    width: 28px;
    height: 28px;
    stroke-width: 2;
    color: var(--gray-400);
}

.workflow-step-card.active .step-icon {
    color: var(--primary-500);
}

.workflow-step-card.completed .step-icon {
    color: var(--success-500);
}

.step-info {
    flex: 1;
}

.step-title {
    color: var(--gray-100);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    margin-bottom: var(--space-2);
    letter-spacing: -0.025em;
}

.step-subtitle {
    color: var(--gray-400);
    font-size: var(--text-sm);
    font-weight: var(--font-normal);
    line-height: var(--leading-relaxed);
}

.step-details {
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.step-tasks {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.task-item {
    color: var(--gray-300);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    padding: var(--space-3) var(--space-4);
    background: rgba(248, 250, 252, 0.03);
    border: 1px solid rgba(203, 213, 225, 0.1);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    letter-spacing: -0.015em;
}

.task-item.completed {
    color: var(--success-400);
    background: rgba(5, 150, 105, 0.1);
    border-color: rgba(5, 150, 105, 0.2);
}

.step-timing {
    color: var(--gray-500);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: var(--space-2) var(--space-3);
    background: rgba(248, 250, 252, 0.05);
    border-radius: var(--radius-md);
    border: 1px solid rgba(203, 213, 225, 0.1);
}

.workflow-connector-enhanced {
    display: flex;
    justify-content: center;
    height: 30px;
    position: relative;
}

.connector-line {
    width: 2px;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    position: relative;
}

.connector-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 0%;
    background: var(--primary-gradient);
    transition: height var(--transition-slow);
}

/* Progress Timeline */
.progress-timeline {
    padding: var(--space-6) var(--space-8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.timeline-title {
    color: var(--white);
    font-size: 1rem;
    font-weight: 700;
    margin: 0;
}

.timeline-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-base);
}

.timeline-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.timeline-track {
    position: relative;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    margin-bottom: var(--space-4);
}

.timeline-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    width: 0%;
    transition: width var(--transition-base);
}

.timeline-markers {
    display: flex;
    justify-content: space-between;
    margin-top: var(--space-2);
}

.timeline-marker {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    font-weight: 500;
}

/* Metrics Dashboard */
.metrics-dashboard {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    box-shadow: var(--glass-shadow);
    margin-bottom: var(--space-8);
    overflow: hidden;
}

.metrics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6) var(--space-8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.metrics-title {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    color: var(--white);
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
}

.metrics-icon {
    font-size: 1.5rem;
}

.metrics-controls {
    display: flex;
    gap: var(--space-3);
}

.metrics-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-base);
}

.metrics-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    padding: var(--space-8);
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    transition: all var(--transition-base);
}

.metric-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.metric-card-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.metric-card .metric-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.metric-title {
    color: var(--white);
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.metric-value-container {
    display: flex;
    align-items: baseline;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.metric-value {
    color: var(--white);
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.metric-unit {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
    font-weight: 500;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.trend-indicator {
    font-size: 0.875rem;
}

.trend-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    font-weight: 500;
}

/* Status Timeline Detailed */
.status-timeline-detailed {
    padding: var(--space-6) var(--space-8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.02);
}

.timeline-detailed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.timeline-detailed-header h4 {
    color: var(--white);
    font-size: 1rem;
    font-weight: 700;
    margin: 0;
}

.timeline-legend {
    display: flex;
    gap: var(--space-4);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    font-weight: 500;
}

.timeline-events {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.timeline-event {
    display: flex;
    align-items: flex-start;
    gap: var(--space-4);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border-left: 3px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-base);
}

.timeline-event[data-status="completed"] {
    border-left-color: var(--success-500);
}

.timeline-event[data-status="active"] {
    border-left-color: var(--primary-500);
    background: rgba(102, 126, 234, 0.1);
}

.timeline-event[data-status="error"] {
    border-left-color: var(--error-500);
    background: rgba(239, 68, 68, 0.1);
}

.event-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 50px;
}

.event-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    margin-top: 2px;
}

.event-icon svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
    color: var(--gray-400);
}

.timeline-event[data-status="completed"] .event-icon {
    background: rgba(5, 150, 105, 0.1);
    border-color: var(--success-500);
}

.timeline-event[data-status="completed"] .event-icon svg {
    color: var(--success-500);
}

.timeline-event[data-status="active"] .event-icon {
    background: rgba(37, 99, 235, 0.1);
    border-color: var(--primary-500);
}

.timeline-event[data-status="active"] .event-icon svg {
    color: var(--primary-500);
}

.event-content {
    flex: 1;
}

.event-title {
    color: var(--white);
    font-size: 0.875rem;
    font-weight: 700;
    margin-bottom: var(--space-1);
}

.event-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
    line-height: 1.4;
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Dashboard Responsive Design */
@media (max-width: 1024px) {
    .dashboard-container {
        padding: 0 var(--space-4);
    }
    
    .workflow-progress-enhanced {
        padding: var(--space-6);
    }
    
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: var(--space-4);
    }
}

@media (max-width: 768px) {
    .dashboard-nav {
        flex-direction: column;
        gap: var(--space-4);
        align-items: stretch;
    }
    
    .nav-controls {
        justify-content: space-between;
    }
    
    .dashboard-status-bar {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .status-metrics {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }
    
    .workflow-mini-progress {
        min-width: auto;
        width: 100%;
        align-items: stretch;
    }
    
    .workflow-header {
        flex-direction: column;
        gap: var(--space-4);
        align-items: stretch;
    }
    
    .workflow-controls {
        justify-content: space-between;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .timeline-detailed-header {
        flex-direction: column;
        gap: var(--space-3);
        align-items: stretch;
    }
    
    .timeline-legend {
        justify-content: space-between;
    }
}

@media (max-width: 640px) {
    .status-metrics {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }
    
    .workflow-progress-enhanced {
        padding: var(--space-4);
    }
    
    .workflow-step-card {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: var(--space-3);
    }
    
    .step-header {
        justify-content: center;
    }
    
    .brand-section {
        flex-direction: column;
        gap: var(--space-2);
        text-align: center;
    }
}