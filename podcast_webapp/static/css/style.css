/* Readdy.ai Inspired Design System */
:root {
    /* Readdy-style color palette with purple/lavender accents */
    --color-white: #ffffff;
    --color-black: #000000;
    --color-gray-50: #fafafa;
    --color-gray-100: #f5f5f5;
    --color-gray-200: #e5e5e5;
    --color-gray-300: #d4d4d4;
    --color-gray-400: #a3a3a3;
    --color-gray-500: #737373;
    --color-gray-600: #525252;
    --color-gray-700: #404040;
    --color-gray-800: #262626;
    --color-gray-900: #171717;
    
    /* Purple/lavender accent colors inspired by Readdy.ai */
    --color-primary: #8b5cf6;
    --color-primary-light: #a78bfa;
    --color-primary-dark: #7c3aed;
    --color-accent: #c084fc;
    --color-accent-light: #ddd6fe;
    
    /* Typography */
    --font-inter: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    
    /* Font sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;
    
    /* Font weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    
    /* Border radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Layout */
    --max-width: 1200px;
    --header-height: 72px;
}

/* Reset */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-inter);
    background-color: var(--color-white);
    color: var(--color-gray-900);
    line-height: 1.6;
    font-weight: var(--font-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Readdy-style Header */
.readdy-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--color-gray-200);
    height: var(--header-height);
}

.header-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--space-6);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.brand-section {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-logo svg {
    color: var(--color-primary);
}

.brand-text {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--color-gray-900);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.nav-link {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--color-gray-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--color-gray-900);
}

.cta-button {
    background: var(--color-primary);
    color: var(--color-white);
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.cta-button:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Main Content */
.main-content {
    padding-top: var(--header-height);
}

/* Hero Section */
.hero-section {
    padding: var(--space-32) var(--space-6) var(--space-24);
    text-align: center;
    background: linear-gradient(135deg, #fdfbfb 0%, #ebedee 100%);
}

.hero-container {
    max-width: var(--max-width);
    margin: 0 auto;
}

.hero-content {
    max-width: 700px;
    margin: 0 auto;
}

.hero-title {
    font-size: var(--text-5xl);
    font-weight: var(--font-bold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-6);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--color-gray-600);
    font-weight: var(--font-normal);
    margin-bottom: var(--space-8);
    line-height: 1.5;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.btn-primary {
    background: var(--color-primary);
    color: var(--color-white);
    border: none;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.btn-primary:hover {
    background: var(--color-primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-outline {
    background: transparent;
    color: var(--color-gray-700);
    border: 2px solid var(--color-gray-300);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.btn-outline:hover {
    border-color: var(--color-primary);
    color: var(--color-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Section Container */
.section-container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.section-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--color-gray-900);
    text-align: center;
    margin-bottom: var(--space-16);
}

/* How It Works Section */
.how-it-works {
    padding: var(--space-24) 0;
    background: var(--color-white);
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-12);
}

.step-card {
    text-align: center;
    padding: var(--space-8);
}

.step-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    background: var(--color-accent-light);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-icon svg {
    color: var(--color-primary);
}

.step-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-4);
}

.step-description {
    font-size: var(--text-base);
    color: var(--color-gray-600);
    line-height: 1.6;
}

/* Design Preview Section */
.design-preview {
    padding: var(--space-24) 0;
    background: var(--color-gray-50);
}

.preview-tabs {
    display: flex;
    justify-content: center;
    gap: var(--space-2);
    margin-bottom: var(--space-12);
}

.tab-btn {
    background: var(--color-white);
    border: 1px solid var(--color-gray-300);
    color: var(--color-gray-600);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
    border-color: var(--color-primary);
}

.preview-container {
    text-align: center;
}

.preview-mockup {
    max-width: 800px;
    margin: 0 auto var(--space-6);
    background: var(--color-white);
    border-radius: var(--radius-3xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.mockup-screen {
    padding: var(--space-8);
}

.generator-form-preview .generator-form {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
}

.preview-caption {
    font-size: var(--text-sm);
    color: var(--color-gray-500);
    font-style: italic;
}

/* Features Section */
.features-section {
    padding: var(--space-24) 0;
    background: var(--color-white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-8);
}

.feature-card {
    background: var(--color-white);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    transition: all var(--transition-base);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--color-primary);
}

.feature-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--space-6);
    background: var(--color-accent-light);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon svg {
    color: var(--color-primary);
}

.feature-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--space-3);
}

.feature-description {
    font-size: var(--text-base);
    color: var(--color-gray-600);
    line-height: 1.6;
}

/* Form Styles */
.generator-form {
    background-color: var(--color-white);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: var(--space-6);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--color-gray-700);
    margin-bottom: var(--space-2);
}

.input-field,
.select-field {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid var(--color-gray-300);
    border-radius: var(--radius-xl);
    font-size: var(--text-base);
    color: var(--color-gray-900);
    background-color: var(--color-white);
    transition: all var(--transition-fast);
    font-family: var(--font-inter);
}

.input-field:focus,
.select-field:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.input-field::placeholder {
    color: var(--color-gray-400);
}

.select-field {
    cursor: pointer;
}

.generate-btn {
    width: 100%;
    padding: var(--space-5) var(--space-6);
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
    color: var(--color-white);
    border: none;
    border-radius: var(--radius-xl);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    font-family: var(--font-inter);
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.generate-btn:active {
    transform: translateY(0);
}

.btn-loading {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.hidden {
    display: none;
}

/* Status Section */
.status-section {
    padding: var(--space-16) 0;
    background: var(--color-gray-50);
}

.status-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-6);
}

.status-item {
    background-color: var(--color-white);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    text-align: center;
    transition: all var(--transition-fast);
}

.status-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.status-label {
    font-size: var(--text-sm);
    color: var(--color-gray-600);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-2);
}

.status-value {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--color-gray-900);
}

/* Legacy sections - hide completely */
.metrics-dashboard,
.content-card,
.hero-section,
.app-container {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .hero-title {
        font-size: var(--text-4xl);
    }
    
    .hero-subtitle {
        font-size: var(--text-lg);
    }
    
    .hero-cta {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-primary,
    .btn-outline {
        width: 100%;
        max-width: 280px;
    }
    
    .steps-grid,
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .header-container {
        padding: 0 var(--space-4);
    }
    
    .section-container {
        padding: 0 var(--space-4);
    }
    
    .hero-section {
        padding: var(--space-20) var(--space-4) var(--space-16);
    }
}