/* TrendCast AI - Professional Clean Design (Overleaf Style) */

/* CSS Custom Properties - Overleaf Inspired */
:root {
  /* Professional Color Palette */
  --color-primary: #138A36;        /* Overleaf Green */
  --color-primary-dark: #0F6B2A;   /* Darker Green */
  --color-primary-light: #E8F5E8;  /* Light Green Background */
  --color-secondary: #2F7D32;      /* Secondary Green */
  --color-accent: #FF6B35;         /* Orange Accent */
  --color-accent-light: #FFF3E0;   /* Light Orange */

  /* Neutral Colors */
  --color-white: #FFFFFF;
  --color-gray-50: #FAFAFA;
  --color-gray-100: #F5F5F5;
  --color-gray-200: #EEEEEE;
  --color-gray-300: #E0E0E0;
  --color-gray-400: #BDBDBD;
  --color-gray-500: #9E9E9E;
  --color-gray-600: #757575;
  --color-gray-700: #616161;
  --color-gray-800: #424242;
  --color-gray-900: #212121;

  /* Background Colors */
  --bg-primary: var(--color-white);
  --bg-secondary: var(--color-gray-50);
  --bg-tertiary: var(--color-gray-100);
  --bg-accent: var(--color-primary-light);
  --bg-card: var(--color-white);
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* Text Colors */
  --text-primary: var(--color-gray-900);
  --text-secondary: var(--color-gray-700);
  --text-tertiary: var(--color-gray-600);
  --text-muted: var(--color-gray-500);
  --text-inverse: var(--color-white);
  --text-accent: var(--color-primary);
  --text-link: var(--color-primary);

  /* Border Colors */
  --border-light: var(--color-gray-200);
  --border-medium: var(--color-gray-300);
  --border-dark: var(--color-gray-400);
  --border-accent: var(--color-primary);

  /* Social Platform Colors */
  --tiktok-red: #fe2c55;
  --instagram-gradient: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  --discord-purple: #5865f2;
  --spotify-green: #1db954;
  --youtube-red: #ff0000;
  --twitter-blue: #1da1f2;

  /* Professional Typography System - Overleaf Style */
  --font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --font-secondary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-accent: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-display: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;

  /* Enhanced Typography Scale */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */

  /* Typography Enhancements */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Font Weights */
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* Premium Spacing System - Inspired by readdy.ai */
  --space-px: 1px;
  --space-0: 0;
  --space-0-5: 0.125rem;   /* 2px */
  --space-1: 0.25rem;      /* 4px */
  --space-1-5: 0.375rem;   /* 6px */
  --space-2: 0.5rem;       /* 8px */
  --space-2-5: 0.625rem;   /* 10px */
  --space-3: 0.75rem;      /* 12px */
  --space-3-5: 0.875rem;   /* 14px */
  --space-4: 1rem;         /* 16px */
  --space-5: 1.25rem;      /* 20px */
  --space-6: 1.5rem;       /* 24px */
  --space-7: 1.75rem;      /* 28px */
  --space-8: 2rem;         /* 32px */
  --space-9: 2.25rem;      /* 36px */
  --space-10: 2.5rem;      /* 40px */
  --space-11: 2.75rem;     /* 44px */
  --space-12: 3rem;        /* 48px */
  --space-14: 3.5rem;      /* 56px */
  --space-16: 4rem;        /* 64px */
  --space-20: 5rem;        /* 80px */
  --space-24: 6rem;        /* 96px */
  --space-28: 7rem;        /* 112px */
  --space-32: 8rem;        /* 128px */

  /* Legacy spacing for compatibility */
  --space-xs: var(--space-2);
  --space-sm: var(--space-3);
  --space-md: var(--space-4);
  --space-lg: var(--space-6);
  --space-xl: var(--space-8);
  --space-2xl: var(--space-12);
  --space-3xl: var(--space-16);
  --space-4xl: var(--space-20);
  --space-5xl: var(--space-24);

  /* Enhanced Border Radius */
  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  --radius-3xl: 32px;
  --radius-full: 9999px;

  /* Professional Shadow System - Overleaf Style */
  --shadow-none: none;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-xl: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-2xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);

  /* Card Shadows */
  --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-card-hover: 0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
  --shadow-button: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-button-hover: 0 2px 4px rgba(0, 0, 0, 0.15);

  /* Focus Shadow */
  --shadow-focus: 0 0 0 3px rgba(19, 138, 54, 0.2);

  /* Professional Animation System - Overleaf Style */
  --transition-none: 0s;
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* Clean Easing Functions */
  --ease-linear: linear;
  --ease-in: ease-in;
  --ease-out: ease-out;
  --ease-in-out: ease-in-out;
  --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);

  /* Professional Timings */
  --duration-fast: 0.15s;
  --duration-normal: 0.2s;
  --duration-slow: 0.3s;
}

/* Enhanced Reset and Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background: #FFFFFF;
  color: #212121;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  margin: 0;
  padding: 0;
}

/* Premium Typography Classes */
.text-display {
  font-family: var(--font-display);
  font-weight: var(--font-bold);
  letter-spacing: var(--letter-spacing-tight);
  line-height: var(--line-height-tight);
}

.text-heading {
  font-family: var(--font-accent);
  font-weight: var(--font-semibold);
  letter-spacing: var(--letter-spacing-tight);
  line-height: var(--line-height-snug);
}

.text-body {
  font-family: var(--font-primary);
  font-weight: var(--font-normal);
  letter-spacing: var(--letter-spacing-normal);
  line-height: var(--line-height-relaxed);
}

.text-caption {
  font-family: var(--font-secondary);
  font-weight: var(--font-medium);
  letter-spacing: var(--letter-spacing-wide);
  line-height: var(--line-height-normal);
  text-transform: uppercase;
}

/* Professional Focus States - Overleaf Style */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: var(--shadow-focus);
}

/* Button Focus States */
.cta-primary:focus-visible,
.cta-secondary:focus-visible,
.nav-link:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  box-shadow: var(--shadow-focus);
}

/* Enhanced Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: var(--bg-primary);
}

.gradient-orb {
  display: none; /* Hide all animated orbs for clean Overleaf style */
}

.orb-1 {
  width: 350px;
  height: 350px;
  background: var(--gradient-primary);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  box-shadow: 0 0 100px var(--neon-pink-glow);
}

.orb-2 {
  width: 450px;
  height: 450px;
  background: var(--gradient-secondary);
  top: 60%;
  right: 10%;
  animation-delay: 8s;
  box-shadow: 0 0 120px var(--neon-blue-glow);
}

.orb-3 {
  width: 300px;
  height: 300px;
  background: var(--gradient-accent);
  bottom: 20%;
  left: 50%;
  animation-delay: 16s;
  box-shadow: 0 0 80px var(--neon-green-glow);
}

/* Additional Particle Effects */
.animated-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 25% 25%, var(--neon-pink-glow) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, var(--neon-blue-glow) 0%, transparent 50%);
  opacity: 0.3;
  animation: particleFloat 30s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  25% {
    transform: translate(40px, -40px) rotate(90deg) scale(1.1);
  }
  50% {
    transform: translate(-30px, 30px) rotate(180deg) scale(0.9);
  }
  75% {
    transform: translate(20px, -20px) rotate(270deg) scale(1.05);
  }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Professional Navigation - Overleaf Style */
.glass-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #FFFFFF;
  border-bottom: 1px solid #EEEEEE;
  padding: 0;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.glass-nav.scrolled {
  box-shadow: var(--shadow-md);
  border-bottom-color: var(--border-medium);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-weight: var(--font-semibold);
  font-size: var(--text-xl);
  transition: all var(--transition-fast);
  cursor: pointer;
  text-decoration: none;
  color: var(--text-primary);
}

.nav-brand:hover {
  color: var(--color-primary);
}

.nav-brand:active {
  transform: scale(0.98);
}

.brand-icon {
  width: 32px;
  height: 32px;
  background: #138A36;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  transition: all 0.15s ease;
}

.brand-icon:hover {
  background: #0F6B2A;
}

.brand-text {
  color: #212121;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  letter-spacing: -0.025em;
  font-weight: 600;
}

.brand-badge {
  background: var(--color-accent);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  transition: all var(--transition-fast);
}

.brand-badge:hover {
  background: var(--color-accent);
  transform: scale(1.05);
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  text-decoration: none;
  color: inherit;
  transition: transform var(--transition-fast);
}

.brand-link:hover {
  transform: scale(1.02);
}

.nav-links {
  display: flex;
  gap: var(--space-1);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  color: #616161;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.15s ease;
  position: relative;
}

.nav-link:hover {
  color: #212121;
  background: #FAFAFA;
}

.nav-link.active {
  background: #E8F5E8;
  color: #138A36;
  font-weight: 600;
}

.nav-link i {
  font-size: var(--text-sm);
}

.nav-link:active {
  transform: scale(0.98);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.search-btn,
.profile-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-btn:hover,
.profile-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
  color: var(--text-primary);
}

.search-btn:active,
.profile-btn:active {
  transform: scale(0.98);
}

.search-btn i {
  font-size: var(--text-sm);
}

.profile-avatar {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid var(--border-light);
}

.profile-btn:hover .profile-avatar {
  border-color: var(--border-medium);
}

/* Professional Hero Section - Overleaf Style */
.hero-section {
  min-height: calc(100vh - 64px);
  display: flex;
  align-items: center;
  padding: var(--space-20) 0;
  position: relative;
  background: var(--bg-primary);
  margin-top: 64px;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-16);
  align-items: center;
}

.hero-content {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: #E8F5E8;
  border: 1px solid #138A36;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 1.5rem;
  color: #138A36;
  transition: all 0.15s ease;
}

.hero-badge:hover {
  background: #138A36;
  color: white;
}

.hero-badge i {
  font-size: var(--text-sm);
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 700;
  line-height: 1.25;
  letter-spacing: -0.025em;
  margin-bottom: 1.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #212121;
}

.gradient-text {
  color: #138A36;
  display: block;
}

.hero-subtitle {
  font-size: 20px;
  color: #616161;
  margin-bottom: 2rem;
  line-height: 1.625;
  font-weight: 400;
  max-width: 90%;
}

.hero-actions {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-12);
}

.cta-primary,
.cta-secondary {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border-radius: 8px;
  font-weight: var(--font-medium);
  font-size: var(--text-base);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  text-decoration: none;
  white-space: nowrap;
}

.cta-primary {
  background: #138A36;
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cta-primary:hover {
  background: #0F6B2A;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.cta-primary:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cta-secondary {
  background: #FFFFFF;
  color: #212121;
  border-color: #E0E0E0;
}

.cta-secondary:hover {
  background: #FAFAFA;
  border-color: #BDBDBD;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.cta-secondary:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--transition-slow);
  pointer-events: none;
}

.cta-primary:hover .btn-glow {
  left: 100%;
}

/* Button Icons */
.cta-primary i,
.cta-secondary i {
  font-size: 1.2rem;
  transition: transform var(--transition-fast);
}

.cta-primary:hover i {
  transform: translateX(2px) scale(1.1);
}

.cta-secondary:hover i {
  transform: scale(1.2) rotate(90deg);
}

.hero-stats {
  display: flex;
  gap: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #138A36;
  display: block;
}

.stat-label {
  color: #616161;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Hero Visual */
.hero-visual {
  position: relative;
  height: 500px;
}

.floating-cards {
  position: relative;
  height: 100%;
}

.trend-card {
  position: absolute;
  background: #FFFFFF;
  border: 1px solid #E0E0E0;
  border-radius: 12px;
  padding: 1rem;
  min-width: 200px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: cardFloat 6s ease-in-out infinite;
}

.card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.card-2 {
  top: 50%;
  right: 20%;
  animation-delay: 2s;
}

.card-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: 4s;
}

@keyframes cardFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.card-icon {
  font-size: 2rem;
  margin-bottom: var(--space-sm);
}

.card-title {
  font-weight: 600;
  margin-bottom: var(--space-xs);
  font-size: 0.875rem;
}

.card-metric {
  color: var(--neon-green);
  font-weight: 500;
  font-size: 0.75rem;
}

/* Audio Visualizer */
.audio-visualizer {
  position: absolute;
  bottom: 10%;
  right: 10%;
  display: flex;
  align-items: end;
  gap: 3px;
  height: 60px;
}

.wave-bar {
  width: 4px;
  background: var(--gradient-accent);
  border-radius: 2px;
  animation: wave 1.5s ease-in-out infinite;
}

.wave-bar:nth-child(1) { animation-delay: 0s; height: 20px; }
.wave-bar:nth-child(2) { animation-delay: 0.1s; height: 30px; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; height: 40px; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; height: 50px; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; height: 35px; }
.wave-bar:nth-child(6) { animation-delay: 0.5s; height: 45px; }
.wave-bar:nth-child(7) { animation-delay: 0.6s; height: 25px; }
.wave-bar:nth-child(8) { animation-delay: 0.7s; height: 15px; }

@keyframes wave {
  0%, 100% { transform: scaleY(0.5); }
  50% { transform: scaleY(1.2); }
}

/* Animations */
.bounce-in {
  animation: bounceIn 0.8s ease;
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

.slide-up {
  animation: slideUp 0.6s ease;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.neon-glow {
  filter: drop-shadow(0 0 10px currentColor);
}

/* Professional Card System - Overleaf Style */
.glass-card {
  background: #FFFFFF;
  border: 1px solid #EEEEEE;
  border-radius: 12px;
  transition: all 0.15s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.glass-card:hover {
  border-color: #E0E0E0;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
}

/* Neumorphism Elements */
.neuro-card {
  background: linear-gradient(145deg, #1e1e1e, #0a0a0a);
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.4),
    -20px -20px 40px rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-2xl);
  transition: all var(--transition-normal);
}

.neuro-card:hover {
  box-shadow:
    25px 25px 50px rgba(0, 0, 0, 0.5),
    -25px -25px 50px rgba(255, 255, 255, 0.03),
    inset 0 0 20px rgba(255, 0, 110, 0.1);
}

/* Sections */
.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 1.5rem;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: #212121;
}

.section-subtitle {
  font-size: 1.125rem;
  color: #616161;
  max-width: 600px;
  margin: 0 auto;
}

/* Professional Trending Section - Overleaf Style */
.trending-section {
  background: #FAFAFA;
  padding: 5rem 0;
}

.trending-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.trending-item {
  padding: 1.5rem;
  position: relative;
  transition: all 0.2s ease;
}

/* Removed complex effects for clean Overleaf style */

.trending-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 0.025em;
  margin-bottom: 1rem;
  background: #E8F5E8;
  border: 1px solid #138A36;
  color: #138A36;
  transition: all 0.2s ease;
  text-transform: uppercase;
}

.trending-badge:hover {
  background: #138A36;
  color: white;
}

.trending-badge .fab.fa-tiktok {
  color: var(--tiktok-red);
  filter: drop-shadow(0 0 4px var(--tiktok-red));
}

.trending-badge .fab.fa-instagram {
  background: var(--instagram-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 4px #e6683c);
}

.trending-badge .fab.fa-youtube {
  color: var(--youtube-red);
  filter: drop-shadow(0 0 4px var(--youtube-red));
}

.trending-badge .fab.fa-spotify {
  color: var(--spotify-green);
  filter: drop-shadow(0 0 4px var(--spotify-green));
}

.trending-content h3 {
  font-size: 1.4rem;
  font-weight: 700;
  letter-spacing: -0.025em;
  line-height: 1.25;
  margin-bottom: 0.75rem;
  color: #212121;
  transition: color 0.15s ease;
}

.trending-item:hover .trending-content h3 {
  color: #138A36;
}

.trending-content p {
  color: #616161;
  margin-bottom: 1.5rem;
  line-height: 1.625;
  font-size: 1rem;
  font-weight: 400;
}

.trending-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.stat i {
  color: var(--neon-pink);
}

.analyze-btn {
  background: #138A36;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.analyze-btn:hover {
  background: #0F6B2A;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.analyze-btn:active {
  transform: translateY(0);
}

.analyze-btn i {
  font-size: 1rem;
}

/* Features Section */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: #FFFFFF;
  border: 1px solid #EEEEEE;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.feature-card:hover {
  transform: translateY(-2px);
  border-color: #E0E0E0;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
}

.feature-icon {
  width: 64px;
  height: 64px;
  background: #138A36;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #212121;
}

.feature-description {
  color: #616161;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.feature-tags {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* CTA Section */
.cta-section {
  background: var(--gradient-hero);
  position: relative;
  overflow: hidden;
}

.cta-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-3xl) var(--space-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: center;
}

.cta-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  margin-bottom: var(--space-md);
  color: white;
  font-family: var(--font-accent);
}

.cta-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-2xl);
}

.cta-button {
  background: white;
  color: var(--bg-primary);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-lg) var(--space-2xl);
  font-size: 1.125rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  box-shadow: var(--shadow-xl);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.floating-emojis {
  position: relative;
  height: 300px;
}

.emoji {
  position: absolute;
  font-size: 3rem;
  animation: emojiFloat 4s ease-in-out infinite;
}

.emoji:nth-child(1) { top: 10%; left: 20%; animation-delay: 0s; }
.emoji:nth-child(2) { top: 30%; right: 10%; animation-delay: 0.8s; }
.emoji:nth-child(3) { bottom: 40%; left: 10%; animation-delay: 1.6s; }
.emoji:nth-child(4) { bottom: 20%; right: 30%; animation-delay: 2.4s; }
.emoji:nth-child(5) { top: 60%; left: 50%; animation-delay: 3.2s; }

@keyframes emojiFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Professional Floating Action Button - Overleaf Style */
.fab {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: #138A36;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab:hover {
  background: #0F6B2A;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2), 0 3px 6px rgba(0, 0, 0, 0.15);
}

.fab:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.fab i {
  transition: transform var(--transition-fast);
  position: relative;
  z-index: 1;
}

.fab:hover i {
  transform: rotate(90deg) scale(1.1);
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Loading States and Skeleton Screens */
.loading-skeleton {
  background: linear-gradient(90deg,
    var(--bg-secondary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.skeleton-text {
  height: 1rem;
  margin-bottom: var(--space-sm);
}

.skeleton-text.large {
  height: 1.5rem;
}

.skeleton-text.small {
  height: 0.75rem;
  width: 60%;
}

.skeleton-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}

.skeleton-card {
  height: 200px;
  border-radius: var(--radius-xl);
}

/* Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--neon-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse Loading */
.pulse-loading {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Button Loading State */
.btn-loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Micro-interactions */
.micro-bounce {
  animation: microBounce 0.6s ease;
}

@keyframes microBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.micro-shake {
  animation: microShake 0.5s ease;
}

@keyframes microShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Success/Error States */
.state-success {
  border-color: var(--neon-green) !important;
  box-shadow: 0 0 20px var(--neon-green-glow) !important;
}

.state-error {
  border-color: var(--neon-pink) !important;
  box-shadow: 0 0 20px var(--neon-pink-glow) !important;
  animation: microShake 0.5s ease;
}

.state-warning {
  border-color: var(--electric-yellow) !important;
  box-shadow: 0 0 20px rgba(255, 255, 0, 0.3) !important;
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Enhanced Mobile Optimizations */
@media (max-width: 768px) {
  /* Reduce motion for better performance on mobile */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Mobile Navigation */
  .nav-container {
    padding: 0 var(--space-md);
  }

  .nav-links {
    gap: var(--space-sm);
  }

  .nav-link span {
    display: none;
  }

  .nav-link {
    min-height: 44px;
    padding: var(--space-sm);
    border-radius: 50%;
    width: 44px;
    justify-content: center;
  }

  .nav-link[data-tooltip]:hover::before {
    bottom: -35px;
    font-size: 0.7rem;
  }

  /* Mobile Hero Section */
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    text-align: center;
  }

  .hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    max-width: 100%;
  }

  .hero-actions {
    flex-direction: column;
    gap: var(--space-md);
  }

  .cta-primary,
  .cta-secondary {
    width: 100%;
    justify-content: center;
    min-height: 52px;
    padding: var(--space-md) var(--space-xl);
  }

  .hero-features {
    justify-content: center;
  }

  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-md);
  }

  /* Mobile Cards */
  .trending-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .enhanced-card {
    margin: 0 var(--space-sm);
  }

  .card-header {
    padding: var(--space-md) var(--space-md) var(--space-sm);
  }

  .trending-content {
    padding: 0 var(--space-md) var(--space-md);
  }

  .trending-tags {
    gap: var(--space-xs);
  }

  .tag {
    font-size: 0.65rem;
    padding: 2px var(--space-xs);
  }

  .stat-group {
    gap: var(--space-md);
  }

  .card-actions {
    flex-direction: column;
    gap: var(--space-sm);
    padding: var(--space-md);
  }

  .analyze-btn,
  .create-btn {
    width: 100%;
    justify-content: center;
    min-height: 44px;
  }

  /* Mobile Modal */
  .modal-content {
    width: 95%;
    margin: var(--space-md);
  }

  .demo-features {
    gap: var(--space-md);
  }

  .feature-item {
    font-size: 0.8rem;
  }

  .feature-item i {
    font-size: 1.2rem;
  }

  /* Optimize glassmorphism for mobile */
  .glass-card {
    backdrop-filter: blur(10px) saturate(150%);
  }

  .glass-nav {
    backdrop-filter: blur(15px) saturate(150%);
  }

  /* Reduce shadow complexity on mobile */
  .shadow-depth-4,
  .shadow-depth-5 {
    box-shadow: var(--shadow-depth-2);
  }

  /* Optimize animations for 60fps */
  .gradient-orb {
    filter: blur(40px) saturate(120%);
  }

  .animated-bg {
    background-size: 200% 200%;
  }

  /* Optimize FAB for mobile */
  .fab {
    width: 56px;
    height: 56px;
    bottom: var(--space-lg);
    right: var(--space-lg);
  }

  /* Mobile Typography */
  .section-title {
    font-size: 1.8rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  /* Mobile Spacing */
  .section-container {
    padding: 0 var(--space-md);
  }

  .hero-section {
    padding: var(--space-3xl) 0 var(--space-2xl);
  }

  .trending-section {
    padding: var(--space-2xl) 0;
  }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .brand-icon,
  .fab {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-glass: rgba(255, 255, 255, 0.06);
    --bg-glass-strong: rgba(255, 255, 255, 0.1);
    --glass-border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .animated-bg,
  .gradient-orb {
    animation: none;
  }

  .hero-badge i {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --glass-border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .glass-card {
    border-width: 2px;
  }
}

/* Enhanced Navigation Styles */
.nav-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 20px;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: var(--radius-sm);
  transition: transform var(--transition-normal);
}

.nav-link.active .nav-indicator {
  transform: translateX(-50%) scaleX(1);
  box-shadow: var(--shadow-neon-blue);
}

.nav-link[data-tooltip]:hover::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-glass-darker);
  color: var(--text-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  backdrop-filter: var(--glass-backdrop);
  border: var(--glass-border);
}

/* Enhanced Hero Styles */
.badge-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--neon-pink-glow);
  border-radius: var(--radius-full);
  animation: badgePulse 2s ease-in-out infinite;
  opacity: 0.3;
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
}

.subtitle-highlight {
  color: var(--neon-blue);
  font-weight: 600;
}

.hero-features {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-xl);
  flex-wrap: wrap;
}

.feature-tag {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  background: var(--bg-glass);
  backdrop-filter: var(--glass-backdrop);
  border: var(--glass-border);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.feature-tag:hover {
  color: var(--neon-pink);
  border-color: var(--neon-pink-soft);
  transform: translateY(-2px);
}

.feature-tag i {
  font-size: 0.9rem;
  color: var(--neon-pink);
}

/* Enhanced Button Styles */
.btn-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  border-radius: var(--radius-full);
  pointer-events: none;
}

.btn-particles::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: var(--neon-pink);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: particleFloat1 3s ease-in-out infinite;
}

.btn-particles::after {
  content: '';
  position: absolute;
  top: 30%;
  right: 20%;
  width: 3px;
  height: 3px;
  background: var(--neon-blue);
  border-radius: 50%;
  animation: particleFloat2 2.5s ease-in-out infinite;
}

@keyframes particleFloat1 {
  0%, 100% { transform: translate(-50%, -50%) translateY(0); opacity: 0; }
  50% { transform: translate(-50%, -50%) translateY(-10px); opacity: 1; }
}

@keyframes particleFloat2 {
  0%, 100% { transform: translateY(0); opacity: 0; }
  50% { transform: translateY(-8px); opacity: 1; }
}

.btn-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-full);
  overflow: hidden;
  pointer-events: none;
}

/* Enhanced Card Styles */
.enhanced-card {
  padding: 0;
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
}

.enhanced-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-accent);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
  transform-origin: left;
}

.enhanced-card:hover::before {
  transform: scaleX(1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-lg) var(--space-md);
}

.trending-rank {
  background: var(--gradient-secondary);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 0.9rem;
  box-shadow: var(--shadow-depth-2);
}

.badge-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-full);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.tiktok-badge:hover .badge-glow {
  background: var(--tiktok-red);
  opacity: 0.2;
  box-shadow: 0 0 20px var(--tiktok-red);
}

.instagram-badge:hover .badge-glow {
  background: #e6683c;
  opacity: 0.2;
  box-shadow: 0 0 20px #e6683c;
}

.trending-content {
  padding: 0 var(--space-lg) var(--space-lg);
}

.trending-tags {
  display: flex;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
  flex-wrap: wrap;
}

.tag {
  background: var(--bg-glass);
  color: var(--text-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.7rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-fast);
}

.tag:hover {
  color: var(--neon-pink);
  border-color: var(--neon-pink-soft);
}

.stat-group {
  display: flex;
  gap: var(--space-lg);
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
}

.stat-number {
  font-weight: 800;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.growth-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: 0.8rem;
  font-weight: 700;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
}

.growth-indicator.positive {
  color: var(--neon-green);
  background: rgba(57, 255, 20, 0.1);
}

.card-actions {
  display: flex;
  gap: var(--space-sm);
  padding: var(--space-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.create-btn {
  background: #FFFFFF;
  border: 1px solid #E0E0E0;
  color: #616161;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  flex: 1;
  justify-content: center;
}

.create-btn:hover {
  color: #138A36;
  border-color: #138A36;
  background: #E8F5E8;
}

/* Print Styles */
@media print {
  .animated-bg,
  .glass-nav,
  .fab {
    display: none !important;
  }

  .glass-card {
    background: white !important;
    color: black !important;
    border: 1px solid #ccc !important;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 var(--space-md);
  }

  .nav-links {
    display: none;
  }

  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-2xl);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .cta-container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .trending-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .section-container {
    padding: 3rem 1rem;
  }
}

@media (max-width: 480px) {
  .hero-visual {
    height: 300px;
  }

  .floating-cards .trend-card {
    min-width: 150px;
    padding: 1rem;
  }

  .fab {
    bottom: 1rem;
    right: 1rem;
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
}
