/* TrendCast AI - Gen Z Pop Culture Platform - Enhanced Visual Quality */

/* CSS Custom Properties - Enhanced */
:root {
  /* Enhanced Gradient System with Mesh Overlays */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);

  /* Advanced Gradient Meshes */
  --gradient-mesh-1: radial-gradient(circle at 20% 80%, #667eea 0%, transparent 50%),
                     radial-gradient(circle at 80% 20%, #f093fb 0%, transparent 50%),
                     radial-gradient(circle at 40% 40%, #00f2fe 0%, transparent 50%);
  --gradient-mesh-2: conic-gradient(from 0deg at 50% 50%, #ff006e, #00f5ff, #39ff14, #bf00ff, #ff006e);
  --gradient-animated: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);

  /* Neon Colors with Enhanced Variants */
  --neon-pink: #ff006e;
  --neon-pink-soft: rgba(255, 0, 110, 0.6);
  --neon-pink-glow: rgba(255, 0, 110, 0.3);
  --neon-blue: #00f5ff;
  --neon-blue-soft: rgba(0, 245, 255, 0.6);
  --neon-blue-glow: rgba(0, 245, 255, 0.3);
  --neon-green: #39ff14;
  --neon-green-soft: rgba(57, 255, 20, 0.6);
  --neon-green-glow: rgba(57, 255, 20, 0.3);
  --neon-purple: #bf00ff;
  --electric-yellow: #ffff00;
  --cyber-orange: #ff4500;

  /* Enhanced Dark Theme with Depth */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --bg-quaternary: #3a3a3a;
  --bg-glass: rgba(255, 255, 255, 0.08);
  --bg-glass-strong: rgba(255, 255, 255, 0.12);
  --bg-glass-dark: rgba(0, 0, 0, 0.3);
  --bg-glass-darker: rgba(0, 0, 0, 0.5);

  /* Enhanced Text Colors with Better Contrast */
  --text-primary: #ffffff;
  --text-secondary: #b8b8b8;
  --text-tertiary: #888888;
  --text-accent: #ff006e;
  --text-muted: #666666;
  --text-inverse: #0a0a0a;

  /* Social Platform Colors */
  --tiktok-red: #fe2c55;
  --instagram-gradient: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  --discord-purple: #5865f2;
  --spotify-green: #1db954;
  --youtube-red: #ff0000;
  --twitter-blue: #1da1f2;

  /* Enhanced Typography System */
  --font-primary: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-secondary: 'Nunito', 'Poppins', sans-serif;
  --font-accent: 'Montserrat', 'Poppins', sans-serif;

  /* Typography Enhancements */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;

  /* Enhanced Spacing System */
  --space-xs: 0.5rem;
  --space-sm: 0.75rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 5rem;
  --space-5xl: 6rem;

  /* Enhanced Border Radius */
  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  --radius-3xl: 32px;
  --radius-full: 9999px;

  /* Advanced Shadow System */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 24px 96px rgba(0, 0, 0, 0.3);

  /* Layered Shadow System */
  --shadow-depth-1: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-depth-2: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-depth-3: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --shadow-depth-4: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  --shadow-depth-5: 0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22);

  /* Neon Shadow System */
  --shadow-neon-pink: 0 0 20px var(--neon-pink-glow), 0 0 40px var(--neon-pink-glow), 0 0 60px var(--neon-pink-glow);
  --shadow-neon-blue: 0 0 20px var(--neon-blue-glow), 0 0 40px var(--neon-blue-glow), 0 0 60px var(--neon-blue-glow);
  --shadow-neon-green: 0 0 20px var(--neon-green-glow), 0 0 40px var(--neon-green-glow), 0 0 60px var(--neon-green-glow);

  /* Glassmorphism Enhancement */
  --glass-border: 1px solid rgba(255, 255, 255, 0.18);
  --glass-border-strong: 1px solid rgba(255, 255, 255, 0.25);
  --glass-backdrop: blur(20px) saturate(180%);
  --glass-backdrop-strong: blur(40px) saturate(200%);

  /* Animation Timing */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;
  --transition-bounce: 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Enhanced Reset and Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-primary);
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1;
  font-kerning: normal;
}

/* Enhanced Focus States */
*:focus {
  outline: 2px solid var(--neon-pink);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--neon-pink);
  outline-offset: 2px;
}

/* Enhanced Animated Background */
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
  background: var(--gradient-mesh-1);
  background-size: 400% 400%;
  animation: gradientShift 20s ease infinite;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(80px) saturate(150%);
  opacity: 0.6;
  animation: float 25s ease-in-out infinite;
  will-change: transform;
}

.orb-1 {
  width: 350px;
  height: 350px;
  background: var(--gradient-primary);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
  box-shadow: 0 0 100px var(--neon-pink-glow);
}

.orb-2 {
  width: 450px;
  height: 450px;
  background: var(--gradient-secondary);
  top: 60%;
  right: 10%;
  animation-delay: 8s;
  box-shadow: 0 0 120px var(--neon-blue-glow);
}

.orb-3 {
  width: 300px;
  height: 300px;
  background: var(--gradient-accent);
  bottom: 20%;
  left: 50%;
  animation-delay: 16s;
  box-shadow: 0 0 80px var(--neon-green-glow);
}

/* Additional Particle Effects */
.animated-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 25% 25%, var(--neon-pink-glow) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, var(--neon-blue-glow) 0%, transparent 50%);
  opacity: 0.3;
  animation: particleFloat 30s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
  }
  25% {
    transform: translate(40px, -40px) rotate(90deg) scale(1.1);
  }
  50% {
    transform: translate(-30px, 30px) rotate(180deg) scale(0.9);
  }
  75% {
    transform: translate(20px, -20px) rotate(270deg) scale(1.05);
  }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Enhanced Navigation */
.glass-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--bg-glass-darker);
  backdrop-filter: var(--glass-backdrop-strong);
  border-bottom: var(--glass-border);
  padding: var(--space-md) 0;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-depth-2);
}

.glass-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    var(--neon-pink-glow) 0%,
    transparent 20%,
    transparent 80%,
    var(--neon-blue-glow) 100%);
  opacity: 0.1;
  pointer-events: none;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-weight: 700;
  font-size: 1.5rem;
  transition: transform var(--transition-fast);
}

.nav-brand:hover {
  transform: scale(1.02);
}

.brand-icon {
  width: 44px;
  height: 44px;
  background: var(--gradient-accent);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.3rem;
  box-shadow: var(--shadow-depth-2);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.brand-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--transition-slow);
}

.brand-icon:hover::before {
  left: 100%;
}

.brand-icon:hover {
  box-shadow: var(--shadow-neon-blue);
  transform: translateY(-1px);
}

.brand-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: var(--font-accent);
  letter-spacing: var(--letter-spacing-tight);
  font-weight: 800;
}

.brand-badge {
  background: var(--neon-pink);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wider);
  box-shadow: var(--shadow-depth-1);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.brand-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform var(--transition-slow);
}

.brand-badge:hover::before {
  transform: translateX(100%);
}

.brand-badge:hover {
  box-shadow: var(--shadow-neon-pink);
  transform: scale(1.05);
}

.nav-links {
  display: flex;
  gap: var(--space-md);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.9rem;
  letter-spacing: var(--letter-spacing-wide);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-glass);
  opacity: 0;
  transition: opacity var(--transition-normal);
  border-radius: var(--radius-full);
}

.nav-link:hover::before {
  opacity: 1;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-depth-1);
}

.nav-link.active {
  background: var(--bg-glass-strong);
  box-shadow: var(--shadow-depth-2);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: var(--radius-sm);
  box-shadow: var(--shadow-neon-blue);
}

.nav-link i {
  font-size: 1rem;
  transition: transform var(--transition-fast);
}

.nav-link:hover i {
  transform: scale(1.1);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.search-btn,
.profile-btn {
  width: 44px;
  height: 44px;
  border: var(--glass-border);
  border-radius: var(--radius-lg);
  background: var(--bg-glass);
  backdrop-filter: var(--glass-backdrop);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.search-btn::before,
.profile-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-glass-strong);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.search-btn:hover::before,
.profile-btn:hover::before {
  opacity: 1;
}

.search-btn:hover,
.profile-btn:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-depth-3);
  border-color: var(--neon-pink-soft);
}

.search-btn:active,
.profile-btn:active {
  transform: translateY(0) scale(0.98);
}

.search-btn i {
  font-size: 1.1rem;
  transition: transform var(--transition-fast);
}

.search-btn:hover i {
  transform: rotate(90deg) scale(1.1);
}

.profile-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--glass-border-strong);
  transition: all var(--transition-normal);
}

.profile-btn:hover .profile-avatar {
  border-color: var(--neon-pink);
  box-shadow: var(--shadow-neon-pink);
}

/* Enhanced Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: var(--space-4xl) 0;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(255, 0, 110, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4xl);
  align-items: center;
  position: relative;
}

.hero-content {
  z-index: 2;
  animation: heroContentSlide 1s ease-out;
}

@keyframes heroContentSlide {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  background: var(--bg-glass-strong);
  backdrop-filter: var(--glass-backdrop-strong);
  border: var(--glass-border-strong);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: var(--space-xl);
  color: var(--neon-pink);
  box-shadow: var(--shadow-depth-2);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.hero-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--neon-pink-glow), transparent);
  transition: left var(--transition-slow);
}

.hero-badge:hover::before {
  left: 100%;
}

.hero-badge:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-depth-3), var(--shadow-neon-pink);
}

.hero-badge i {
  font-size: 1rem;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.2) rotate(180deg); }
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: 900;
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin-bottom: var(--space-xl);
  font-family: var(--font-accent);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.gradient-text {
  background: var(--gradient-animated);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
  animation: gradientShift 8s ease infinite;
  position: relative;
}

.gradient-text::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-hero);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0;
  animation: textGlow 3s ease-in-out infinite alternate;
}

@keyframes textGlow {
  from { opacity: 0; }
  to { opacity: 0.3; }
}

.hero-subtitle {
  font-size: 1.3rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-2xl);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-normal);
  font-weight: 400;
  max-width: 90%;
}

.hero-actions {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.cta-primary,
.cta-secondary {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-2xl);
  border-radius: var(--radius-full);
  font-weight: 700;
  font-size: 1.1rem;
  letter-spacing: var(--letter-spacing-wide);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  border: none;
  backdrop-filter: var(--glass-backdrop);
  will-change: transform;
}

.cta-primary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: var(--shadow-depth-3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.cta-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%) skewX(-15deg);
  transition: transform var(--transition-slow);
}

.cta-primary:hover::before {
  transform: translateX(100%) skewX(-15deg);
}

.cta-primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-depth-5), var(--shadow-neon-pink);
}

.cta-primary:active {
  transform: translateY(-1px) scale(0.98);
}

.cta-secondary {
  background: var(--bg-glass-strong);
  color: var(--text-primary);
  border: 2px solid var(--glass-border-strong);
  backdrop-filter: var(--glass-backdrop-strong);
}

.cta-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-glass-strong);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.cta-secondary:hover::before {
  opacity: 1;
}

.cta-secondary:hover {
  transform: translateY(-3px) scale(1.02);
  border-color: var(--neon-blue-soft);
  box-shadow: var(--shadow-depth-3), var(--shadow-neon-blue);
}

.cta-secondary:active {
  transform: translateY(-1px) scale(0.98);
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--transition-slow);
  pointer-events: none;
}

.cta-primary:hover .btn-glow {
  left: 100%;
}

/* Button Icons */
.cta-primary i,
.cta-secondary i {
  font-size: 1.2rem;
  transition: transform var(--transition-fast);
}

.cta-primary:hover i {
  transform: translateX(2px) scale(1.1);
}

.cta-secondary:hover i {
  transform: scale(1.2) rotate(90deg);
}

.hero-stats {
  display: flex;
  gap: var(--space-2xl);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
}

/* Hero Visual */
.hero-visual {
  position: relative;
  height: 500px;
}

.floating-cards {
  position: relative;
  height: 100%;
}

.trend-card {
  position: absolute;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  min-width: 200px;
  animation: cardFloat 6s ease-in-out infinite;
}

.card-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.card-2 {
  top: 50%;
  right: 20%;
  animation-delay: 2s;
}

.card-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: 4s;
}

@keyframes cardFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.card-icon {
  font-size: 2rem;
  margin-bottom: var(--space-sm);
}

.card-title {
  font-weight: 600;
  margin-bottom: var(--space-xs);
  font-size: 0.875rem;
}

.card-metric {
  color: var(--neon-green);
  font-weight: 500;
  font-size: 0.75rem;
}

/* Audio Visualizer */
.audio-visualizer {
  position: absolute;
  bottom: 10%;
  right: 10%;
  display: flex;
  align-items: end;
  gap: 3px;
  height: 60px;
}

.wave-bar {
  width: 4px;
  background: var(--gradient-accent);
  border-radius: 2px;
  animation: wave 1.5s ease-in-out infinite;
}

.wave-bar:nth-child(1) { animation-delay: 0s; height: 20px; }
.wave-bar:nth-child(2) { animation-delay: 0.1s; height: 30px; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; height: 40px; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; height: 50px; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; height: 35px; }
.wave-bar:nth-child(6) { animation-delay: 0.5s; height: 45px; }
.wave-bar:nth-child(7) { animation-delay: 0.6s; height: 25px; }
.wave-bar:nth-child(8) { animation-delay: 0.7s; height: 15px; }

@keyframes wave {
  0%, 100% { transform: scaleY(0.5); }
  50% { transform: scaleY(1.2); }
}

/* Animations */
.bounce-in {
  animation: bounceIn 0.8s ease;
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

.slide-up {
  animation: slideUp 0.6s ease;
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.neon-glow {
  filter: drop-shadow(0 0 10px currentColor);
}

/* Enhanced Glass Card Effect */
.glass-card {
  background: var(--bg-glass);
  backdrop-filter: var(--glass-backdrop-strong);
  border: var(--glass-border);
  border-radius: var(--radius-2xl);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
}

.glass-card:hover::before {
  opacity: 1;
}

.glass-card:hover {
  background: var(--bg-glass-strong);
  border-color: var(--glass-border-strong);
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-depth-4);
}

/* Neumorphism Elements */
.neuro-card {
  background: linear-gradient(145deg, #1e1e1e, #0a0a0a);
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.4),
    -20px -20px 40px rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-2xl);
  transition: all var(--transition-normal);
}

.neuro-card:hover {
  box-shadow:
    25px 25px 50px rgba(0, 0, 0, 0.5),
    -25px -25px 50px rgba(255, 255, 255, 0.03),
    inset 0 0 20px rgba(255, 0, 110, 0.1);
}

/* Sections */
.section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-3xl) var(--space-lg);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.section-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  margin-bottom: var(--space-md);
  font-family: var(--font-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Trending Section */
.trending-section {
  background: linear-gradient(180deg, transparent 0%, rgba(255, 255, 255, 0.02) 100%);
}

.trending-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
}

.trending-item {
  padding: var(--space-2xl);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.trending-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    var(--neon-pink-glow),
    var(--neon-blue-glow),
    transparent);
  transition: left var(--transition-slow);
  opacity: 0.3;
}

.trending-item:hover::before {
  left: 100%;
}

.trending-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-accent);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
  transform-origin: left;
}

.trending-item:hover::after {
  transform: scaleX(1);
}

.trending-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  font-weight: 700;
  letter-spacing: var(--letter-spacing-wide);
  margin-bottom: var(--space-lg);
  background: var(--bg-glass);
  backdrop-filter: var(--glass-backdrop);
  border: var(--glass-border);
  transition: all var(--transition-normal);
  text-transform: uppercase;
}

.trending-badge:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-depth-2);
}

.trending-badge .fab.fa-tiktok {
  color: var(--tiktok-red);
  filter: drop-shadow(0 0 4px var(--tiktok-red));
}

.trending-badge .fab.fa-instagram {
  background: var(--instagram-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 0 4px #e6683c);
}

.trending-badge .fab.fa-youtube {
  color: var(--youtube-red);
  filter: drop-shadow(0 0 4px var(--youtube-red));
}

.trending-badge .fab.fa-spotify {
  color: var(--spotify-green);
  filter: drop-shadow(0 0 4px var(--spotify-green));
}

.trending-content h3 {
  font-size: 1.4rem;
  font-weight: 800;
  letter-spacing: var(--letter-spacing-tight);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-sm);
  color: var(--text-primary);
  transition: color var(--transition-fast);
}

.trending-item:hover .trending-content h3 {
  color: var(--neon-pink);
  text-shadow: 0 0 10px var(--neon-pink-glow);
}

.trending-content p {
  color: var(--text-secondary);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-relaxed);
  font-size: 1rem;
  font-weight: 400;
}

.trending-stats {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.stat {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.stat i {
  color: var(--neon-pink);
}

.analyze-btn {
  background: var(--gradient-accent);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-md) var(--space-xl);
  color: white;
  font-weight: 700;
  font-size: 0.9rem;
  letter-spacing: var(--letter-spacing-wide);
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-depth-2);
}

.analyze-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.analyze-btn:hover::before {
  left: 100%;
}

.analyze-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-depth-4), var(--shadow-neon-blue);
}

.analyze-btn:active {
  transform: translateY(-1px) scale(0.98);
}

.analyze-btn i {
  font-size: 1rem;
  transition: transform var(--transition-fast);
}

.analyze-btn:hover i {
  transform: rotate(180deg) scale(1.1);
}

/* Features Section */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-xl);
}

.feature-card {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-10px);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-xl);
}

.feature-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.feature-card:hover::after {
  transform: scaleX(1);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-secondary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-lg);
  font-size: 2rem;
  color: white;
  box-shadow: var(--shadow-lg);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--space-md);
  color: var(--text-primary);
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-lg);
}

.feature-tags {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* CTA Section */
.cta-section {
  background: var(--gradient-hero);
  position: relative;
  overflow: hidden;
}

.cta-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-3xl) var(--space-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  align-items: center;
}

.cta-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  margin-bottom: var(--space-md);
  color: white;
  font-family: var(--font-accent);
}

.cta-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--space-2xl);
}

.cta-button {
  background: white;
  color: var(--bg-primary);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-lg) var(--space-2xl);
  font-size: 1.125rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--space-md);
  box-shadow: var(--shadow-xl);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.floating-emojis {
  position: relative;
  height: 300px;
}

.emoji {
  position: absolute;
  font-size: 3rem;
  animation: emojiFloat 4s ease-in-out infinite;
}

.emoji:nth-child(1) { top: 10%; left: 20%; animation-delay: 0s; }
.emoji:nth-child(2) { top: 30%; right: 10%; animation-delay: 0.8s; }
.emoji:nth-child(3) { bottom: 40%; left: 10%; animation-delay: 1.6s; }
.emoji:nth-child(4) { bottom: 20%; right: 30%; animation-delay: 2.4s; }
.emoji:nth-child(5) { top: 60%; left: 50%; animation-delay: 3.2s; }

@keyframes emojiFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* Enhanced Floating Action Button */
.fab {
  position: fixed;
  bottom: var(--space-2xl);
  right: var(--space-2xl);
  width: 68px;
  height: 68px;
  border-radius: 50%;
  background: var(--gradient-secondary);
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1.6rem;
  cursor: pointer;
  box-shadow: var(--shadow-depth-4);
  transition: all var(--transition-normal);
  z-index: 1000;
  backdrop-filter: var(--glass-backdrop);
  position: relative;
  overflow: hidden;
}

.fab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: conic-gradient(from 0deg, var(--neon-pink), var(--neon-blue), var(--neon-green), var(--neon-pink));
  border-radius: 50%;
  padding: 2px;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.fab:hover::before {
  opacity: 1;
  animation: rotate 2s linear infinite;
}

.fab::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all var(--transition-fast);
}

.fab:active::after {
  width: 100%;
  height: 100%;
}

.fab:hover {
  transform: scale(1.15) rotate(5deg);
  box-shadow: var(--shadow-depth-5), var(--shadow-neon-pink);
  border-color: var(--neon-pink-soft);
}

.fab:active {
  transform: scale(1.05) rotate(0deg);
}

.fab i {
  transition: transform var(--transition-fast);
  position: relative;
  z-index: 1;
}

.fab:hover i {
  transform: rotate(90deg) scale(1.1);
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Loading States and Skeleton Screens */
.loading-skeleton {
  background: linear-gradient(90deg,
    var(--bg-secondary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.skeleton-text {
  height: 1rem;
  margin-bottom: var(--space-sm);
}

.skeleton-text.large {
  height: 1.5rem;
}

.skeleton-text.small {
  height: 0.75rem;
  width: 60%;
}

.skeleton-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}

.skeleton-card {
  height: 200px;
  border-radius: var(--radius-xl);
}

/* Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--bg-tertiary);
  border-top: 3px solid var(--neon-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pulse Loading */
.pulse-loading {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Button Loading State */
.btn-loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Micro-interactions */
.micro-bounce {
  animation: microBounce 0.6s ease;
}

@keyframes microBounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.micro-shake {
  animation: microShake 0.5s ease;
}

@keyframes microShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

/* Success/Error States */
.state-success {
  border-color: var(--neon-green) !important;
  box-shadow: 0 0 20px var(--neon-green-glow) !important;
}

.state-error {
  border-color: var(--neon-pink) !important;
  box-shadow: 0 0 20px var(--neon-pink-glow) !important;
  animation: microShake 0.5s ease;
}

.state-warning {
  border-color: var(--electric-yellow) !important;
  box-shadow: 0 0 20px rgba(255, 255, 0, 0.3) !important;
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Enhanced Mobile Optimizations */
@media (max-width: 768px) {
  /* Reduce motion for better performance on mobile */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Mobile Navigation */
  .nav-container {
    padding: 0 var(--space-md);
  }

  .nav-links {
    gap: var(--space-sm);
  }

  .nav-link span {
    display: none;
  }

  .nav-link {
    min-height: 44px;
    padding: var(--space-sm);
    border-radius: 50%;
    width: 44px;
    justify-content: center;
  }

  .nav-link[data-tooltip]:hover::before {
    bottom: -35px;
    font-size: 0.7rem;
  }

  /* Mobile Hero Section */
  .hero-container {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    text-align: center;
  }

  .hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    max-width: 100%;
  }

  .hero-actions {
    flex-direction: column;
    gap: var(--space-md);
  }

  .cta-primary,
  .cta-secondary {
    width: 100%;
    justify-content: center;
    min-height: 52px;
    padding: var(--space-md) var(--space-xl);
  }

  .hero-features {
    justify-content: center;
  }

  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-md);
  }

  /* Mobile Cards */
  .trending-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .enhanced-card {
    margin: 0 var(--space-sm);
  }

  .card-header {
    padding: var(--space-md) var(--space-md) var(--space-sm);
  }

  .trending-content {
    padding: 0 var(--space-md) var(--space-md);
  }

  .trending-tags {
    gap: var(--space-xs);
  }

  .tag {
    font-size: 0.65rem;
    padding: 2px var(--space-xs);
  }

  .stat-group {
    gap: var(--space-md);
  }

  .card-actions {
    flex-direction: column;
    gap: var(--space-sm);
    padding: var(--space-md);
  }

  .analyze-btn,
  .create-btn {
    width: 100%;
    justify-content: center;
    min-height: 44px;
  }

  /* Mobile Modal */
  .modal-content {
    width: 95%;
    margin: var(--space-md);
  }

  .demo-features {
    gap: var(--space-md);
  }

  .feature-item {
    font-size: 0.8rem;
  }

  .feature-item i {
    font-size: 1.2rem;
  }

  /* Optimize glassmorphism for mobile */
  .glass-card {
    backdrop-filter: blur(10px) saturate(150%);
  }

  .glass-nav {
    backdrop-filter: blur(15px) saturate(150%);
  }

  /* Reduce shadow complexity on mobile */
  .shadow-depth-4,
  .shadow-depth-5 {
    box-shadow: var(--shadow-depth-2);
  }

  /* Optimize animations for 60fps */
  .gradient-orb {
    filter: blur(40px) saturate(120%);
  }

  .animated-bg {
    background-size: 200% 200%;
  }

  /* Optimize FAB for mobile */
  .fab {
    width: 56px;
    height: 56px;
    bottom: var(--space-lg);
    right: var(--space-lg);
  }

  /* Mobile Typography */
  .section-title {
    font-size: 1.8rem;
  }

  .section-subtitle {
    font-size: 1rem;
  }

  /* Mobile Spacing */
  .section-container {
    padding: 0 var(--space-md);
  }

  .hero-section {
    padding: var(--space-3xl) 0 var(--space-2xl);
  }

  .trending-section {
    padding: var(--space-2xl) 0;
  }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .brand-icon,
  .fab {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-glass: rgba(255, 255, 255, 0.06);
    --bg-glass-strong: rgba(255, 255, 255, 0.1);
    --glass-border: 1px solid rgba(255, 255, 255, 0.15);
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .animated-bg,
  .gradient-orb {
    animation: none;
  }

  .hero-badge i {
    animation: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --glass-border: 2px solid rgba(255, 255, 255, 0.5);
  }

  .glass-card {
    border-width: 2px;
  }
}

/* Enhanced Navigation Styles */
.nav-indicator {
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 20px;
  height: 3px;
  background: var(--gradient-accent);
  border-radius: var(--radius-sm);
  transition: transform var(--transition-normal);
}

.nav-link.active .nav-indicator {
  transform: translateX(-50%) scaleX(1);
  box-shadow: var(--shadow-neon-blue);
}

.nav-link[data-tooltip]:hover::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-glass-darker);
  color: var(--text-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 1000;
  backdrop-filter: var(--glass-backdrop);
  border: var(--glass-border);
}

/* Enhanced Hero Styles */
.badge-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--neon-pink-glow);
  border-radius: var(--radius-full);
  animation: badgePulse 2s ease-in-out infinite;
  opacity: 0.3;
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
}

.subtitle-highlight {
  color: var(--neon-blue);
  font-weight: 600;
}

.hero-features {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-xl);
  flex-wrap: wrap;
}

.feature-tag {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  background: var(--bg-glass);
  backdrop-filter: var(--glass-backdrop);
  border: var(--glass-border);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-secondary);
  transition: all var(--transition-normal);
}

.feature-tag:hover {
  color: var(--neon-pink);
  border-color: var(--neon-pink-soft);
  transform: translateY(-2px);
}

.feature-tag i {
  font-size: 0.9rem;
  color: var(--neon-pink);
}

/* Enhanced Button Styles */
.btn-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  border-radius: var(--radius-full);
  pointer-events: none;
}

.btn-particles::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: var(--neon-pink);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: particleFloat1 3s ease-in-out infinite;
}

.btn-particles::after {
  content: '';
  position: absolute;
  top: 30%;
  right: 20%;
  width: 3px;
  height: 3px;
  background: var(--neon-blue);
  border-radius: 50%;
  animation: particleFloat2 2.5s ease-in-out infinite;
}

@keyframes particleFloat1 {
  0%, 100% { transform: translate(-50%, -50%) translateY(0); opacity: 0; }
  50% { transform: translate(-50%, -50%) translateY(-10px); opacity: 1; }
}

@keyframes particleFloat2 {
  0%, 100% { transform: translateY(0); opacity: 0; }
  50% { transform: translateY(-8px); opacity: 1; }
}

.btn-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-full);
  overflow: hidden;
  pointer-events: none;
}

/* Enhanced Card Styles */
.enhanced-card {
  padding: 0;
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
}

.enhanced-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-accent);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
  transform-origin: left;
}

.enhanced-card:hover::before {
  transform: scaleX(1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-lg) var(--space-lg) var(--space-md);
}

.trending-rank {
  background: var(--gradient-secondary);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 0.9rem;
  box-shadow: var(--shadow-depth-2);
}

.badge-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-full);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.tiktok-badge:hover .badge-glow {
  background: var(--tiktok-red);
  opacity: 0.2;
  box-shadow: 0 0 20px var(--tiktok-red);
}

.instagram-badge:hover .badge-glow {
  background: #e6683c;
  opacity: 0.2;
  box-shadow: 0 0 20px #e6683c;
}

.trending-content {
  padding: 0 var(--space-lg) var(--space-lg);
}

.trending-tags {
  display: flex;
  gap: var(--space-xs);
  margin-bottom: var(--space-md);
  flex-wrap: wrap;
}

.tag {
  background: var(--bg-glass);
  color: var(--text-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.7rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-fast);
}

.tag:hover {
  color: var(--neon-pink);
  border-color: var(--neon-pink-soft);
}

.stat-group {
  display: flex;
  gap: var(--space-lg);
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
}

.stat-number {
  font-weight: 800;
  font-size: 1.1rem;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.growth-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: 0.8rem;
  font-weight: 700;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
}

.growth-indicator.positive {
  color: var(--neon-green);
  background: rgba(57, 255, 20, 0.1);
}

.card-actions {
  display: flex;
  gap: var(--space-sm);
  padding: var(--space-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.create-btn {
  background: var(--bg-glass);
  border: var(--glass-border);
  color: var(--text-secondary);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  flex: 1;
  justify-content: center;
}

.create-btn:hover {
  color: var(--neon-blue);
  border-color: var(--neon-blue-soft);
  background: var(--bg-glass-strong);
}

/* Print Styles */
@media print {
  .animated-bg,
  .glass-nav,
  .fab {
    display: none !important;
  }

  .glass-card {
    background: white !important;
    color: black !important;
    border: 1px solid #ccc !important;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 var(--space-md);
  }

  .nav-links {
    display: none;
  }

  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-2xl);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .cta-container {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .trending-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .section-container {
    padding: var(--space-2xl) var(--space-md);
  }
}

@media (max-width: 480px) {
  .hero-visual {
    height: 300px;
  }

  .floating-cards .trend-card {
    min-width: 150px;
    padding: var(--space-md);
  }

  .fab {
    bottom: var(--space-md);
    right: var(--space-md);
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
}
