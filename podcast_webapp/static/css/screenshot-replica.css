/* AiRiVerse AI - Screenshot Replica Design */

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #ffffff;
}

/* Navigation - Exact replica */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e5e5;
    z-index: 1000;
    height: 60px;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 24px;
    max-width: 1200px;
    margin: 0 auto;
}

.hamburger {
    display: flex;
    flex-direction: column;
    gap: 3px;
    cursor: pointer;
}

.hamburger span {
    width: 18px;
    height: 2px;
    background: #333;
    border-radius: 1px;
}

.brand-text {
    font-size: 18px;
    font-weight: 600;
    color: #000;
}

.nav-btn {
    background: none;
    border: 1px solid #e5e5e5;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    background: #f8f9fa;
    color: #000;
}

/* Hero Section - Exact layout */
.hero {
    padding: 100px 0 60px;
    background: #ffffff;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 100px;
    align-items: center;
}

.hero-title {
    font-size: 52px;
    font-weight: 700;
    line-height: 1.1;
    color: #000;
    margin-bottom: 20px;
    letter-spacing: -0.02em;
}

.hero-subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 28px;
    line-height: 1.5;
    max-width: 480px;
}

.hero-buttons {
    display: flex;
    gap: 16px;
}

.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #000;
    color: #fff;
}

.btn-primary:hover {
    background: #333;
    transform: translateY(-1px);
}

.btn-secondary {
    background: transparent;
    color: #666;
    border: 1px solid #e5e5e5;
}

.btn-secondary:hover {
    background: #f8f9fa;
    color: #000;
}

/* Image Grid - Exact replica */
.image-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 8px;
    height: 380px;
}

.image-item {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.image-item:hover {
    transform: translateY(-4px);
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-item.large {
    grid-row: span 2;
}

.image-item:nth-child(1) { grid-row: 1 / 3; }
.image-item:nth-child(2) { grid-column: 2; grid-row: 1; }
.image-item:nth-child(3) { grid-column: 3; grid-row: 1; }
.image-item:nth-child(4) { grid-column: 2; grid-row: 2; }
.image-item:nth-child(5) { grid-column: 3; grid-row: 2; }
.image-item:nth-child(6) { grid-column: 2 / 4; grid-row: 3; }

/* Hero Bottom */
.hero-bottom {
    max-width: 1400px;
    margin: 40px auto 0;
    padding: 0 40px;
    text-align: center;
}

.hero-bottom-text {
    font-size: 18px;
    font-weight: 600;
    color: #000;
    margin-bottom: 8px;
}

.hero-bottom-desc {
    font-size: 14px;
    color: #666;
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.4;
}

/* Dark Section - Exact replica */
.dark-section {
    background: #000;
    color: #fff;
    padding: 100px 0;
}

.dark-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 100px;
    align-items: center;
}

.dark-title {
    font-size: 40px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 24px;
}

.dark-subtitle {
    font-size: 18px;
    color: #ccc;
    margin-bottom: 32px;
}

.btn-white {
    background: #fff;
    color: #000;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-white:hover {
    background: #f0f0f0;
    transform: translateY(-1px);
}

/* Dashboard Mockup */
.dashboard-mockup {
    background: #1a1a1a;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.mockup-header {
    background: #2a2a2a;
    padding: 16px;
    border-bottom: 1px solid #333;
}

.mockup-dots {
    display: flex;
    gap: 8px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.dot.red { background: #ff5f56; }
.dot.yellow { background: #ffbd2e; }
.dot.green { background: #27ca3f; }

.mockup-content {
    display: flex;
    height: 300px;
}

.mockup-sidebar {
    width: 200px;
    background: #1a1a1a;
    padding: 24px;
    border-right: 1px solid #333;
}

.sidebar-item {
    padding: 12px 0;
    color: #999;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.sidebar-item.active {
    color: #fff;
    font-weight: 600;
}

.mockup-main {
    flex: 1;
    padding: 24px;
}

.chart-area {
    height: 180px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    margin-bottom: 24px;
}

.stats-area {
    display: flex;
    gap: 24px;
}

.stat-box {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: #999;
}

/* Stats Section */
.stats-section {
    padding: 100px 0;
    background: #fff;
}

.stats-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.stats-header {
    text-align: center;
    margin-bottom: 80px;
}

.stats-title {
    font-size: 40px;
    font-weight: 700;
    color: #000;
    margin-bottom: 16px;
}

.stats-subtitle {
    font-size: 18px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-item .stat-number {
    font-size: 48px;
    font-weight: 700;
    color: #000;
    margin-bottom: 8px;
}

.stat-item .stat-label {
    font-size: 16px;
    color: #666;
}

/* Gallery Section */
.gallery-section {
    padding: 100px 0;
    background: #f8f9fa;
}

.gallery-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.gallery-header {
    text-align: center;
    margin-bottom: 80px;
}

.gallery-title {
    font-size: 40px;
    font-weight: 700;
    color: #000;
    margin-bottom: 16px;
}

.gallery-subtitle {
    font-size: 18px;
    color: #666;
}

.gallery-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.gallery-item {
    background: #fff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-4px);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-item.featured img {
    height: 300px;
}

.gallery-info {
    padding: 24px;
}

.gallery-info h3 {
    font-size: 24px;
    font-weight: 600;
    color: #000;
    margin-bottom: 8px;
}

.gallery-info h4 {
    font-size: 18px;
    font-weight: 600;
    color: #000;
    margin-bottom: 8px;
}

.gallery-info p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.gallery-sidebar {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* CTA Section */
.cta-section {
    padding: 100px 0;
    background: #fff;
}

.cta-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 100px;
    align-items: center;
}

.cta-title {
    font-size: 40px;
    font-weight: 700;
    color: #000;
    margin-bottom: 24px;
}

.cta-subtitle {
    font-size: 18px;
    color: #666;
    margin-bottom: 32px;
}

.cta-buttons {
    display: flex;
    gap: 16px;
}

.cta-visual img {
    width: 100%;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Footer */
.footer {
    background: #000;
    color: #fff;
    padding: 80px 0 40px;
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 80px;
    margin-bottom: 40px;
}

.footer-brand h3 {
    font-size: 20px;
    margin-bottom: 16px;
}

.footer-brand p {
    color: #ccc;
    font-size: 14px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
}

.footer-column h4 {
    margin-bottom: 16px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
}

.footer-column a {
    display: block;
    color: #ccc;
    text-decoration: none;
    margin-bottom: 8px;
    font-size: 14px;
    transition: color 0.2s ease;
}

.footer-column a:hover {
    color: #fff;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 40px;
    border-top: 1px solid #333;
    color: #ccc;
    font-size: 14px;
}

.footer-social {
    display: flex;
    gap: 16px;
}

.footer-social a {
    color: #ccc;
    font-size: 18px;
    transition: color 0.2s ease;
}

.footer-social a:hover {
    color: #fff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container,
    .dark-container,
    .cta-container {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .hero-title {
        font-size: 36px;
    }
    
    .image-grid {
        grid-template-columns: 1fr 1fr;
        height: 300px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .footer-links {
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }
}
