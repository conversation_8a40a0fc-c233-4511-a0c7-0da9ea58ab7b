/* Enhanced AI Podcast Studio Styles */
:root {
    /* Enhanced Color Palette */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Enhanced Gradients */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    
    /* Glass Morphism */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #1f2937;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Enhanced Header */
.header {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: white;
}

.logo i {
    font-size: var(--font-size-2xl);
    color: var(--accent-color);
}

.version-badge {
    background: var(--gradient-success);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.tts-status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: white;
    font-size: var(--font-size-sm);
}

.status-dot {
    color: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Enhanced Main Content */
.main-content {
    padding: var(--spacing-2xl) 0;
}

/* Workflow Progress Bar */
.workflow-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-2xl);
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    box-shadow: var(--glass-shadow);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s ease;
    cursor: pointer;
}

.progress-step.active {
    color: white;
}

.progress-step.completed {
    color: var(--success-color);
}

.step-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.progress-step.active .step-icon {
    background: var(--gradient-primary);
    border-color: white;
    box-shadow: var(--shadow-lg);
}

.progress-step.completed .step-icon {
    background: var(--gradient-success);
    border-color: var(--success-color);
}

.step-info {
    text-align: center;
}

.step-title {
    font-weight: 600;
    font-size: var(--font-size-base);
}

.step-subtitle {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.progress-connector {
    width: 100px;
    height: 2px;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 var(--spacing-lg);
    position: relative;
}

.progress-connector::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.5s ease;
}

.progress-connector.completed::after {
    width: 100%;
}

/* Enhanced Step Panels */
.step-panel {
    display: none;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--glass-shadow);
    margin-bottom: var(--spacing-xl);
}

.step-panel.active {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.panel-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    color: white;
}

.panel-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.panel-header p {
    font-size: var(--font-size-lg);
    opacity: 0.8;
}

/* Enhanced Configuration Grid */
.config-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.config-section {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
}

.config-section h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Enhanced Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group label {
    display: block;
    color: white;
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: var(--font-size-base);
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.input-hint {
    font-size: var(--font-size-xs);
    color: rgba(255, 255, 255, 0.6);
    margin-top: var(--spacing-xs);
}

/* Enhanced TTS Provider Cards */
.tts-providers {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.tts-provider-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tts-provider-card:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.tts-provider-card.selected {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.tts-provider-card.unavailable {
    opacity: 0.5;
    cursor: not-allowed;
}

.provider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.provider-name {
    color: white;
    font-weight: 600;
    font-size: var(--font-size-lg);
}

.provider-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.provider-status.available {
    background: var(--gradient-success);
    color: white;
}

.provider-status.fallback {
    background: var(--gradient-secondary);
    color: white;
}

.provider-status.unavailable {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.provider-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
}

.provider-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.feature-badge {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.feature-badge.enhanced {
    background: var(--gradient-primary);
}

/* Enhanced Features Preview */
.features-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.features-preview h4 {
    color: white;
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.feature-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.feature-tag {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.feature-tag i {
    font-size: var(--font-size-sm);
}

/* Enhanced Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Panel Actions */
.panel-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-2xl);
}

/* Enhanced Script Editor */
.script-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    overflow: hidden;
}

.script-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.enhancement-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: white;
    font-size: var(--font-size-sm);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background: var(--gradient-primary);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.script-editor {
    min-height: 400px;
    padding: var(--spacing-xl);
    background: rgba(0, 0, 0, 0.2);
    color: white;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: var(--font-size-sm);
    line-height: 1.8;
    white-space: pre-wrap;
    overflow-y: auto;
}

.script-line {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
    cursor: text;
}

.script-line:hover {
    background: rgba(255, 255, 255, 0.05);
}

.script-line.selected {
    background: rgba(99, 102, 241, 0.2);
    border: 1px solid var(--primary-color);
}

.speaker-label {
    color: var(--accent-color);
    font-weight: 600;
    margin-right: var(--spacing-sm);
}

.emotion-tag-inline {
    background: rgba(139, 92, 246, 0.3);
    color: #c4b5fd;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    margin: 0 2px;
}

.audio-tag-inline {
    background: rgba(6, 182, 212, 0.3);
    color: #67e8f9;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    margin: 0 2px;
}

.pause-marker {
    background: rgba(245, 158, 11, 0.3);
    color: #fbbf24;
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    margin: 0 2px;
}

/* Enhancement Palette */
.enhancement-palette {
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.03);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.palette-section {
    margin-bottom: var(--spacing-lg);
}

.palette-section:last-child {
    margin-bottom: 0;
}

.palette-section h4 {
    color: white;
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.tag-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.emotion-tag,
.audio-tag {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.emotion-tag:hover {
    background: rgba(139, 92, 246, 0.3);
    border-color: #8b5cf6;
}

.audio-tag:hover {
    background: rgba(6, 182, 212, 0.3);
    border-color: #06b6d4;
}

/* Voice Selection */
.voice-selection-container {
    display: grid;
    gap: var(--spacing-2xl);
}

.speakers-grid {
    display: grid;
    gap: var(--spacing-xl);
}

.speaker-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
}

.speaker-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.speaker-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: white;
    font-weight: 600;
}

.speaker-info h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.speaker-role {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
}

.voice-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.voice-option {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.voice-option:hover {
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.voice-option.selected {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.voice-name {
    color: white;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.voice-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
}

.voice-preview {
    background: var(--gradient-secondary);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.voice-preview:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* TTS Dashboard */
.tts-dashboard {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
}

.tts-dashboard h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.status-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
}

.status-info {
    flex: 1;
}

.status-title {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.status-value {
    color: white;
    font-weight: 600;
    font-size: var(--font-size-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
    .config-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .workflow-progress {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .progress-connector {
        width: 2px;
        height: 50px;
        margin: 0;
    }

    .progress-connector::after {
        width: 100%;
        height: 0%;
        transition: height 0.5s ease;
    }

    .progress-connector.completed::after {
        height: 100%;
    }

    .panel-actions {
        flex-direction: column;
        align-items: center;
    }

    .script-toolbar {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }

    .voice-options {
        grid-template-columns: 1fr;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }
}

/* Results Panel */
.results-container {
    display: grid;
    gap: var(--spacing-2xl);
    text-align: center;
}

.audio-player-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
}

.audio-player {
    width: 100%;
    max-width: 600px;
    height: 60px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-lg);
    outline: none;
}

.audio-player::-webkit-media-controls-panel {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: var(--radius-lg);
}

.generation-summary {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    color: white;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.summary-item {
    text-align: center;
}

.summary-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: var(--spacing-xs);
}

.summary-label {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.7);
}

.generation-details {
    background: rgba(255, 255, 255, 0.03);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: left;
}

.generation-details h4 {
    color: white;
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
}

.detail-value {
    color: white;
    font-weight: 500;
}

.results-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.active {
    display: flex;
}

.loading-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-2xl);
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.loading-details {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-top: var(--spacing-lg);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: var(--radius-sm);
}

/* Animations */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-slide-in-right {
    animation: slideInFromRight 0.5s ease;
}

.animate-slide-in-left {
    animation: slideInFromLeft 0.5s ease;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.opacity-50 {
    opacity: 0.5;
}

.opacity-75 {
    opacity: 0.75;
}

.cursor-pointer {
    cursor: pointer;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Advanced Settings */
.advanced-settings-toggle {
    margin: var(--spacing-lg) 0;
    text-align: center;
}

.advanced-settings-toggle .btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    transition: all 0.3s ease;
}

.advanced-settings-toggle .btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.toggle-icon {
    margin-left: var(--spacing-sm);
    transition: transform 0.3s ease;
}

.advanced-settings-toggle.expanded .toggle-icon {
    transform: rotate(180deg);
}

.advanced-settings {
    display: none;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    margin-top: var(--spacing-lg);
    animation: slideDown 0.3s ease;
}

.advanced-settings.expanded {
    display: block;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    color: white;
    font-size: var(--font-size-sm);
}

.checkbox-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.music-style-select {
    flex: 1;
    min-width: 150px;
}

.music-style-select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.form-group textarea {
    width: 100%;
    min-height: 80px;
    padding: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: var(--font-size-sm);
    font-family: var(--font-family);
    resize: vertical;
    transition: all 0.3s ease;
}

.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background: rgba(255, 255, 255, 0.15);
}

.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Template Library */
.template-library {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.template-library h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.template-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.template-card:hover::before {
    left: 100%;
}

.template-card:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.template-card.selected {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.15);
}

.template-card .template-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
}

.template-info .template-title {
    color: white;
    font-weight: 600;
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.template-info .template-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
}

.template-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.template-tags .tag {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Collaboration Panel */
.collaboration-panel {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.collaboration-panel h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.collaboration-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: white;
    font-size: var(--font-size-sm);
}

.status-indicator .fa-circle.online {
    color: var(--success-color);
    animation: pulse 2s infinite;
}

.active-collaborators {
    margin-bottom: var(--spacing-lg);
}

.collaborator-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.collaborator-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.collaborator-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.collaborator-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
}

.collaborator-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-fallback {
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-sm);
}

.collaborator-info {
    flex: 1;
}

.collaborator-name {
    color: white;
    font-weight: 600;
    font-size: var(--font-size-sm);
    margin-bottom: 2px;
}

.collaborator-role {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-xs);
}

.collaborator-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
}

.collaborator-status.online {
    background: var(--success-color);
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.collaborator-status.away {
    background: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
}

.collaborator-status.offline {
    background: rgba(255, 255, 255, 0.3);
}

.collaboration-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-md);
}

.feature-item i {
    color: var(--accent-color);
}

/* Enhanced Audio Player */
.audio-player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.track-info h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.track-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
}

.audio-controls-extra {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-icon {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.btn-icon.active {
    background: var(--gradient-primary);
}

.speed-indicator {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: var(--font-size-xs);
    color: rgba(255, 255, 255, 0.7);
}

.waveform-container {
    position: relative;
    height: 80px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
}

.waveform-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

.progress-overlay {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(99, 102, 241, 0.3), rgba(139, 92, 246, 0.3));
    width: 0%;
    transition: width 0.1s ease;
    pointer-events: none;
}

.audio-timeline {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
}

.timeline-markers {
    flex: 1;
    height: 20px;
    position: relative;
    margin: 0 var(--spacing-md);
}

.timeline-marker {
    position: absolute;
    top: 0;
    width: 2px;
    height: 100%;
    background: var(--accent-color);
    cursor: pointer;
}

.timeline-marker.emotion {
    background: var(--secondary-color);
}

.timeline-marker.audio-event {
    background: var(--accent-color);
}

.timeline-marker::after {
    content: attr(data-label);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.timeline-marker:hover::after {
    opacity: 1;
}

/* Quality Metrics */
.quality-metrics {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.quality-metrics h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.metric-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.3s ease;
}

.metric-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
}

.metric-info {
    flex: 1;
}

.metric-value {
    color: white;
    font-weight: 700;
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xs);
}

.metric-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
}

/* Export Options */
.export-options {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.export-options h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.export-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.export-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: all 0.3s ease;
    cursor: pointer;
}

.export-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.export-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
}

.export-info {
    flex: 1;
}

.export-title {
    color: white;
    font-weight: 600;
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.export-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.export-size {
    color: var(--accent-color);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.export-btn {
    padding: var(--spacing-sm);
    min-width: auto;
}

/* Social Sharing */
.social-sharing {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.social-sharing h3 {
    color: white;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sharing-options {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: white;
}

.share-btn.twitter {
    background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
}

.share-btn.linkedin {
    background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
}

.share-btn.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #166fe5 100%);
}

.share-btn.copy-link {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.share-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.preview-card {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.preview-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
}

.preview-content {
    flex: 1;
}

.preview-title {
    color: white;
    font-weight: 600;
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.preview-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.preview-url {
    color: var(--accent-color);
    font-size: var(--font-size-xs);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .template-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .export-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
}

@media (max-width: 768px) {
    .collaboration-status {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .sharing-options {
        justify-content: center;
    }

    .preview-card {
        flex-direction: column;
        text-align: center;
    }

    .audio-player-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .template-grid {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .export-grid {
        grid-template-columns: 1fr;
    }

    .collaboration-features {
        grid-template-columns: 1fr;
    }
}

/* Selection Styling */
::selection {
    background: rgba(99, 102, 241, 0.3);
    color: white;
}

::-moz-selection {
    background: rgba(99, 102, 241, 0.3);
    color: white;
}
