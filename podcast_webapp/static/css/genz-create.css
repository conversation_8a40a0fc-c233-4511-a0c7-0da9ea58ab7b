/* TrendCast AI - Content Creation Page Styles */

/* Creation-specific variables */
:root {
  --creation-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --creation-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --creation-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --creation-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  
  --step-height: 100vh;
  --nav-height: 80px;
  --content-height: calc(100vh - var(--nav-height) - 80px);
}

/* Body adjustments for creation page */
.genz-create {
  overflow-x: hidden;
  background: var(--bg-primary);
}

/* Enhanced Navigation for Creation */
.glass-nav {
  height: var(--nav-height);
  padding: 0;
}

.nav-container {
  height: 100%;
  align-items: center;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* Creation Progress */
.creation-progress {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  padding: var(--space-sm) var(--space-lg);
}

.progress-step {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.progress-step.active {
  color: var(--text-primary);
}

.progress-step.completed {
  color: var(--neon-green);
}

.step-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.progress-step.active .step-icon {
  background: var(--creation-primary);
  color: white;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.progress-step.completed .step-icon {
  background: var(--creation-success);
  color: white;
}

.progress-connector {
  width: 40px;
  height: 2px;
  background: var(--bg-tertiary);
  position: relative;
  overflow: hidden;
}

.progress-connector::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--creation-success);
  width: 0%;
  transition: width 0.5s ease;
}

.progress-connector.completed::after {
  width: 100%;
}

/* Save Button */
.save-btn {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-md);
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.save-btn:hover {
  background: var(--creation-accent);
  border-color: transparent;
  color: white;
}

/* Main Content */
.create-main {
  min-height: var(--content-height);
  padding-top: var(--nav-height);
  position: relative;
}

/* Creation Steps */
.create-step {
  display: none;
  min-height: var(--content-height);
  padding: var(--space-2xl) 0;
}

.create-step.active {
  display: block;
  animation: stepFadeIn 0.5s ease;
}

@keyframes stepFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.step-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

/* Step Header */
.step-header {
  text-align: center;
  margin-bottom: var(--space-3xl);
}

.step-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  margin-bottom: var(--space-md);
  font-family: var(--font-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
}

.step-emoji {
  font-size: 1.2em;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.step-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Creation Grid */
.creation-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3xl);
  margin-bottom: var(--space-3xl);
}

/* Input Section */
.input-section {
  padding: var(--space-2xl);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--space-xl);
  color: var(--text-primary);
}

.section-title i {
  color: var(--neon-pink);
}

/* Input Tabs */
.input-tabs {
  display: flex;
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-xs);
  margin-bottom: var(--space-xl);
}

.tab-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: var(--creation-primary);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab-btn:hover:not(.active) {
  background: var(--bg-glass);
  color: var(--text-primary);
}

/* Tab Content */
.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: tabSlideIn 0.3s ease;
}

@keyframes tabSlideIn {
  from { opacity: 0; transform: translateX(-10px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Content Input */
.content-input {
  width: 100%;
  min-height: 200px;
  background: var(--bg-secondary);
  border: 2px solid var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: 1rem;
  line-height: 1.6;
  resize: vertical;
  transition: all 0.3s ease;
}

.content-input:focus {
  outline: none;
  border-color: var(--neon-pink);
  box-shadow: 0 0 20px rgba(255, 0, 110, 0.3);
}

.content-input::placeholder {
  color: var(--text-muted);
  line-height: 1.6;
}

/* Input Actions */
.input-actions {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-lg);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-full);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.action-btn.primary {
  background: var(--creation-secondary);
  color: white;
}

.action-btn.secondary {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid var(--bg-tertiary);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Upload Zone */
.upload-zone {
  border: 2px dashed var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-3xl);
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-zone:hover {
  border-color: var(--neon-pink);
  background: rgba(255, 0, 110, 0.05);
}

.upload-icon {
  font-size: 3rem;
  color: var(--neon-pink);
  margin-bottom: var(--space-lg);
}

.upload-zone h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--space-sm);
  color: var(--text-primary);
}

.upload-zone p {
  color: var(--text-secondary);
}

.file-input {
  display: none;
}

/* URL Input */
.url-input-group {
  display: flex;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.url-input {
  flex: 1;
  background: var(--bg-secondary);
  border: 2px solid var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-md);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.url-input:focus {
  outline: none;
  border-color: var(--neon-blue);
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
}

.fetch-btn {
  background: var(--creation-accent);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-lg);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.fetch-btn:hover {
  transform: scale(1.05);
}

/* URL Suggestions */
.url-suggestions h5 {
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
  font-size: 0.875rem;
  font-weight: 500;
}

.suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
}

.chip {
  background: var(--bg-tertiary);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-xs) var(--space-md);
  color: var(--text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chip:hover {
  background: var(--creation-primary);
  color: white;
}

/* Suggestions Section */
.suggestions-section {
  padding: var(--space-2xl);
}

.trending-suggestions {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

/* Suggestion Item */
.suggestion-item {
  background: var(--bg-secondary);
  border: 1px solid var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.suggestion-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transition: left 0.6s ease;
}

.suggestion-item:hover::before {
  left: 100%;
}

.suggestion-item:hover {
  border-color: var(--neon-pink);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-sm);
}

.suggestion-icon {
  font-size: 2rem;
}

.suggestion-meta h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.trend-score {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--neon-green);
}

.suggestion-desc {
  color: var(--text-secondary);
  margin-bottom: var(--space-md);
  line-height: 1.5;
}

.suggestion-stats {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.suggestion-stats span {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.suggestion-stats .fab {
  color: var(--neon-pink);
}

.use-suggestion-btn {
  background: var(--creation-accent);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-sm) var(--space-lg);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.use-suggestion-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

/* Refresh Button */
.refresh-suggestions {
  width: 100%;
  background: transparent;
  border: 2px dashed var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
}

.refresh-suggestions:hover {
  border-color: var(--neon-green);
  color: var(--neon-green);
  background: rgba(57, 255, 20, 0.05);
}

/* Quick Actions */
.quick-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.quick-action-btn:hover {
  background: var(--creation-primary);
  border-color: transparent;
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.quick-action-btn i {
  font-size: 1.5rem;
}

.quick-action-btn span {
  font-weight: 500;
  font-size: 0.875rem;
}

/* Step Navigation */
.step-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  background: var(--creation-primary);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--space-md) var(--space-xl);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:disabled {
  background: var(--bg-tertiary);
  color: var(--text-muted);
  cursor: not-allowed;
}

.nav-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.step-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-weight: 600;
  color: var(--text-primary);
}

.current-step {
  color: var(--neon-pink);
  font-size: 1.25rem;
}

.step-separator {
  color: var(--text-secondary);
}

/* Floating Elements */
.floating-elements {
  position: fixed;
  bottom: 100px;
  right: var(--space-xl);
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  z-index: 1000;
}

.help-btn,
.feedback-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.help-btn {
  background: var(--creation-accent);
}

.feedback-btn {
  background: var(--creation-secondary);
}

.help-btn:hover,
.feedback-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .creation-grid {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }
  
  .creation-progress {
    gap: var(--space-md);
    padding: var(--space-sm);
  }
  
  .progress-step span {
    display: none;
  }
  
  .progress-connector {
    width: 20px;
  }
}

@media (max-width: 768px) {
  .nav-center {
    display: none;
  }
  
  .step-container {
    padding: 0 var(--space-md);
  }
  
  .quick-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .quick-action-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .floating-elements {
    right: var(--space-md);
    bottom: 120px;
  }
  
  .step-navigation {
    padding: var(--space-md);
  }
  
  .nav-btn {
    padding: var(--space-sm) var(--space-lg);
  }
}

/* Research Step Styles */
.research-container {
  max-width: 1000px;
  margin: 0 auto;
}

.research-results {
  background: var(--bg-glass);
  border: var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  margin-bottom: var(--space-xl);
  backdrop-filter: var(--glass-backdrop);
}

.research-placeholder {
  text-align: center;
  padding: var(--space-3xl);
  color: var(--text-secondary);
}

.placeholder-icon {
  font-size: 4rem;
  color: var(--neon-blue);
  margin-bottom: var(--space-lg);
}

.research-overview h3 {
  color: var(--text-primary);
  font-size: 1.5rem;
  margin-bottom: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.research-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.metric {
  text-align: center;
  background: var(--bg-glass);
  border: var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.metric-value {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: var(--neon-pink);
  margin-bottom: var(--space-xs);
}

.metric-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

.research-insights h4,
.research-suggestions h4,
.research-hashtags h4 {
  color: var(--text-primary);
  font-size: 1.2rem;
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.research-insights ul {
  list-style: none;
  padding: 0;
}

.research-insights li {
  background: var(--bg-glass);
  border: var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  margin-bottom: var(--space-sm);
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--space-xl);
}

.research-insights li::before {
  content: '💡';
  position: absolute;
  left: var(--space-md);
  top: var(--space-md);
}

.suggestions-list {
  display: grid;
  gap: var(--space-sm);
}

.suggestion-item {
  background: var(--bg-glass);
  border: var(--glass-border);
  border-radius: var(--radius-md);
  padding: var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.suggestion-item:hover {
  border-color: var(--neon-blue-soft);
  transform: translateX(4px);
  color: var(--text-primary);
}

.suggestion-item i {
  color: var(--neon-blue);
  font-size: 0.9rem;
}

.hashtags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
}

.hashtag {
  background: var(--bg-glass);
  border: var(--glass-border);
  color: var(--neon-pink);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.hashtag:hover {
  background: var(--neon-pink-glow);
  color: white;
  transform: scale(1.05);
}

.research-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-full);
  font-weight: 700;
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  backdrop-filter: var(--glass-backdrop);
}

.action-btn.primary {
  background: var(--gradient-secondary);
  color: white;
  box-shadow: var(--shadow-depth-2);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-depth-4);
}

.action-btn.secondary {
  background: var(--bg-glass);
  border: var(--glass-border);
  color: var(--text-secondary);
}

.action-btn.secondary:hover {
  color: var(--text-primary);
  border-color: var(--glass-border-strong);
  background: var(--bg-glass-strong);
}

/* Script Step Styles */
.script-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: var(--space-xl);
}

.script-editor {
  background: var(--bg-glass);
  border: var(--glass-border);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  backdrop-filter: var(--glass-backdrop);
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-glass-strong);
  border-bottom: var(--glass-border);
}

.toolbar-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: var(--space-xs);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.toolbar-btn:hover {
  color: var(--text-primary);
  background: var(--bg-glass);
}

.toolbar-divider {
  width: 1px;
  height: 20px;
  background: var(--glass-border);
  margin: 0 var(--space-sm);
}

.script-textarea {
  width: 100%;
  min-height: 400px;
  padding: var(--space-lg);
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-family: var(--font-primary);
  font-size: 1rem;
  line-height: var(--line-height-relaxed);
  resize: vertical;
  outline: none;
}

.script-textarea::placeholder {
  color: var(--text-muted);
}

.script-sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.script-stats,
.script-suggestions {
  background: var(--bg-glass);
  border: var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--space-lg);
  backdrop-filter: var(--glass-backdrop);
}

.script-stats h4,
.script-suggestions h4 {
  color: var(--text-primary);
  font-size: 1.1rem;
  margin-bottom: var(--space-md);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.stat-value {
  color: var(--neon-pink);
  font-weight: 600;
}

.script-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  margin-top: var(--space-xl);
}

/* Audio Step Styles */
.audio-container {
  max-width: 1000px;
  margin: 0 auto;
  display: grid;
  gap: var(--space-xl);
}

.voice-selection,
.audio-settings,
.audio-preview {
  background: var(--bg-glass);
  border: var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--space-xl);
  backdrop-filter: var(--glass-backdrop);
}

.voice-selection h3,
.audio-settings h3,
.audio-preview h3 {
  color: var(--text-primary);
  font-size: 1.3rem;
  margin-bottom: var(--space-lg);
}

.voice-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
}

.voice-option {
  background: var(--bg-glass);
  border: var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.voice-option:hover {
  border-color: var(--neon-blue-soft);
  transform: translateY(-2px);
}

.voice-option.active {
  border-color: var(--neon-pink);
  box-shadow: var(--shadow-neon-pink);
}

.voice-avatar {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-glass-strong);
  border-radius: 50%;
}

.voice-info {
  flex: 1;
}

.voice-info h4 {
  color: var(--text-primary);
  font-size: 1.1rem;
  margin-bottom: var(--space-xs);
}

.voice-info p {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.voice-preview {
  background: var(--neon-blue);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-preview:hover {
  background: var(--neon-pink);
  transform: scale(1.1);
}

.setting-group {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.setting-group label {
  color: var(--text-primary);
  font-weight: 600;
  min-width: 80px;
}

.setting-slider {
  flex: 1;
  height: 6px;
  background: var(--bg-glass-strong);
  border-radius: var(--radius-full);
  outline: none;
  cursor: pointer;
}

.setting-value {
  color: var(--neon-pink);
  font-weight: 600;
  min-width: 50px;
  text-align: right;
}

.audio-player {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  background: var(--bg-glass-strong);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.play-btn {
  background: var(--neon-pink);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.play-btn:hover {
  background: var(--neon-blue);
  transform: scale(1.1);
}

.audio-progress {
  flex: 1;
  height: 6px;
  background: var(--bg-glass);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--gradient-accent);
  width: 0%;
  transition: width 0.3s ease;
}

.audio-time {
  color: var(--text-secondary);
  font-size: 0.9rem;
  min-width: 80px;
  text-align: right;
}

.audio-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  margin-top: var(--space-xl);
}

/* Mobile Responsiveness for New Steps */
@media (max-width: 768px) {
  .script-container {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .script-sidebar {
    order: -1;
  }

  .voice-grid {
    grid-template-columns: 1fr;
  }

  .audio-player {
    flex-direction: column;
    gap: var(--space-sm);
  }

  .audio-progress {
    width: 100%;
  }

  .research-actions,
  .script-actions,
  .audio-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}
