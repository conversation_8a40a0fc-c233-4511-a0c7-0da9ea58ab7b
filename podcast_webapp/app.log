/Users/<USER>/claude_home/magic_src/podcast_minimax/podcast_webapp/app.py:100: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
INFO:     Will watch for changes in these directories: ['/Users/<USER>/claude_home/magic_src/podcast_minimax/podcast_webapp']
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [66212] using StatReload
Unified TTS service created
Legacy TTS service initialized
INFO:     Started server process [66218]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Unified TTS service created
Legacy TTS service initialized
Unified TTS service created
Legacy TTS service initialized
✅ Unified TTS service initialized successfully
INFO:     127.0.0.1:54244 - "GET /?topic=%E8%AF%B7%E6%8E%A2%E8%AE%A8%E4%B8%AD%E7%BE%8E%E5%85%B3%E7%B3%BB%E5%92%8C%E6%97%A5%E6%9C%AC%E7%9A%84%E5%8F%91%E5%B1%95&script_style=conversational&num_speakers=2&duration_target=180&language=zh HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "POST /api/generate-script HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54248 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54254 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Search API response keys: dict_keys(['id', 'provider', 'model', 'object', 'created', 'choices', 'usage'])
Raw LLM response content: ```json
[
    {
        "title": "地震予知・予測について - 日本地震学会 (About Earthquake Prediction and Forecasting - The Seismological Society of Japan)",
        "snippet": "日本地震学会提供的权威科学观点指出，目前在科学上实现精准的短期地震预报（何时、何地、多大震级）仍然极其困难。该学会强调，现有的科学水平主要集中于长期预测，例如评估未来几十年内某个地区（如南海海槽）发生大地震的概率，但无法预测具体日期。学会致力于向公众普及地震科学知识，澄清社会上流传的非科学性“预言”，并推动以概率论为基础的防灾减灾规划。",
        "url": "https://www.zisin.jp/publications/qa/qa_01.html",
        "relevance_score": 0.95
    },
    {
        "title": "南海トラフ地震の「予言」はデマ。科学的根拠なし。気象庁が注意喚起 (Nanka...
Cleaned content: [
    {
        "title": "地震予知・予測について - 日本地震学会 (About Earthquake Prediction and Forecasting - The Seismological Society of Japan)",
        "snippet": "日本地震学会提供的权威科学观点指出，目前在科学上实现精准的短期地震预报（何时、何地、多大震级）仍...
Extracted JSON: [
    {
        "title": "地震予知・予測について - 日本地震学会 (About Earthquake Prediction and Forecasting - The Seismological Society of Japan)",
        "snippet": "日本地震学会提供的权威科学观点指出，目前在科学上实现精准的短期地震预报（何时、何地、多大震级）仍...
Successfully parsed 5 search results
INFO:     127.0.0.1:54259 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54259 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54261 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54261 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54261 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54265 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54265 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Report API response keys: dict_keys(['id', 'provider', 'model', 'object', 'created', 'choices', 'usage'])
Raw report content: ```json
{
    "summary": "The discussion around earthquake prediction in Japan, a nation defined by its seismic activity, navigates a complex landscape of ancient myth, modern science, and persistent public anxiety. Historically, the desire to foresee these devastating events is deeply ingrained, rooted in folklore such as the giant catfish, or 'Ōnamazu,' believed to cause tremors. This cultural preoccupation transitioned into a formal scientific endeavor with the 20th-century 'Earthquake Predic...
Cleaned report content: {
    "summary": "The discussion around earthquake prediction in Japan, a nation defined by its seismic activity, navigates a complex landscape of ancient myth, modern science, and persistent public a...
Extracted report JSON: {
    "summary": "The discussion around earthquake prediction in Japan, a nation defined by its seismic activity, navigates a complex landscape of ancient myth, modern science, and persistent public a...
Successfully parsed report with summary length: 3585
Key findings count: 8
INFO:     127.0.0.1:54265 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54271 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54273 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54274 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54277 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54277 - "GET /api/session/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54404 - "POST /api/approve-script HTTP/1.1" 200 OK
Synthesizing: David Kim - Welcome everyone.<#0.3#>Today we're tackling a sub...
Text length: 93 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54404 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54404 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54404 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54404 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54404 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (189186 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_000_David_Kim_20250706_084552.mp3 (94593 bytes)
Successfully synthesized line 1/66
INFO:     127.0.0.1:54404 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 2#>and public anxiety:<#0.2#>the idea of earthquak...
Text length: 76 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54404 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (190338 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_001_David_Kim_20250706_084600.mp3 (95169 bytes)
Successfully synthesized line 2/66
Synthesizing: David Kim - 3#>It's a topic that captivates people globally.<#...
Text length: 84 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (203024 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_002_David_Kim_20250706_084607.mp3 (101512 bytes)
Successfully synthesized line 3/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 2#>we're joined by expert seismologist,<#0.2#>Prof...
Text length: 90 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (193798 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_003_David_Kim_20250706_084614.mp3 (96899 bytes)
Successfully synthesized line 4/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 2#>Professor....
Text length: 13 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (39276 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_004_David_Kim_20250706_084619.mp3 (19638 bytes)
Successfully synthesized line 5/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - Thank you for having me,<#0.2#>David.<#0.3#>It’s a...
Text length: 82 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (207636 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_005_Prof._Jonathan_Hayes_20250706_084626.mp3 (103818 bytes)
Successfully synthesized line 6/66
Synthesizing: Prof. Jonathan Hayes - 3#>The desire to predict earthquakes in Japan isn'...
Text length: 60 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (178808 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_006_Prof._Jonathan_Hayes_20250706_084633.mp3 (89404 bytes)
Successfully synthesized line 7/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>it's deeply woven into the culture,<#0....
Text length: 42 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (137294 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_007_Prof._Jonathan_Hayes_20250706_084640.mp3 (68647 bytes)
Successfully synthesized line 8/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>from the ancient folklore of the giant catfish ...
Text length: 80 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (239924 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_008_Prof._Jonathan_Hayes_20250706_084647.mp3 (119962 bytes)
Successfully synthesized line 9/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>to more modern scientific ambitions....
Text length: 39 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (117690 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_009_Prof._Jonathan_Hayes_20250706_084652.mp3 (58845 bytes)
Successfully synthesized line 10/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - That's a fascinating starting point.<#0....
Text length: 40 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (88862 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_010_David_Kim_20250706_084658.mp3 (44431 bytes)
Successfully synthesized line 11/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 3#>So this cultural preoccupation evolved into a r...
Text length: 87 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (247996 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_011_David_Kim_20250706_084706.mp3 (123998 bytes)
Successfully synthesized line 12/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 3#>I understand there was a formal 'Earthquake Pre...
Text length: 80 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (181114 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_012_David_Kim_20250706_084712.mp3 (90557 bytes)
Successfully synthesized line 13/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - Exactly.<#0.3#>During the 20th century,<#0....
Text length: 43 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (158050 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_013_Prof._Jonathan_Hayes_20250706_084718.mp3 (79025 bytes)
Successfully synthesized line 14/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>there was immense hope and significant resource...
Text length: 81 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (201870 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_014_Prof._Jonathan_Hayes_20250706_084727.mp3 (100935 bytes)
Successfully synthesized line 15/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>But decades of research into precursors—things ...
Text length: 88 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (247996 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_015_Prof._Jonathan_Hayes_20250706_084733.mp3 (123998 bytes)
Successfully synthesized line 16/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - groundwater—have led to a stark conclusion from Ja...
Text length: 82 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (166122 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_016_Prof._Jonathan_Hayes_20250706_084740.mp3 (83061 bytes)
Successfully synthesized line 17/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - And what is that conclusion.<#0.3#><#0.<#0.3#>2#> ...
Text length: 82 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (226086 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_017_David_Kim_20250706_084747.mp3 (113043 bytes)
Successfully synthesized line 18/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 2#>predict an earthquake with the kind of precisio...
Text length: 100 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (266202 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_018_David_Kim_20250706_084753.mp3 (133101 bytes)
Successfully synthesized line 19/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 2#>and magnitude....
Text length: 17 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (55420 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_019_David_Kim_20250706_084758.mp3 (27710 bytes)
Successfully synthesized line 20/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - The answer,<#0.2#>unequivocally,<#0.2#>is no.<#0.3...
Text length: 60 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (222628 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_020_Prof._Jonathan_Hayes_20250706_084805.mp3 (111314 bytes)
Successfully synthesized line 21/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>1#> The Seismological Society of Japan and the ...
Text length: 96 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (309114 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_021_Prof._Jonathan_Hayes_20250706_084812.mp3 (154557 bytes)
Successfully synthesized line 22/66
Synthesizing: Prof. Jonathan Hayes - 2#>are very clear:<#0.2#>precise,<#0....
Text length: 37 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (143060 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_022_Prof._Jonathan_Hayes_20250706_084818.mp3 (71530 bytes)
Successfully synthesized line 23/66
Synthesizing: Prof. Jonathan Hayes - 2#>short-term earthquake prediction is not current...
Text length: 83 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (199564 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_023_Prof._Jonathan_Hayes_20250706_084824.mp3 (99782 bytes)
Successfully synthesized line 24/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>The systems involved are just too chaotic and c...
Text length: 81 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (222628 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_024_Prof._Jonathan_Hayes_20250706_084831.mp3 (111314 bytes)
Successfully synthesized line 25/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>specific forecast....
Text length: 21 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (88862 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_025_Prof._Jonathan_Hayes_20250706_084836.mp3 (44431 bytes)
Successfully synthesized line 26/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - That gap between public hope and scientific realit...
Text length: 98 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (193798 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_026_David_Kim_20250706_084843.mp3 (96899 bytes)
Successfully synthesized line 27/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - <#0.3#>I'm thinking of things like 'earthquake clo...
Text length: 78 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (143060 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_027_David_Kim_20250706_084851.mp3 (71530 bytes)
Successfully synthesized line 28/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - That’s a perfect example.<#0....
Text length: 29 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (81942 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_028_Prof._Jonathan_Hayes_20250706_084856.mp3 (40971 bytes)
Successfully synthesized line 29/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>The JMA has had to issue explicit statements de...
Text length: 99 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (254916 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_029_Prof._Jonathan_Hayes_20250706_084904.mp3 (127458 bytes)
Successfully synthesized line 30/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - <#0.3#>2#> They’ve explained that these formations...
Text length: 93 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (207636 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_030_Prof._Jonathan_Hayes_20250706_084911.mp3 (103818 bytes)
Successfully synthesized line 31/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>like altocumulus or cirrocumulus clouds,<#0....
Text length: 47 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (179960 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_031_Prof._Jonathan_Hayes_20250706_084918.mp3 (89980 bytes)
Successfully synthesized line 32/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>and there is absolutely no proven physical link...
Text length: 85 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (201870 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_032_Prof._Jonathan_Hayes_20250706_084924.mp3 (100935 bytes)
Successfully synthesized line 33/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - Speaking of that,<#0....
Text length: 21 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (61186 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_033_David_Kim_20250706_084930.mp3 (30593 bytes)
Successfully synthesized line 34/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 25#>I’ve seen viral posts online setting specific ...
Text length: 93 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (230700 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_034_David_Kim_20250706_084938.mp3 (115350 bytes)
Successfully synthesized line 35/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 3#>It causes a lot of panic.<#0.3#>How do authorit...
Text length: 66 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (133834 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_035_David_Kim_20250706_084945.mp3 (66917 bytes)
Successfully synthesized line 36/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - Yes,<#0.2#>and authorities actively combat this.<#...
Text length: 52 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (162664 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_036_Prof._Jonathan_Hayes_20250706_084952.mp3 (81332 bytes)
Successfully synthesized line 37/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>The JMA labels these specific 'prophecies' as b...
Text length: 69 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (208790 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_037_Prof._Jonathan_Hayes_20250706_084959.mp3 (104395 bytes)
Successfully synthesized line 38/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>The science is in long-term probabilistic forec...
Text length: 61 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (179960 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_038_Prof._Jonathan_Hayes_20250706_085006.mp3 (89980 bytes)
Successfully synthesized line 39/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>We can say there's a high probability—say,<#0....
Text length: 49 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (155744 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_039_Prof._Jonathan_Hayes_20250706_085013.mp3 (77872 bytes)
Successfully synthesized line 40/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>70-80%—of a major Nankai Trough quake in the ne...
Text length: 99 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (306522 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_040_Prof._Jonathan_Hayes_20250706_085020.mp3 (153261 bytes)
Successfully synthesized line 41/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>but it's not a prophecy for next Tuesday....
Text length: 44 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (115384 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_041_Prof._Jonathan_Hayes_20250706_085026.mp3 (57692 bytes)
Successfully synthesized line 42/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - That makes sense.<#0.3#>It seems there was a funda...
Text length: 93 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (179960 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_042_David_Kim_20250706_085033.mp3 (89980 bytes)
Successfully synthesized line 43/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 3#>I’ve read that the 1995 Kobe earthquake was a m...
Text length: 95 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (252610 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_043_David_Kim_20250706_085042.mp3 (126305 bytes)
Successfully synthesized line 44/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - Absolutely.<#0.3#>The catastrophic Hanshin-Awaji e...
Text length: 91 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (310266 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_044_Prof._Jonathan_Hayes_20250706_085049.mp3 (155133 bytes)
Successfully synthesized line 45/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>It tragically highlighted the limitations of a ...
Text length: 93 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (266446 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_045_Prof._Jonathan_Hayes_20250706_085056.mp3 (133223 bytes)
Successfully synthesized line 46/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>3#> As reported in publications like Toyo Keiza...
Text length: 63 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (216862 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_046_Prof._Jonathan_Hayes_20250706_085102.mp3 (108431 bytes)
Successfully synthesized line 47/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>Japan then pivoted its formidable resources awa...
Text length: 100 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (239924 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_047_Prof._Jonathan_Hayes_20250706_085110.mp3 (119962 bytes)
Successfully synthesized line 48/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - and resilience....
Text length: 15 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (56574 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_048_Prof._Jonathan_Hayes_20250706_085115.mp3 (28287 bytes)
Successfully synthesized line 49/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - So,<#0.2#>what does that resilience look like in p...
Text length: 62 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (153438 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_049_David_Kim_20250706_085122.mp3 (76719 bytes)
Successfully synthesized line 50/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 3#>This is where the famous Earthquake Early Warni...
Text length: 89 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (204176 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_050_David_Kim_20250706_085130.mp3 (102088 bytes)
Successfully synthesized line 51/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 3#>Many people think it's a prediction tool....
Text length: 44 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (98086 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_051_David_Kim_20250706_085136.mp3 (49043 bytes)
Successfully synthesized line 52/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - A crucial distinction.<#0.3#>The EEW system does n...
Text length: 88 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (262988 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_052_Prof._Jonathan_Hayes_20250706_085143.mp3 (131494 bytes)
Successfully synthesized line 53/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>2#> It detects the initial,<#0....
Text length: 34 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (140754 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_053_Prof._Jonathan_Hayes_20250706_085151.mp3 (70377 bytes)
Successfully synthesized line 54/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>faster-moving P-waves of a quake that has *alre...
Text length: 98 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (224934 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_054_Prof._Jonathan_Hayes_20250706_085158.mp3 (112467 bytes)
Successfully synthesized line 55/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - destructive S-waves arrive.<#0....
Text length: 31 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (99240 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_055_Prof._Jonathan_Hayes_20250706_085204.mp3 (49620 bytes)
Successfully synthesized line 56/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>It buys you precious seconds to a minute to tak...
Text length: 82 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (216862 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_056_Prof._Jonathan_Hayes_20250706_085210.mp3 (108431 bytes)
Successfully synthesized line 57/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 3#>It's a post-event warning,<#0.2#>not a pre-even...
Text length: 63 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (175348 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_057_Prof._Jonathan_Hayes_20250706_085217.mp3 (87674 bytes)
Successfully synthesized line 58/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - So to summarize,<#0.2#>the real progress in Japan ...
Text length: 93 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (192646 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_058_David_Kim_20250706_085224.mp3 (96323 bytes)
Successfully synthesized line 59/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 3#>It’s about building a society that can withstan...
Text length: 91 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (228392 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_059_David_Kim_20250706_085233.mp3 (114196 bytes)
Successfully synthesized line 60/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 2#>public drills,<#0.2#>and this incredible early ...
Text length: 73 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (171888 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_060_David_Kim_20250706_085241.mp3 (85944 bytes)
Successfully synthesized line 61/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: David Kim - 3#>It's a move from prophecy to practical prepared...
Text length: 55 chars, Voice ID: presenter_male
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (147672 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_061_David_Kim_20250706_085248.mp3 (73836 bytes)
Successfully synthesized line 62/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - That is the perfect summary.<#0.3#>The expert cons...
Text length: 69 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (186880 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_062_Prof._Jonathan_Hayes_20250706_085254.mp3 (93440 bytes)
Successfully synthesized line 63/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>investing in societal resilience is far more ef...
Text length: 98 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (222628 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_063_Prof._Jonathan_Hayes_20250706_085301.mp3 (111314 bytes)
Successfully synthesized line 64/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - goal of short-term prediction.<#0.3#>The future is...
Text length: 100 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (220320 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_064_Prof._Jonathan_Hayes_20250706_085309.mp3 (110160 bytes)
Successfully synthesized line 65/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
Synthesizing: Prof. Jonathan Hayes - 2#>educationally,<#0.2#>and technologically ready ...
Text length: 92 chars, Voice ID: audiobook_female_1
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
TTS Response status: 200
TTS Response data keys: ['data', 'extra_info', 'trace_id', 'base_resp']
Processing hex-encoded audio data (251456 chars)
Audio saved to: audio_output/podcasts/podcast_8f8decfe_line_065_Prof._Jonathan_Hayes_20250706_085316.mp3 (125728 bytes)
Successfully synthesized line 66/66
INFO:     127.0.0.1:54410 - "GET /api/status/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54410 - "GET /api/session/d3bc00ae-9124-401f-80ef-aaed92c5f61c HTTP/1.1" 200 OK
INFO:     127.0.0.1:54870 - "GET /audio/podcast_8f8decfe_complete.mp3 HTTP/1.1" 206 Partial Content
INFO:     127.0.0.1:54870 - "GET /audio/podcast_8f8decfe_complete.mp3 HTTP/1.1" 206 Partial Content
