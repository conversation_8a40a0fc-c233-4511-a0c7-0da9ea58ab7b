#!/usr/bin/env python3
"""
Enhanced AI Podcast Studio - Feature Demonstration Script

This script demonstrates the key features of the enhanced UI and API endpoints.
"""

import requests
import json
import time
from typing import Dict, Any

class EnhancedPodcastDemo:
    def __init__(self, base_url: str = "http://127.0.0.1:8002"):
        self.base_url = base_url
        
    def test_tts_providers(self) -> Dict[str, Any]:
        """Test TTS providers endpoint"""
        print("🔍 Testing TTS Providers API...")
        
        try:
            response = requests.get(f"{self.base_url}/api/tts/providers")
            data = response.json()
            
            if data.get("success"):
                print("✅ TTS Providers API working!")
                print(f"   Found {len(data['providers'])} providers:")
                
                for provider in data['providers']:
                    status_icon = "🟢" if provider['available'] else "🔴"
                    enhanced_features = len([f for f in provider['features'] if f.get('enhanced')])
                    print(f"   {status_icon} {provider['name']} - {enhanced_features} enhanced features")
                
                return data
            else:
                print(f"❌ TTS Providers API failed: {data.get('error')}")
                return {}
                
        except Exception as e:
            print(f"❌ Error testing TTS providers: {e}")
            return {}
    
    def test_voices_api(self, provider: str = "enhanced_elevenlabs") -> Dict[str, Any]:
        """Test voices API"""
        print(f"\n🎤 Testing Voices API for {provider}...")
        
        try:
            response = requests.get(f"{self.base_url}/api/tts/voices?provider={provider}")
            data = response.json()
            
            if data.get("success"):
                print("✅ Voices API working!")
                print(f"   Found {len(data['voices'])} voices for {provider}:")
                
                for voice in data['voices']:
                    features = ", ".join(voice.get('supported_features', []))
                    print(f"   🎭 {voice['name']} ({voice['gender']}) - {features}")
                
                return data
            else:
                print(f"❌ Voices API failed: {data.get('error')}")
                return {}
                
        except Exception as e:
            print(f"❌ Error testing voices: {e}")
            return {}
    
    def test_voice_preview(self, voice_id: str = "voice_001") -> Dict[str, Any]:
        """Test voice preview API"""
        print(f"\n🔊 Testing Voice Preview for {voice_id}...")
        
        try:
            payload = {
                "voice_id": voice_id,
                "provider": "enhanced_elevenlabs",
                "text": "Hello! This is a demonstration of the enhanced AI podcast studio. [excited] Isn't this amazing? {applause}",
                "enhanced_features": {
                    "emotion_tags": True,
                    "audio_events": True
                }
            }
            
            response = requests.post(f"{self.base_url}/api/tts/preview", json=payload)
            data = response.json()
            
            if data.get("success"):
                print("✅ Voice Preview API working!")
                print(f"   Preview URL: {data.get('audio_url')}")
                print(f"   Duration: {data.get('duration')}s")
                print(f"   Model: {data.get('model_used')}")
                return data
            else:
                print(f"❌ Voice Preview API failed: {data.get('error')}")
                return {}
                
        except Exception as e:
            print(f"❌ Error testing voice preview: {e}")
            return {}
    
    def demonstrate_enhanced_features(self):
        """Demonstrate the enhanced features"""
        print("🚀 Enhanced AI Podcast Studio - Feature Demonstration")
        print("=" * 60)
        
        # Test TTS Providers
        providers_data = self.test_tts_providers()
        
        # Test Voices API
        voices_data = self.test_voices_api()
        
        # Test Voice Preview
        preview_data = self.test_voice_preview()
        
        # Summary
        print("\n📊 Feature Summary:")
        print("=" * 30)
        
        if providers_data.get("success"):
            enhanced_providers = [p for p in providers_data['providers'] 
                                if any(f.get('enhanced') for f in p['features'])]
            print(f"✅ Enhanced TTS Providers: {len(enhanced_providers)}")
        
        if voices_data.get("success"):
            enhanced_voices = [v for v in voices_data['voices'] 
                             if 'emotion_tags' in v.get('supported_features', [])]
            print(f"✅ Enhanced Voices: {len(enhanced_voices)}")
        
        if preview_data.get("success"):
            print(f"✅ Voice Preview: Working")
        
        print("\n🎯 Enhanced Features Available:")
        print("   • Emotion Tags: [excited], [calm], [whispering], [laughing]")
        print("   • Audio Events: {applause}, {music}, {footsteps}, {phone}")
        print("   • Text-to-Dialogue: Advanced conversation synthesis")
        print("   • Intelligent Fallback: v3 → v2 model switching")
        print("   • Real-time Status: Provider health monitoring")
        print("   • Quality Metrics: AI naturalness scoring")
        
        print(f"\n🌐 Access the Enhanced UI at: {self.base_url}/enhanced")
        print("\n🎉 Enhanced AI Podcast Studio is ready!")

def main():
    """Main demonstration function"""
    demo = EnhancedPodcastDemo()
    demo.demonstrate_enhanced_features()

if __name__ == "__main__":
    main()
