# Commercial AI Podcast Studio - Design System Specification

## 🎨 Professional Color Palette

### Primary Colors (Trust & Innovation)
```css
--primary-50: #f0f9ff;    /* Lightest blue - backgrounds */
--primary-100: #e0f2fe;   /* Light blue - hover states */
--primary-200: #bae6fd;   /* Medium light - borders */
--primary-300: #7dd3fc;   /* Medium - secondary elements */
--primary-400: #38bdf8;   /* Medium dark - interactive elements */
--primary-500: #0ea5e9;   /* Primary brand color */
--primary-600: #0284c7;   /* Primary hover */
--primary-700: #0369a1;   /* Primary active */
--primary-800: #075985;   /* Dark primary */
--primary-900: #0c4a6e;   /* Darkest primary */
```

### Neutral Colors (Professional Foundation)
```css
--neutral-50: #fafafa;    /* Pure white alternative */
--neutral-100: #f5f5f5;   /* Light backgrounds */
--neutral-200: #e5e5e5;   /* Borders, dividers */
--neutral-300: #d4d4d4;   /* Disabled states */
--neutral-400: #a3a3a3;   /* Placeholder text */
--neutral-500: #737373;   /* Secondary text */
--neutral-600: #525252;   /* Primary text */
--neutral-700: #404040;   /* Headings */
--neutral-800: #262626;   /* Dark text */
--neutral-900: #171717;   /* Darkest text */
```

### Semantic Colors
```css
--success-50: #f0fdf4;    /* Success backgrounds */
--success-500: #22c55e;   /* Success primary */
--success-600: #16a34a;   /* Success hover */

--warning-50: #fffbeb;    /* Warning backgrounds */
--warning-500: #f59e0b;   /* Warning primary */
--warning-600: #d97706;   /* Warning hover */

--error-50: #fef2f2;      /* Error backgrounds */
--error-500: #ef4444;     /* Error primary */
--error-600: #dc2626;     /* Error hover */

--info-50: #eff6ff;       /* Info backgrounds */
--info-500: #3b82f6;      /* Info primary */
--info-600: #2563eb;      /* Info hover */
```

## 📐 Layout System

### Container Widths
```css
--container-sm: 640px;    /* Small screens */
--container-md: 768px;    /* Medium screens */
--container-lg: 1024px;   /* Large screens */
--container-xl: 1280px;   /* Extra large screens */
--container-2xl: 1536px;  /* 2X large screens */
```

### Spacing Scale
```css
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
--space-20: 5rem;     /* 80px */
--space-24: 6rem;     /* 96px */
```

## 🔤 Typography System

### Font Stack
```css
--font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
--font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
```

### Font Sizes
```css
--text-xs: 0.75rem;     /* 12px */
--text-sm: 0.875rem;    /* 14px */
--text-base: 1rem;      /* 16px */
--text-lg: 1.125rem;    /* 18px */
--text-xl: 1.25rem;     /* 20px */
--text-2xl: 1.5rem;     /* 24px */
--text-3xl: 1.875rem;   /* 30px */
--text-4xl: 2.25rem;    /* 36px */
--text-5xl: 3rem;       /* 48px */
```

### Font Weights
```css
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

## 🏗️ Multi-Page Architecture

### 1. Dashboard Page (`/dashboard`)
**Purpose**: Central hub for project management and system overview
**Components**:
- Header with navigation and user profile
- Project overview cards with status indicators
- System health dashboard
- Usage analytics widgets
- Quick action buttons
- Recent activity feed

### 2. Project Creation Page (`/projects/new`)
**Purpose**: Comprehensive project setup and configuration
**Components**:
- Step-by-step wizard interface
- Template selection gallery
- Advanced configuration panels
- TTS provider selection with feature comparison
- Project settings and metadata

### 3. Script Editor Page (`/projects/{id}/script`)
**Purpose**: Advanced script editing with enhancement tools
**Components**:
- Full-screen editor with syntax highlighting
- Enhancement palette (emotion tags, audio events)
- Real-time collaboration indicators
- Version history sidebar
- AI assistance panel

### 4. Voice & Audio Page (`/projects/{id}/audio`)
**Purpose**: Voice selection and audio generation
**Components**:
- Voice library with preview capabilities
- Speaker assignment interface
- Audio generation controls
- Real-time progress monitoring
- Quality metrics dashboard

### 5. Results & Export Page (`/projects/{id}/results`)
**Purpose**: Final output management and export options
**Components**:
- Audio player with waveform visualization
- Export format selection
- Sharing and collaboration tools
- Analytics and performance metrics
- Download and distribution options

## 🧭 Navigation Patterns

### Primary Navigation
- **Logo/Brand**: Top-left, always visible
- **Main Menu**: Horizontal navigation bar
  - Dashboard
  - Projects
  - Library (templates, voices)
  - Analytics
  - Settings
- **User Menu**: Top-right dropdown
  - Profile settings
  - Billing & usage
  - Help & support
  - Sign out

### Secondary Navigation
- **Breadcrumbs**: Show current location in hierarchy
- **Project Tabs**: When inside a project
  - Overview
  - Script
  - Audio
  - Results
- **Contextual Actions**: Page-specific action buttons

### Mobile Navigation
- **Hamburger Menu**: Collapsible sidebar
- **Bottom Tab Bar**: Quick access to main sections
- **Swipe Gestures**: Between project phases

## 📊 Dashboard Layout Specification

### Header Section (Fixed)
```
[Logo] [Dashboard] [Projects] [Library] [Analytics] [Settings]     [Search] [Notifications] [User Avatar ▼]
```

### Main Content Grid
```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ Welcome Back, [User Name]                                    [+ New Project]    │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │   Active        │ │   Completed     │ │   Total         │ │   This Month    │ │
│ │   Projects      │ │   This Week     │ │   Audio Hours   │ │   Usage         │ │
│ │      12         │ │       8         │ │      24.5       │ │     89%         │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────┤
│ ┌───────────────────────────────────┐ ┌─────────────────────────────────────────┐ │
│ │        Recent Projects            │ │           System Status                 │ │
│ │ ┌─────────────────────────────────┐ │ │ ┌─────────────────────────────────────┐ │ │
│ │ │ 🎙️ AI in Healthcare            │ │ │ │ 🟢 Enhanced ElevenLabs    Online   │ │ │
│ │ │ Script Complete • 2 hours ago   │ │ │ │ 🟢 MiniMax TTS           Online   │ │ │
│ │ └─────────────────────────────────┘ │ │ │ 🟢 OpenRouter AI         Online   │ │ │
│ │ ┌─────────────────────────────────┐ │ │ │ 🟡 Voice Library         Syncing │ │ │
│ │ │ 📊 Market Analysis              │ │ │ └─────────────────────────────────────┘ │ │
│ │ │ Audio Generated • 5 hours ago   │ │ │                                         │ │
│ │ └─────────────────────────────────┘ │ │ ┌─────────────────────────────────────┐ │ │
│ └───────────────────────────────────┘ │ │           Quick Actions             │ │ │
├─────────────────────────────────────────┤ │ [📝 New Project] [📚 Browse Library] │ │ │
│ ┌─────────────────────────────────────┐ │ │ [⚙️ Settings]    [📊 View Analytics] │ │ │
│ │         Usage Analytics             │ │ └─────────────────────────────────────┘ │ │
│ │ [Monthly Usage Chart]               │ └─────────────────────────────────────────┘ │
│ │ [TTS Provider Distribution]         │                                             │
│ │ [Quality Metrics Trends]            │                                             │
│ └─────────────────────────────────────┘                                             │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 🎯 Component Design Principles

### Cards & Containers
- **Elevation**: Subtle shadows for depth
- **Borders**: 1px solid neutral-200
- **Radius**: 8px for cards, 6px for buttons
- **Padding**: Consistent 16px/24px internal spacing

### Interactive Elements
- **Hover States**: Subtle color shifts and elevation
- **Focus States**: Clear outline indicators
- **Loading States**: Skeleton screens and progress indicators
- **Disabled States**: Reduced opacity and cursor changes

### Data Visualization
- **Charts**: Clean, minimal design with primary color scheme
- **Progress Bars**: Rounded corners, smooth animations
- **Status Indicators**: Color-coded with clear labels
- **Metrics**: Large numbers with descriptive labels

This design system provides the foundation for a professional, commercial-grade product that conveys trust, reliability, and innovation while maintaining excellent usability.
