#!/usr/bin/env python3
"""
Debug Audio Generation Issues
Simple test to identify the exact problem
"""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.services.podcast_audio_service import PodcastAudioService
from src.services.tts_factory import TTSServiceManager
from src.models.podcast_models import PodcastScript, DialogueLine, PodcastRole, TTSProvider


async def test_v3_provider_directly():
    """Test V3 provider directly"""
    print("🔧 Testing V3 Provider Directly")
    print("=" * 50)
    
    try:
        # Create TTS manager
        config = {
            "primary_provider": "elevenlabs_v3",
            "fallback_providers": ["elevenlabs", "minimax"]
        }
        
        manager = TTSServiceManager(config)
        await manager.initialize()
        
        print(f"✅ TTS Manager initialized")
        print(f"   Primary provider: {manager.primary_provider}")
        print(f"   Available providers: {list(manager.providers.keys())}")
        
        # Check if V3 provider is available
        if TTSProvider.ELEVENLABS_V3 in manager.providers:
            v3_provider = manager.providers[TTSProvider.ELEVENLABS_V3]
            print(f"✅ V3 provider found: {type(v3_provider).__name__}")
            
            # Test V3 provider initialization
            if hasattr(v3_provider, '_initialized'):
                print(f"   V3 provider initialized: {v3_provider._initialized}")
            
            # Test V3 availability
            if hasattr(v3_provider, 'is_v3_available'):
                print(f"   V3 features available: {v3_provider.is_v3_available()}")
            
            # Test voice availability
            voices = await v3_provider.get_available_voices("en")
            print(f"   Available voices: {len(voices)}")
            
            return True
        else:
            print(f"❌ V3 provider not found in manager")
            return False
            
    except Exception as e:
        print(f"❌ V3 provider test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_unified_service():
    """Test unified TTS service"""
    print(f"\n🎵 Testing Unified TTS Service")
    print("=" * 50)
    
    try:
        # Create unified service
        service = PodcastAudioService()
        await service.initialize()
        
        print(f"✅ Unified service initialized")
        
        # Test simple script
        script = PodcastScript(
            title="Test Script",
            description="Test description",
            dialogue=[
                DialogueLine(
                    role=PodcastRole.HOST,
                    speaker_name="Test Host",
                    text="Hello, this is a test.",
                    voice_id="21m00Tcm4TlvDq8ikWAM",
                    emotion="neutral"
                )
            ],
            language="en"
        )
        
        print(f"Testing audio synthesis with V3...")
        
        # Test synthesis
        result = await service.synthesize_podcast_audio(
            script=script,
            preferred_provider="elevenlabs_v3"
        )
        
        print(f"✅ Audio synthesis completed")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Provider used: {result.get('provider', 'unknown')}")
        print(f"   Fallback used: {result.get('fallback_used', False)}")
        
        if not result.get('success', False):
            print(f"   Error: {result.get('error', 'Unknown error')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Unified service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_provider_config():
    """Test provider configuration"""
    print(f"\n⚙️ Testing Provider Configuration")
    print("=" * 50)
    
    try:
        from src.services.tts_factory import TTSServiceFactory
        
        # Test V3 provider creation
        factory = TTSServiceFactory()
        
        print(f"Testing V3 provider creation...")
        v3_provider = factory.create_provider(TTSProvider.ELEVENLABS_V3)
        
        print(f"✅ V3 provider created: {type(v3_provider).__name__}")
        
        # Test initialization
        init_result = await v3_provider.initialize()
        print(f"   Initialization result: {init_result}")
        
        # Test configuration
        if hasattr(v3_provider, 'api_key'):
            print(f"   API key available: {bool(v3_provider.api_key)}")
        
        if hasattr(v3_provider, 'model_id'):
            print(f"   Model ID: {v3_provider.model_id}")
        
        if hasattr(v3_provider, 'max_text_length'):
            print(f"   Max text length: {v3_provider.max_text_length}")
        
        return True
        
    except Exception as e:
        print(f"❌ Provider configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run debug tests"""
    print("🐛 Audio Generation Debug Tests")
    print("Identifying the exact cause of audio generation issues")
    print("=" * 70)
    
    tests = [
        ("Provider Configuration", test_provider_config),
        ("V3 Provider Direct Test", test_v3_provider_directly),
        ("Unified Service Test", test_unified_service)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Final summary
    print(f"\n" + "=" * 70)
    print("🏁 DEBUG TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == 0:
        print("\n🚨 All tests failed - there's a fundamental issue with V3 setup")
    elif passed < total:
        print("\n⚠️ Some tests failed - partial V3 functionality")
    else:
        print("\n🎉 All tests passed - V3 should be working")
    
    return passed > 0

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
